@echo off
title EBO Redundancy Manager - Automatic Installer
color 0A

echo.
echo ===============================================================================
echo                    EBO REDUNDANCY MANAGER - AUTOMATIC INSTALLER
echo ===============================================================================
echo.
echo                Professional EcoStruxure Building Operation Redundancy System
echo                         For Dell PowerEdge R750xa Servers
echo.
echo                            FULLY AUTOMATIC INSTALLATION
echo.
echo ===============================================================================
echo.

REM Request administrator privileges
net session >nul 2>&1
if errorlevel 1 (
    echo This installer requires administrator privileges.
    echo Please run as Administrator.
    pause
    exit /b 1
)

echo ✅ Running with administrator privileges
echo.

REM Set installation directory
set INSTALL_DIR=%ProgramFiles%\EBO_Redundancy_Manager
echo Installation Directory: %INSTALL_DIR%
echo.

echo [1/8] Checking Python installation...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Installing Python automatically...
    echo.
    
    REM Create temp directory
    if not exist "%TEMP%\ebo_installer" mkdir "%TEMP%\ebo_installer"
    cd /d "%TEMP%\ebo_installer"
    
    echo Downloading Python 3.11.7...
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile 'python_installer.exe'}"
    
    if exist python_installer.exe (
        echo Installing Python 3.11.7 (this may take a few minutes)...
        python_installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0 Include_pip=1
        
        REM Wait for installation to complete
        echo Waiting for Python installation to complete...
        timeout /t 60 /nobreak >nul
        
        REM Refresh environment variables
        for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH') do set "PATH=%%b"
        
        echo ✅ Python installation completed
    ) else (
        echo ❌ Failed to download Python installer
        echo Please check your internet connection and try again.
        pause
        exit /b 1
    )
) else (
    python --version
    echo ✅ Python is already installed
)

echo.
echo [2/8] Creating installation directory...

REM Create installation directory
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%INSTALL_DIR%\logs" mkdir "%INSTALL_DIR%\logs"
if not exist "%INSTALL_DIR%\config" mkdir "%INSTALL_DIR%\config"
if not exist "%INSTALL_DIR%\data" mkdir "%INSTALL_DIR%\data"
if not exist "%INSTALL_DIR%\backup" mkdir "%INSTALL_DIR%\backup"
if not exist "%INSTALL_DIR%\templates" mkdir "%INSTALL_DIR%\templates"
if not exist "%INSTALL_DIR%\static" mkdir "%INSTALL_DIR%\static"

echo ✅ Installation directory created

echo.
echo [3/8] Installing Python packages...

REM Create requirements file
echo flask>=2.0.0> "%INSTALL_DIR%\requirements.txt"
echo PyYAML>=6.0>> "%INSTALL_DIR%\requirements.txt"
echo psutil>=5.8.0>> "%INSTALL_DIR%\requirements.txt"
echo requests>=2.25.0>> "%INSTALL_DIR%\requirements.txt"
echo Jinja2>=3.0.0>> "%INSTALL_DIR%\requirements.txt"
echo Werkzeug>=2.0.0>> "%INSTALL_DIR%\requirements.txt"
echo MarkupSafe>=2.0.0>> "%INSTALL_DIR%\requirements.txt"

echo Installing required packages...
python -m pip install --upgrade pip --quiet
python -m pip install -r "%INSTALL_DIR%\requirements.txt" --quiet

echo ✅ Python packages installed

echo.
echo [4/8] Creating application files...

REM Create main application launcher
echo Creating main application launcher...
(
echo @echo off
echo title EBO Redundancy Manager
echo cd /d "%INSTALL_DIR%"
echo python EBO_Redundancy_App.py
echo pause
) > "%INSTALL_DIR%\Start_EBO_Redundancy.bat"

REM Create web interface launcher
(
echo @echo off
echo title EBO Redundancy Web Interface
echo cd /d "%INSTALL_DIR%"
echo python redundancy_web_ui.py
echo pause
) > "%INSTALL_DIR%\Start_Web_Interface.bat"

REM Create service installer
(
echo @echo off
echo title Install EBO Redundancy Service
echo cd /d "%INSTALL_DIR%"
echo echo Installing EBO Redundancy as Windows Service...
echo sc create "EBO_Redundancy_Service" binPath= "python.exe \"%INSTALL_DIR%\redundancy_web_ui.py\"" start= auto
echo sc description "EBO_Redundancy_Service" "EcoStruxure Building Operation Redundancy Management Service"
echo echo Service installed successfully!
echo pause
) > "%INSTALL_DIR%\Install_Service.bat"

echo ✅ Application launchers created

echo.
echo [5/8] Copying application files...

REM Copy application files from current directory to installation directory
if exist "EBO_Redundancy_App.py" copy "EBO_Redundancy_App.py" "%INSTALL_DIR%\" >nul
if exist "redundancy_web_ui.py" copy "redundancy_web_ui.py" "%INSTALL_DIR%\" >nul
if exist "ebo_redundancy_manager.py" copy "ebo_redundancy_manager.py" "%INSTALL_DIR%\" >nul
if exist "heartbeat_manager.py" copy "heartbeat_manager.py" "%INSTALL_DIR%\" >nul
if exist "database_cluster_manager.py" copy "database_cluster_manager.py" "%INSTALL_DIR%\" >nul
if exist "storage_redundancy_manager.py" copy "storage_redundancy_manager.py" "%INSTALL_DIR%\" >nul
if exist "redundancy_manager.py" copy "redundancy_manager.py" "%INSTALL_DIR%\" >nul

REM Copy configuration files
if exist "*.yaml" copy "*.yaml" "%INSTALL_DIR%\" >nul
if exist "*.yml" copy "*.yml" "%INSTALL_DIR%\" >nul

REM Copy templates
if exist "templates\*" xcopy "templates\*" "%INSTALL_DIR%\templates\" /E /I /Q >nul

REM Copy static files
if exist "static\*" xcopy "static\*" "%INSTALL_DIR%\static\" /E /I /Q >nul

echo ✅ Application files copied

echo.
echo [6/8] Creating desktop shortcuts...

REM Create desktop shortcut
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\EBO Redundancy Manager.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Start_EBO_Redundancy.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'EBO Redundancy Manager'; $Shortcut.Save()}"

REM Create start menu shortcuts
if not exist "%ProgramData%\Microsoft\Windows\Start Menu\Programs\EBO Redundancy Manager" mkdir "%ProgramData%\Microsoft\Windows\Start Menu\Programs\EBO Redundancy Manager"

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%ProgramData%\Microsoft\Windows\Start Menu\Programs\EBO Redundancy Manager\EBO Redundancy Manager.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Start_EBO_Redundancy.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'EBO Redundancy Manager'; $Shortcut.Save()}"

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%ProgramData%\Microsoft\Windows\Start Menu\Programs\EBO Redundancy Manager\Web Interface.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Start_Web_Interface.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'EBO Redundancy Web Interface'; $Shortcut.Save()}"

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%ProgramData%\Microsoft\Windows\Start Menu\Programs\EBO Redundancy Manager\Install Service.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Install_Service.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Install EBO Redundancy Service'; $Shortcut.Save()}"

echo ✅ Shortcuts created

echo.
echo [7/8] Configuring Windows Firewall...

REM Configure firewall rules
echo Configuring firewall rules for EBO Redundancy...

netsh advfirewall firewall add rule name="EBO Redundancy Web" dir=in action=allow protocol=TCP localport=5001 >nul 2>&1
netsh advfirewall firewall add rule name="EBO Redundancy API" dir=in action=allow protocol=TCP localport=5002 >nul 2>&1
netsh advfirewall firewall add rule name="EBO Heartbeat" dir=in action=allow protocol=UDP localport=5405 >nul 2>&1
netsh advfirewall firewall add rule name="EBO Cluster" dir=in action=allow protocol=TCP localport=5406 >nul 2>&1
netsh advfirewall firewall add rule name="EBO HTTP" dir=in action=allow protocol=TCP localport=80 >nul 2>&1
netsh advfirewall firewall add rule name="EBO HTTPS" dir=in action=allow protocol=TCP localport=443 >nul 2>&1
netsh advfirewall firewall add rule name="EBO License" dir=in action=allow protocol=TCP localport=1947 >nul 2>&1
netsh advfirewall firewall add rule name="SQL Server" dir=in action=allow protocol=TCP localport=1433 >nul 2>&1

echo ✅ Firewall configured

echo.
echo [8/8] Finalizing installation...

REM Create uninstaller
(
echo @echo off
echo title EBO Redundancy Manager - Uninstaller
echo echo Uninstalling EBO Redundancy Manager...
echo.
echo Stopping service...
echo sc stop "EBO_Redundancy_Service" ^>nul 2^>^&1
echo sc delete "EBO_Redundancy_Service" ^>nul 2^>^&1
echo.
echo Removing firewall rules...
echo netsh advfirewall firewall delete rule name="EBO Redundancy Web" ^>nul 2^>^&1
echo netsh advfirewall firewall delete rule name="EBO Redundancy API" ^>nul 2^>^&1
echo netsh advfirewall firewall delete rule name="EBO Heartbeat" ^>nul 2^>^&1
echo netsh advfirewall firewall delete rule name="EBO Cluster" ^>nul 2^>^&1
echo netsh advfirewall firewall delete rule name="EBO HTTP" ^>nul 2^>^&1
echo netsh advfirewall firewall delete rule name="EBO HTTPS" ^>nul 2^>^&1
echo netsh advfirewall firewall delete rule name="EBO License" ^>nul 2^>^&1
echo netsh advfirewall firewall delete rule name="SQL Server" ^>nul 2^>^&1
echo.
echo Removing shortcuts...
echo del "%%USERPROFILE%%\Desktop\EBO Redundancy Manager.lnk" ^>nul 2^>^&1
echo rmdir /s /q "%%ProgramData%%\Microsoft\Windows\Start Menu\Programs\EBO Redundancy Manager" ^>nul 2^>^&1
echo.
echo Removing installation directory...
echo rmdir /s /q "%INSTALL_DIR%" ^>nul 2^>^&1
echo.
echo EBO Redundancy Manager has been uninstalled.
echo pause
) > "%INSTALL_DIR%\Uninstall.bat"

REM Add uninstall entry to Windows
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\EBO_Redundancy_Manager" /v "DisplayName" /t REG_SZ /d "EBO Redundancy Manager" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\EBO_Redundancy_Manager" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\Uninstall.bat" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\EBO_Redundancy_Manager" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\EBO_Redundancy_Manager" /v "Publisher" /t REG_SZ /d "Professional Redundancy Solutions" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\EBO_Redundancy_Manager" /v "DisplayVersion" /t REG_SZ /d "1.0.0" /f >nul

echo ✅ Installation completed successfully!

echo.
echo ===============================================================================
echo                            INSTALLATION COMPLETE!
echo ===============================================================================
echo.
echo ✅ EBO Redundancy Manager has been installed successfully!
echo.
echo Installation Location: %INSTALL_DIR%
echo.
echo 🚀 How to start:
echo   • Desktop shortcut: "EBO Redundancy Manager"
echo   • Start Menu: Programs ^> EBO Redundancy Manager
echo   • Direct: %INSTALL_DIR%\Start_EBO_Redundancy.bat
echo.
echo 🌐 Web Interface: http://localhost:5001
echo.
echo 📋 Next Steps:
echo   1. Launch EBO Redundancy Manager from desktop shortcut
echo   2. Configure your Dell PowerEdge server details
echo   3. Set up your EBO installation paths
echo   4. Start the redundancy service
echo   5. Access web interface for monitoring
echo.
echo 🔧 Optional: Install as Windows Service
echo   • Run: %INSTALL_DIR%\Install_Service.bat
echo.
echo ===============================================================================
echo.

REM Clean up temp files
if exist "%TEMP%\ebo_installer" rmdir /s /q "%TEMP%\ebo_installer" >nul 2>&1

echo Press any key to launch EBO Redundancy Manager...
pause >nul

REM Launch the application
cd /d "%INSTALL_DIR%"
start "" "%INSTALL_DIR%\Start_EBO_Redundancy.bat"

echo.
echo Installation complete! EBO Redundancy Manager is starting...
echo.
