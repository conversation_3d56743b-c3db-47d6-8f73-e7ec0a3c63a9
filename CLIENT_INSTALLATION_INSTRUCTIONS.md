# 🚀 SafeKit-Style Redundancy Management System - Client Installation

## 📦 **What You Received**

You have received a complete **SafeKit-style redundancy management system** for your EcoStruxure Building Operation (EBO) servers. This professional solution provides:

- **Microsoft Failover Cluster-style management** via web interface
- **Real-time redundancy monitoring** and automatic failover
- **Multi-site support** for geographic redundancy
- **EBO-specific configuration** for building automation systems
- **Professional web console** similar to enterprise clustering solutions

## 🎯 **Quick Installation (5 Minutes)**

### **Step 1: Extract Package**
1. **Extract** `SafeKit_Redundancy_System_v1.0.zip` to your server
2. **Recommended location**: `C:\SafeKitRedundancy\`

### **Step 2: Install**
1. **Right-click** on `install.bat`
2. **Select** "Run as administrator"
3. **Wait** for installation to complete (2-3 minutes)

### **Step 3: Start System**
1. **Double-click** `quick_start.bat`
2. **<PERSON><PERSON><PERSON> opens automatically** to the management interface

### **Step 4: Access Web Interface**
- **Main Dashboard**: `http://localhost:5002`
- **SafeKit Console**: `http://localhost:5002/safekit-console`
- **Site Management**: `http://localhost:5002/sites`

## 🌐 **Network Access**

### **Local Server Access**
- Use: `http://localhost:5002`

### **Remote Access from Client PCs**
- Replace `localhost` with server IP
- Example: `http://*************:5002`
- **Firewall**: Ensure port 5002 is open

### **Multi-Site Access**
- Each site runs its own instance
- Configure VPN between sites for replication
- Access each site's console independently

## 🔧 **Initial Configuration for EBO**

### **1. Configure Primary EBO Server**
1. **Go to**: SafeKit Console → Nodes tab
2. **Click**: "Add Node"
3. **Configure**:
   ```
   Node Name: EBO-PRIMARY
   IP Address: [Your Primary Server IP]
   Port: 5000
   Role: Primary
   Priority: 1000
   Description: Primary EBO Server
   ```

### **2. Configure Secondary EBO Server**
1. **Click**: "Add Node"
2. **Configure**:
   ```
   Node Name: EBO-SECONDARY
   IP Address: [Your Secondary Server IP]
   Port: 5000
   Role: Secondary
   Priority: 500
   Description: Secondary EBO Server
   ```

### **3. Setup EBO Data Replication**
1. **Go to**: Replication tab
2. **Click**: "Add Directory"
3. **Configure**:
   ```
   Directory Path: C:\Program Files\Schneider Electric\EcoStruxure Building Operation\Data
   Replication Mode: Synchronous (Real-time)
   Priority: High
   Include Patterns: *.*, *.config, *.data
   Exclude Patterns: *.tmp, *.log, *.cache
   Max File Size: 1000 MB
   Sync Interval: 5 seconds
   ```

### **4. Add Your Site Information**
1. **Go to**: Site Management → Add Site
2. **Fill in your site details**:
   - Site ID, description, location
   - Network configuration
   - Server hostnames and IPs
   - EBO installation paths

## 🎯 **Testing Your Installation**

### **Basic Functionality Test**
1. **Access web interface** - Should load without errors
2. **Add a test node** - Verify forms work correctly
3. **Configure test directory** - Test replication setup
4. **View monitoring dashboard** - Check real-time updates

### **EBO Integration Test**
1. **Configure actual EBO paths** in directory replication
2. **Test manual failover** using Operations tab
3. **Monitor EBO services** through the interface
4. **Verify client connectivity** during failover tests

## 🛠 **Production Deployment**

### **For Single Site (2 Servers)**
1. **Install on both EBO servers**
2. **Configure as Primary/Secondary**
3. **Setup real-time replication**
4. **Test failover procedures**

### **For Multi-Site Deployment**
1. **Install at each site**
2. **Configure site-specific settings**
3. **Setup VPN connectivity**
4. **Configure inter-site replication**

## 📞 **Support & Troubleshooting**

### **Common Issues**

**Web Interface Won't Load:**
- Check if service is running: `start_redundancy_system.bat`
- Verify firewall allows port 5002
- Check logs in `logs\` directory

**Can't Add Nodes:**
- Verify network connectivity between servers
- Check IP addresses and ports
- Ensure both servers have the software installed

**Replication Not Working:**
- Verify directory paths exist
- Check file permissions
- Review replication logs

### **Log Files**
- **Application logs**: `logs\application.log`
- **Error logs**: `logs\error.log`
- **Access logs**: `logs\access.log`

### **Getting Help**
- **Built-in help**: Available in web interface
- **Documentation**: Complete guides included
- **Professional support**: Available for enterprise deployments

## ✅ **What You Can Do Now**

### **Immediate Actions**
- ✅ **Monitor your EBO servers** in real-time
- ✅ **Configure redundancy settings** through web interface
- ✅ **Test manual failover** procedures
- ✅ **View comprehensive logs** and monitoring data

### **Advanced Features**
- ✅ **Multi-site management** for geographic redundancy
- ✅ **Automated failover/failback** with configurable thresholds
- ✅ **Professional reporting** and audit trails
- ✅ **Enterprise-grade monitoring** similar to Microsoft Failover Cluster

## 🎉 **Ready for Production Use!**

Your SafeKit-style redundancy management system is now ready to provide enterprise-grade high availability for your EcoStruxure Building Operation environment.

**Key Benefits:**
- **Always-on availability** for critical building automation
- **Professional web interface** for easy management
- **Real-time monitoring** and alerting
- **Comprehensive logging** for compliance and troubleshooting
- **Multi-site support** for business continuity

---

**Questions?** The system includes comprehensive documentation and built-in help features to guide you through all configuration options.
