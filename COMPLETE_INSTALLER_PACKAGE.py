#!/usr/bin/env python3
"""
EBO Redundancy Manager - Complete Installer Package Creator
Creates a complete installation package with all dependencies
"""

import os
import sys
import shutil
import zipfile
from pathlib import Path
import subprocess

class CompleteInstallerCreator:
    """Creates a complete installation package"""
    
    def __init__(self):
        self.current_dir = Path(__file__).parent
        self.package_dir = self.current_dir / "EBO_Redundancy_Complete_Package"
        self.app_files = [
            'EBO_Redundancy_App.py',
            'redundancy_web_ui.py',
            'ebo_redundancy_manager.py',
            'heartbeat_manager.py',
            'database_cluster_manager.py',
            'storage_redundancy_manager.py',
            'redundancy_manager.py'
        ]
        
    def create_complete_package(self):
        """Create complete installation package"""
        print("🚀 Creating Complete EBO Redundancy Installation Package")
        print("=" * 60)
        
        try:
            # Clean and create package directory
            if self.package_dir.exists():
                shutil.rmtree(self.package_dir)
            self.package_dir.mkdir()
            
            # Create subdirectories
            self.create_directory_structure()
            
            # Copy application files
            self.copy_application_files()
            
            # Create configuration files
            self.create_configuration_files()
            
            # Create templates
            self.create_web_templates()
            
            # Create installers
            self.create_installers()
            
            # Create documentation
            self.create_documentation()
            
            # Create ZIP package
            self.create_zip_package()
            
            print("\n🎉 Complete installation package created successfully!")
            print("=" * 60)
            print(f"📁 Package Location: {self.package_dir}")
            print(f"📦 ZIP Package: {self.current_dir}/EBO_Redundancy_Complete_Package.zip")
            print("\n📋 Package Contents:")
            print("  • All application files")
            print("  • Automatic installers")
            print("  • Configuration templates")
            print("  • Web interface templates")
            print("  • Complete documentation")
            print("  • Requirements and dependencies")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to create package: {e}")
            return False
    
    def create_directory_structure(self):
        """Create package directory structure"""
        print("📁 Creating directory structure...")
        
        dirs = [
            'app',
            'config',
            'templates',
            'static',
            'docs',
            'installers',
            'logs',
            'data',
            'backup'
        ]
        
        for dir_name in dirs:
            (self.package_dir / dir_name).mkdir()
            print(f"  ✅ Created: {dir_name}")
    
    def copy_application_files(self):
        """Copy all application files"""
        print("📋 Copying application files...")
        
        for file_name in self.app_files:
            src_file = self.current_dir / file_name
            if src_file.exists():
                dst_file = self.package_dir / 'app' / file_name
                shutil.copy2(src_file, dst_file)
                print(f"  ✅ Copied: {file_name}")
            else:
                print(f"  ⚠️  Missing: {file_name}")
        
        # Copy other important files
        other_files = [
            'requirements.txt',
            'network_requirements.yaml',
            'NETWORK_SETUP_GUIDE.md',
            'PRODUCTION_DEPLOYMENT_GUIDE.md'
        ]
        
        for file_name in other_files:
            src_file = self.current_dir / file_name
            if src_file.exists():
                dst_file = self.package_dir / file_name
                shutil.copy2(src_file, dst_file)
                print(f"  ✅ Copied: {file_name}")
    
    def create_configuration_files(self):
        """Create configuration files"""
        print("⚙️ Creating configuration files...")
        
        # EBO configuration
        ebo_config = '''# EBO Redundancy Configuration
# Update these values with your actual server details

applications:
  ebo_enterprise_primary:
    description: "EBO Enterprise Server - Primary Dell R750xa"
    priority: 1
    is_primary: true
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: "ebo_enterprise_secondary"
    server_ip: "YOUR_PRIMARY_SERVER_IP"      # e.g., "*********"
    server_name: "YOUR_PRIMARY_SERVER_NAME"  # e.g., "EBO-PRIMARY"
    ebo_services:
      - "EcoStruxure Building Operation Enterprise Server"
      - "EcoStruxure Building Operation Database Service"
      - "EcoStruxure Building Operation Web Service"
      - "EcoStruxure Building Operation Automation Server"
      - "EcoStruxure Building Operation License Server"
    ebo_installation_path: "C:\\\\Program Files\\\\Schneider Electric\\\\EcoStruxure Building Operation"
    ebo_data_path: "C:\\\\ProgramData\\\\Schneider Electric\\\\EcoStruxure Building Operation"
    
  ebo_enterprise_secondary:
    description: "EBO Enterprise Server - Secondary Dell R750xa"
    priority: 2
    is_primary: false
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: null
    server_ip: "YOUR_SECONDARY_SERVER_IP"    # e.g., "*********"
    server_name: "YOUR_SECONDARY_SERVER_NAME" # e.g., "EBO-SECONDARY"
    ebo_services:
      - "EcoStruxure Building Operation Enterprise Server"
      - "EcoStruxure Building Operation Database Service"
      - "EcoStruxure Building Operation Web Service"
      - "EcoStruxure Building Operation Automation Server"
      - "EcoStruxure Building Operation License Server"
    ebo_installation_path: "C:\\\\Program Files\\\\Schneider Electric\\\\EcoStruxure Building Operation"
    ebo_data_path: "C:\\\\ProgramData\\\\Schneider Electric\\\\EcoStruxure Building Operation"

network_config:
  virtual_ip: "YOUR_VIRTUAL_IP"              # e.g., "**********"
  primary_server: "YOUR_PRIMARY_SERVER_IP"   # e.g., "*********"
  secondary_server: "YOUR_SECONDARY_SERVER_IP" # e.g., "*********"
  client_redirect_method: "dns_update"

client_pcs:
  - name: "YOUR_CLIENT_PC_1"                 # e.g., "EBO-Workstation-01"
    ip: "YOUR_CLIENT_IP_1"                   # e.g., "*********"
    current_ebo_server: "YOUR_VIRTUAL_IP"
  - name: "YOUR_CLIENT_PC_2"                 # e.g., "EBO-Workstation-02"
    ip: "YOUR_CLIENT_IP_2"                   # e.g., "*********"
    current_ebo_server: "YOUR_VIRTUAL_IP"
  - name: "YOUR_CLIENT_PC_3"                 # e.g., "EBO-Workstation-03"
    ip: "YOUR_CLIENT_IP_3"                   # e.g., "*********"
    current_ebo_server: "YOUR_VIRTUAL_IP"
  - name: "YOUR_CLIENT_PC_4"                 # e.g., "EBO-Workstation-04"
    ip: "YOUR_CLIENT_IP_4"                   # e.g., "*********"
    current_ebo_server: "YOUR_VIRTUAL_IP"
  - name: "YOUR_CLIENT_PC_5"                 # e.g., "EBO-Workstation-05"
    ip: "YOUR_CLIENT_IP_5"                   # e.g., "*********"
    current_ebo_server: "YOUR_VIRTUAL_IP"

monitoring:
  check_interval: 30
  failover_threshold: 3
  recovery_threshold: 2
  email_alerts: true
  email_server: "YOUR_SMTP_SERVER"           # e.g., "mail.yourcompany.com"
  alert_recipients:
    - "<EMAIL>"
    - "<EMAIL>"
'''
        
        config_file = self.package_dir / 'config' / 'ebo_redundancy_config.yaml'
        with open(config_file, 'w') as f:
            f.write(ebo_config)
        
        print(f"  ✅ Created: {config_file}")
    
    def create_web_templates(self):
        """Create web interface templates"""
        print("🌐 Creating web templates...")
        
        # Copy existing templates if they exist
        templates_dir = self.current_dir / 'templates'
        if templates_dir.exists():
            shutil.copytree(templates_dir, self.package_dir / 'templates', dirs_exist_ok=True)
            print(f"  ✅ Copied existing templates")
        
        # Copy static files if they exist
        static_dir = self.current_dir / 'static'
        if static_dir.exists():
            shutil.copytree(static_dir, self.package_dir / 'static', dirs_exist_ok=True)
            print(f"  ✅ Copied static files")
    
    def create_installers(self):
        """Create installer scripts"""
        print("📦 Creating installer scripts...")
        
        # Main installer
        main_installer = '''@echo off
title EBO Redundancy Manager - Complete Installer
color 0A

echo.
echo ===============================================================================
echo                    EBO REDUNDANCY MANAGER - COMPLETE INSTALLER
echo ===============================================================================
echo.
echo                Professional EcoStruxure Building Operation Redundancy System
echo                         For Dell PowerEdge R750xa Servers
echo.
echo                        EVERYTHING INSTALLED AUTOMATICALLY!
echo.
echo ===============================================================================
echo.

REM Check for admin rights
net session >nul 2>&1
if errorlevel 1 (
    echo This installer requires administrator privileges.
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo [1/5] Installing Python (if needed)...
python --version >nul 2>&1
if errorlevel 1 (
    echo Python not found. Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
) else (
    echo ✅ Python is installed
)

echo.
echo [2/5] Installing required packages...
pip install --upgrade pip --quiet
pip install -r requirements.txt --quiet
echo ✅ Packages installed

echo.
echo [3/5] Setting up application...
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "backup" mkdir backup
echo ✅ Environment prepared

echo.
echo [4/5] Configuring firewall...
netsh advfirewall firewall add rule name="EBO Redundancy Web" dir=in action=allow protocol=TCP localport=5001 >nul 2>&1
netsh advfirewall firewall add rule name="EBO Heartbeat" dir=in action=allow protocol=UDP localport=5405 >nul 2>&1
echo ✅ Firewall configured

echo.
echo [5/5] Creating shortcuts...
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\EBO Redundancy Manager.lnk'); $Shortcut.TargetPath = '%CD%\\Start_EBO_Redundancy.bat'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.Save()}" >nul 2>&1
echo ✅ Desktop shortcut created

echo.
echo ===============================================================================
echo                            INSTALLATION COMPLETE!
echo ===============================================================================
echo.
echo 🎉 EBO Redundancy Manager is ready to use!
echo.
echo 🚀 Start: Double-click desktop shortcut "EBO Redundancy Manager"
echo 🌐 Web Interface: http://localhost:5001 (after starting service)
echo.
echo 📋 Next Steps:
echo   1. Start the application from desktop shortcut
echo   2. Configure your Dell PowerEdge server details
echo   3. Set up EBO installation paths
echo   4. Start the redundancy service
echo.
echo ===============================================================================
echo.
pause
'''
        
        installer_file = self.package_dir / 'installers' / 'INSTALL.bat'
        with open(installer_file, 'w') as f:
            f.write(main_installer)
        
        # Application launcher
        launcher = '''@echo off
title EBO Redundancy Manager
cd /d "%~dp0"
python app\\EBO_Redundancy_App.py
pause
'''
        
        launcher_file = self.package_dir / 'Start_EBO_Redundancy.bat'
        with open(launcher_file, 'w') as f:
            f.write(launcher)
        
        print(f"  ✅ Created installers")
    
    def create_documentation(self):
        """Create documentation files"""
        print("📖 Creating documentation...")
        
        readme = '''# EBO Redundancy Manager - Complete Package

## Professional EcoStruxure Building Operation Redundancy System
### For Dell PowerEdge R750xa Servers

## 🚀 Quick Start

### 1. Installation
- Run `installers\\INSTALL.bat` as Administrator
- Follow the on-screen instructions

### 2. Configuration
- Edit `config\\ebo_redundancy_config.yaml`
- Update your server IP addresses
- Set your EBO installation paths
- Configure your client PCs

### 3. Start Application
- Double-click desktop shortcut "EBO Redundancy Manager"
- Or run `Start_EBO_Redundancy.bat`

### 4. Access Web Interface
- Start the service in the GUI
- Open browser to http://localhost:5001

## 📋 Package Contents

- `app\\` - Application files
- `config\\` - Configuration templates
- `templates\\` - Web interface templates
- `static\\` - Web interface assets
- `docs\\` - Documentation
- `installers\\` - Installation scripts
- `requirements.txt` - Python dependencies

## 🔧 Configuration

### Server Configuration
Update these values in `config\\ebo_redundancy_config.yaml`:

```yaml
server_ip: "YOUR_PRIMARY_SERVER_IP"    # e.g., "*********"
server_name: "YOUR_PRIMARY_SERVER_NAME" # e.g., "EBO-PRIMARY"
```

### Network Configuration
```yaml
virtual_ip: "YOUR_VIRTUAL_IP"          # e.g., "**********"
primary_server: "YOUR_PRIMARY_IP"      # e.g., "*********"
secondary_server: "YOUR_SECONDARY_IP"  # e.g., "*********"
```

### Client PCs
```yaml
client_pcs:
  - name: "EBO-Workstation-01"
    ip: "*********"
```

## 🌐 Web Interface Features

- Real-time server monitoring
- EBO service status
- Database redundancy management
- Storage synchronization
- Performance metrics
- Alert management

## 🛡️ Network Requirements

- Primary Network: Production LAN
- Heartbeat Network: Server-to-server communication
- Firewall Ports: 5001 (Web), 5405 (Heartbeat), 80/443 (EBO)

## 📞 Support

For support and documentation:
- Check the `docs\\` folder for detailed guides
- Review configuration templates
- Check log files in `logs\\` folder

## 🎯 Features

✅ Automatic failover (< 2 minutes)
✅ Real-time monitoring
✅ Database redundancy
✅ Storage synchronization
✅ Client PC management
✅ Professional web interface
✅ Enterprise-grade logging
✅ Email alerts
✅ Performance monitoring

Your EBO system now has enterprise-grade redundancy!
'''
        
        readme_file = self.package_dir / 'README.md'
        with open(readme_file, 'w') as f:
            f.write(readme)
        
        print(f"  ✅ Created documentation")
    
    def create_zip_package(self):
        """Create ZIP package"""
        print("📦 Creating ZIP package...")
        
        zip_file = self.current_dir / "EBO_Redundancy_Complete_Package.zip"
        
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            for root, dirs, files in os.walk(self.package_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_path = file_path.relative_to(self.package_dir)
                    zf.write(file_path, arc_path)
        
        print(f"  ✅ Created: {zip_file}")

if __name__ == '__main__':
    creator = CompleteInstallerCreator()
    creator.create_complete_package()
