# 🚀 SafeKit-Style Redundancy Management System - Deployment Guide

## 📦 Package Contents

Your deployment package includes:

```
SafeKit_Redundancy_System_v1.0/
├── 📁 Application Files
│   ├── multisite_web_interface.py      # Main web interface
│   ├── redundancy_manager.py           # Core redundancy logic
│   ├── multisite_manager.py           # Multi-site management
│   └── redundancy_web_ui.py           # Alternative UI
├── 📁 Installation
│   ├── install.bat                    # Automated installer
│   ├── requirements.txt               # Python dependencies
│   └── README_INSTALLATION.md         # Detailed installation guide
├── 📁 Startup Scripts
│   ├── start_redundancy_system.bat    # Start the system
│   ├── quick_start.bat               # Quick start with browser
│   └── stop_system.bat               # Stop the system
├── 📁 Directories
│   ├── logs/                         # Application logs
│   ├── config/                       # Configuration files
│   ├── data/                         # Application data
│   └── backup/                       # Backup storage
└── 📁 Documentation
    └── docs/QUICK_START.txt          # Quick reference
```

## 🎯 Installation Steps

### Step 1: Prepare Target System

**System Requirements:**
- Windows Server 2016/2019/2022 or Windows 10/11 Pro
- 4 GB RAM minimum (8 GB recommended)
- 2 GB free disk space
- Administrator privileges
- Internet connection (for initial setup)

### Step 2: Deploy Package

1. **Copy** the ZIP file to target server
2. **Extract** to desired location (e.g., `C:\SafeKitRedundancy\`)
3. **Navigate** to extracted folder

### Step 3: Run Installation

1. **Right-click** on `install.bat`
2. **Select** "Run as administrator"
3. **Follow** installation prompts
4. **Wait** for completion (2-5 minutes)

### Step 4: Start System

**Option A - Quick Start:**
```cmd
double-click: quick_start.bat
```
*Automatically opens browser and starts system*

**Option B - Manual Start:**
```cmd
double-click: start_redundancy_system.bat
```
*Starts system with console output*

**Option C - Service Mode:**
```cmd
install_service.bat (run as admin)
net start SafeKitRedundancy
```

## 🌐 Access Points

### Web Interfaces

| **Interface** | **URL** | **Purpose** |
|---------------|---------|-------------|
| **Main Dashboard** | `http://[server-ip]:5002` | System overview |
| **SafeKit Console** | `http://[server-ip]:5002/safekit-console` | **Node & cluster management** |
| **Site Management** | `http://[server-ip]:5002/sites` | Multi-site configuration |
| **Add Site Wizard** | `http://[server-ip]:5002/add-site` | New site setup |

### Network Configuration

**Local Access:**
- Use: `http://localhost:5002`
- For: Testing and local management

**Remote Access:**
- Use: `http://*************:5002` (replace with actual IP)
- Requires: Firewall port 5002 open
- Command: `netsh advfirewall firewall add rule name="SafeKit" dir=in action=allow protocol=TCP localport=5002`

## 🔧 Initial Configuration

### 1. Configure Primary Node

1. **Access**: SafeKit Console → Nodes tab
2. **Click**: "Add Node"
3. **Configure**:
   ```
   Node Name: EBO-PRIMARY
   IP Address: *********
   Port: 5000
   Role: Primary
   Priority: 1000
   Description: Primary EBO Server
   ```

### 2. Configure Secondary Node

1. **Click**: "Add Node"
2. **Configure**:
   ```
   Node Name: EBO-SECONDARY
   IP Address: *********
   Port: 5000
   Role: Secondary
   Priority: 500
   Description: Secondary EBO Server
   ```

### 3. Setup Directory Replication

1. **Go to**: Replication tab
2. **Click**: "Add Directory"
3. **Configure EBO Data**:
   ```
   Directory Path: C:\Program Files\Schneider Electric\EcoStruxure Building Operation\Data
   Replication Mode: Synchronous (Real-time)
   Priority: High
   Include Patterns: *.*, *.config, *.data
   Exclude Patterns: *.tmp, *.log, *.cache
   Max File Size: 1000 MB
   Sync Interval: 5 seconds
   ```

### 4. Add Site Configuration

1. **Go to**: Site Management
2. **Click**: "Add Site"
3. **Fill complete form** with site details

## 🏢 Production Deployment Scenarios

### Scenario 1: Single Site Redundancy

**Setup:**
- 2 servers at same location
- Shared network infrastructure
- Local database redundancy

**Configuration:**
1. Install on both servers
2. Configure as Primary/Secondary
3. Setup real-time replication
4. Test failover procedures

### Scenario 2: Multi-Site Redundancy

**Setup:**
- Multiple geographic locations
- VPN connectivity between sites
- Site-to-site replication

**Configuration:**
1. Install at each site
2. Configure site-specific settings
3. Setup inter-site replication
4. Configure WAN connectivity

### Scenario 3: EcoStruxure Building Operation (EBO)

**Specific Setup:**
- Dell PowerEdge R750xa servers
- EBO software with local database
- License server redundancy
- Client PC connectivity

**Configuration Steps:**
1. **Install** on both EBO servers
2. **Configure** EBO-specific paths:
   ```
   Application Path: C:\Program Files\Schneider Electric\EcoStruxure Building Operation
   Database Path: [EBO Database Location]
   License Server: [License Server IP]
   ```
3. **Setup** service monitoring for EBO components
4. **Test** failover with EBO clients

## 🔍 Verification & Testing

### Installation Verification

1. **Check Web Access**: Browse to http://localhost:5002
2. **Verify Console**: Access SafeKit console
3. **Test Node Addition**: Add a test node
4. **Check Logs**: Review logs/ directory for errors

### Functionality Testing

1. **Add Test Nodes**: Create sample cluster nodes
2. **Configure Replication**: Setup test directories
3. **Test Failover**: Use manual failover feature
4. **Monitor Status**: Verify real-time updates

### Network Testing

1. **Local Access**: Test from server browser
2. **Remote Access**: Test from client PCs
3. **Multi-Site**: Test inter-site connectivity
4. **Firewall**: Verify port 5002 accessibility

## 🛠 Troubleshooting

### Common Installation Issues

**Python Not Found:**
```
Error: Python is not installed
Solution: Download Python 3.8+ from python.org
         Check "Add Python to PATH" during installation
```

**Permission Denied:**
```
Error: Access denied
Solution: Run install.bat as administrator
```

**Port Conflict:**
```
Error: Port 5002 already in use
Solution: Stop conflicting service or change port in config
```

### Runtime Issues

**Web Interface Not Loading:**
1. Check if service is running
2. Verify firewall settings
3. Check logs/error.log

**Node Communication Errors:**
1. Verify network connectivity
2. Check IP addresses and ports
3. Review firewall rules

## 📞 Support & Maintenance

### Log Files
- **Application**: `logs/application.log`
- **Errors**: `logs/error.log`
- **Access**: `logs/access.log`

### Backup Procedures
1. **Stop** the service
2. **Backup** entire installation directory
3. **Export** configuration from web interface
4. **Restart** the service

### Updates
1. **Stop** current system
2. **Backup** configuration
3. **Replace** application files
4. **Restart** system
5. **Verify** functionality

---

## ✅ Ready for Production!

This system provides enterprise-grade redundancy management with:

- **Professional web interface** similar to Microsoft Failover Cluster Manager
- **Real-time monitoring** and alerting
- **Comprehensive logging** and audit trails
- **Multi-site support** for geographic redundancy
- **EBO-specific features** for building automation systems

**Support**: Professional installation and configuration support available for enterprise deployments.
