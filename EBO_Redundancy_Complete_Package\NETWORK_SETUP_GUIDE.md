# 🌐 EBO REDUNDANCY SYSTEM - NETWORK SETUP GUIDE

## 📡 Network Requirements & Heartbeat Configuration

### 🎯 Overview
This guide provides comprehensive network requirements and heartbeat configuration for your EBO redundancy system running on Dell PowerEdge R750xa servers.

## 🏗️ Network Architecture

### Recommended Network Topology
```
                    ┌─────────────────────────────────────┐
                    │         Network Switch              │
                    │      (Redundant/Stacked)           │
                    └─────────────┬───────────────────────┘
                                  │
                    ┌─────────────┼───────────────────────┐
                    │             │                       │
            ┌───────▼──────┐ ┌───▼────┐          ┌──────▼──────┐
            │ EBO-PRIMARY  │ │Virtual │          │EBO-SECONDARY│
            │ *********    │ │   IP   │          │ *********   │
            │              │ │*********0        │             │
            └──────┬───────┘ └────────┘          └──────┬──────┘
                   │                                    │
                   └────────────┬───────────────────────┘
                                │ Heartbeat Network
                          *************/24
                                │
                    ┌───────────▼────────────┐
                    │    Client PCs (5)      │
                    │  ********* - ********* │
                    └────────────────────────┘
```

## 🔧 Network Configuration Requirements

### 1. Primary Production Network
```yaml
Network: Production LAN
VLAN ID: 100
Subnet: 10.0.1.0/24
Gateway: ********
DNS: 10.0.1.5, 10.0.1.6

Servers:
  Primary:   *********  (EBO-PRIMARY-01)
  Secondary: *********  (EBO-SECONDARY-01)
  Virtual:   *********0 (Client connection point)

Client PCs:
  EBO-Workstation-01: *********
  EBO-Workstation-02: 10.0.1.21
  EBO-Workstation-03: 10.0.1.22
  EBO-Workstation-04: 10.0.1.23
  EBO-Workstation-05: *********
```

### 2. Dedicated Heartbeat Network (Recommended)
```yaml
Network: Heartbeat/Cluster Network
VLAN ID: 200
Subnet: *************/24
Purpose: Server-to-server communication

Servers:
  Primary:   **************
  Secondary: **************
```

### 3. Management Network (Optional)
```yaml
Network: Management Network
VLAN ID: 300
Subnet: 10.0.3.0/24
Purpose: Server management, monitoring

Servers:
  Primary iDRAC:   10.0.3.10
  Secondary iDRAC: 10.0.3.11
```

## 💓 Heartbeat Configuration

### Heartbeat Types Implemented

#### 1. Network Ping Heartbeat
```yaml
Type: ICMP Ping
Interval: 5 seconds
Timeout: 3 seconds
Targets: 
  - ********* (Production network)
  - ************** (Heartbeat network)
Failure Threshold: 3 consecutive failures
```

#### 2. Service Heartbeat
```yaml
Type: Windows Service Monitoring
Interval: 10 seconds
Services Monitored:
  - EcoStruxure Building Operation Enterprise Server
  - EcoStruxure Building Operation Database Service
  - EcoStruxure Building Operation Web Service
  - EcoStruxure Building Operation Automation Server
  - EcoStruxure Building Operation License Server
Failure Threshold: 2 consecutive failures
```

#### 3. Database Heartbeat
```yaml
Type: SQL Server Connection Test
Interval: 15 seconds
Port: 1433
Test: Connection establishment
Failure Threshold: 2 consecutive failures
```

#### 4. Application Heartbeat
```yaml
Type: HTTP Health Check
Interval: 30 seconds
Endpoints:
  - http://*********/EBO
  - http://*********/EBO
Expected Response: HTTP 200
Failure Threshold: 3 consecutive failures
```

#### 5. UDP Cluster Heartbeat
```yaml
Type: UDP Multicast
Port: 5405
Interval: 5 seconds
Payload: JSON with system status
Timeout: 15 seconds
```

## 🔌 Required Network Ports

### EBO Application Ports
```yaml
Port 80:   EBO Web Interface (HTTP)
Port 443:  EBO Web Interface (HTTPS)
Port 1947: EBO License Server
Port 8080: EBO Automation Server
Port 1433: SQL Server Database
```

### Redundancy System Ports
```yaml
Port 5001: Redundancy Web Management
Port 5002: REST API Interface
Port 5405: UDP Heartbeat Communication
Port 5406: TCP Cluster Management
Port 5407: Monitoring Interface
```

### System Management Ports
```yaml
Port 3389: Remote Desktop (RDP)
Port 22:   SSH (if applicable)
Port 161:  SNMP Monitoring
Port 135:  WMI Management
```

## 🛡️ Firewall Configuration

### Windows Firewall Rules (Inbound)
```powershell
# EBO Web Interface
New-NetFirewallRule -DisplayName "EBO HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow

# EBO HTTPS Interface
New-NetFirewallRule -DisplayName "EBO HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow

# EBO License Server
New-NetFirewallRule -DisplayName "EBO License" -Direction Inbound -Protocol TCP -LocalPort 1947 -Action Allow

# SQL Server Database
New-NetFirewallRule -DisplayName "SQL Server" -Direction Inbound -Protocol TCP -LocalPort 1433 -Action Allow

# Redundancy Web Management
New-NetFirewallRule -DisplayName "Redundancy Web" -Direction Inbound -Protocol TCP -LocalPort 5001 -Action Allow

# Heartbeat Communication
New-NetFirewallRule -DisplayName "Heartbeat UDP" -Direction Inbound -Protocol UDP -LocalPort 5405 -Action Allow

# Cluster Management
New-NetFirewallRule -DisplayName "Cluster TCP" -Direction Inbound -Protocol TCP -LocalPort 5406 -Action Allow

# ICMP Ping
New-NetFirewallRule -DisplayName "ICMP Ping" -Direction Inbound -Protocol ICMPv4 -IcmpType 8 -Action Allow
```

## 📊 Network Performance Requirements

### Latency Requirements
```yaml
Server-to-Server: < 5ms (critical for heartbeat)
Client-to-Server: < 20ms (acceptable for user experience)
Database Replication: < 2ms (for synchronous replication)
```

### Bandwidth Requirements
```yaml
Minimum Total: 100Mbps
Recommended: 1Gbps
Heartbeat Traffic: 10Mbps dedicated
Database Replication: 50Mbps
EBO Application: 30Mbps
```

### Availability Requirements
```yaml
Network Uptime: 99.9%
Switch Redundancy: Required
Cable Redundancy: Recommended
Maximum Packet Loss: 0.1%
```

## 🔧 Implementation Steps

### Step 1: Network Infrastructure Setup
```bash
1. Configure network switches with VLANs
2. Set up redundant network paths
3. Configure firewall rules
4. Test network connectivity
5. Verify DNS resolution
6. Configure NTP time synchronization
```

### Step 2: Server Network Configuration
```powershell
# Configure primary network interface
netsh interface ip set address "Local Area Connection" static ********* ************* ********

# Configure heartbeat network interface
netsh interface ip set address "Local Area Connection 2" static ************** *************

# Configure virtual IP (will be managed by redundancy system)
netsh interface ip add address "Local Area Connection" *********0 *************
```

### Step 3: Heartbeat System Configuration
```python
# Configure heartbeat in your application
heartbeat_config = {
    'heartbeat_interval': 5,
    'heartbeat_timeout': 15,
    'failure_threshold': 3,
    'heartbeat_port': 5405,
    'peer_servers': [
        {'ip': '*********', 'name': 'EBO-SECONDARY-01'},
        {'ip': '**************', 'name': 'EBO-SECONDARY-01-HB'}
    ]
}
```

### Step 4: Client PC Configuration
```registry
# Update EBO client registry to use virtual IP
[HKEY_LOCAL_MACHINE\SOFTWARE\Schneider Electric\EcoStruxure Building Operation]
"ServerAddress"="*********0"
```

## 🚨 Failover Scenarios

### Scenario 1: Primary Server Network Failure
```yaml
Detection: Ping failure to *********
Response: 
  1. Verify failure via heartbeat network (**************)
  2. If confirmed, move virtual IP to secondary server
  3. Start EBO services on secondary server
  4. Redirect client connections
Recovery Time: < 2 minutes
```

### Scenario 2: Heartbeat Network Failure
```yaml
Detection: UDP heartbeat communication lost
Response:
  1. Continue monitoring via production network
  2. Increase monitoring frequency
  3. Alert administrators
Recovery: Restore heartbeat network connectivity
```

### Scenario 3: Split-Brain Prevention
```yaml
Detection: Both servers think they are primary
Response:
  1. Check quorum (client connections)
  2. Server with more active clients remains primary
  3. Other server goes to standby mode
Prevention: Proper network design with witness server
```

## 📈 Monitoring and Alerting

### Network Monitoring Targets
```yaml
Ping Monitoring:
  - ********* (Primary server)
  - ********* (Secondary server)
  - ************** (Primary heartbeat)
  - ************** (Secondary heartbeat)
  - *********0 (Virtual IP)

Performance Monitoring:
  - Bandwidth utilization (alert > 80%)
  - Latency monitoring (alert > 10ms)
  - Packet loss (alert > 0.5%)
```

### Alert Conditions
```yaml
Critical Alerts:
  - Server unreachable for > 15 seconds
  - Heartbeat communication lost
  - Virtual IP not responding
  - Database connection failed

Warning Alerts:
  - High network latency (> 10ms)
  - High bandwidth utilization (> 80%)
  - Packet loss detected (> 0.1%)
```

## 🔒 Network Security

### VLAN Isolation
```yaml
Production VLAN (100): Client and server communication
Heartbeat VLAN (200): Server-to-server only
Management VLAN (300): Administrative access only
```

### Access Control Lists
```yaml
Production Network:
  - Clients can access EBO ports only
  - Servers can communicate freely
  - No internet access required

Heartbeat Network:
  - Server-to-server communication only
  - No client or internet access
  - Isolated from other networks
```

## 📋 Network Validation Checklist

### Pre-Deployment Testing
- [ ] Ping connectivity between all servers
- [ ] Port connectivity testing (telnet/nc)
- [ ] DNS resolution verification
- [ ] NTP synchronization check
- [ ] Firewall rule validation
- [ ] VLAN configuration verification
- [ ] Bandwidth and latency testing

### Post-Deployment Testing
- [ ] Heartbeat communication verification
- [ ] Failover testing (planned)
- [ ] Client connectivity during failover
- [ ] Network monitoring setup
- [ ] Alert system testing
- [ ] Performance baseline establishment

## 🎯 Best Practices

### Network Design
1. **Use redundant network switches** to eliminate single points of failure
2. **Implement separate heartbeat network** for reliable cluster communication
3. **Configure proper VLANs** for network isolation and security
4. **Use static IP addresses** for all servers and critical infrastructure
5. **Implement network monitoring** for proactive issue detection

### Heartbeat Configuration
1. **Multiple heartbeat methods** for comprehensive monitoring
2. **Appropriate timeouts** to balance responsiveness and false positives
3. **Graduated failure thresholds** for different types of failures
4. **Comprehensive logging** for troubleshooting and analysis
5. **Regular testing** of failover scenarios

### Security Considerations
1. **Network segmentation** using VLANs
2. **Firewall rules** restricting unnecessary access
3. **Encrypted communication** where possible
4. **Regular security updates** for network infrastructure
5. **Access logging** for audit trails

---

## 📞 Support Information

For network configuration support:
- Review network logs in `logs/heartbeat.log`
- Check firewall settings and port accessibility
- Verify DNS resolution and time synchronization
- Test network connectivity using provided tools
- Monitor heartbeat status through web interface

**Your EBO redundancy system now has enterprise-grade network monitoring and heartbeat capabilities!** 🚀
