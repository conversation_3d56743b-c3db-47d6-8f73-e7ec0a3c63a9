# 🚀 EBO REDUNDANCY SYSTEM - PRODUCTION DEPLOYMENT GUIDE

## 📋 Overview
This guide will help you deploy the EBO Redundancy System for **real production use** in your environment with Dell PowerEdge R750xa servers and EcoStruxure Building Operation software.

## 🎯 Production Deployment Steps

### Phase 1: Pre-Production Planning

#### 1.1 Environment Requirements
- **Windows Server 2019/2022** on both Dell PowerEdge R750xa servers
- **EcoStruxure Building Operation** installed and licensed on both servers
- **SQL Server Express/Standard** for database redundancy
- **Network connectivity** between servers (1Gbps recommended)
- **Administrative access** to both servers
- **Python 3.8+** installed on both servers

#### 1.2 Network Planning
```yaml
# Example Network Configuration
Primary_Server:
  hostname: "EBO-PRIMARY"
  ip_address: "*********"
  
Secondary_Server:
  hostname: "EBO-SECONDARY" 
  ip_address: "*********"
  
Virtual_IP: "**********"  # For client connections
Subnet: "********/24"
Domain: "yourcompany.local"

Client_PCs:
  - name: "EBO-Workstation-01"
    ip: "*********"
  - name: "EBO-Workstation-02"
    ip: "*********"
  # Add all your client PCs
```

### Phase 2: Automated Production Deployment

#### 2.1 Run Production Deployment Script
```bash
# On your primary server, run as Administrator:
python production_deployment.py
```

This script will:
- ✅ Validate your environment
- ✅ Create production directory structure
- ✅ Copy all application files
- ✅ Generate production configuration templates
- ✅ Create Windows service installer
- ✅ Create management scripts

#### 2.2 Configure for Your Environment
Edit the generated `production_config.yaml` file:

```yaml
servers:
  primary:
    hostname: 'YOUR_PRIMARY_SERVER_NAME'     # e.g., 'EBO-PRIMARY'
    ip_address: 'YOUR_PRIMARY_SERVER_IP'     # e.g., '*********'
    ebo_installation_path: 'C:\Program Files\Schneider Electric\EcoStruxure Building Operation'
    database_path: 'C:\ProgramData\Schneider Electric\EcoStruxure Building Operation\Database'
    
  secondary:
    hostname: 'YOUR_SECONDARY_SERVER_NAME'   # e.g., 'EBO-SECONDARY'
    ip_address: 'YOUR_SECONDARY_SERVER_IP'   # e.g., '*********'
    ebo_installation_path: 'C:\Program Files\Schneider Electric\EcoStruxure Building Operation'
    database_path: 'C:\ProgramData\Schneider Electric\EcoStruxure Building Operation\Database'

network:
  virtual_ip: 'YOUR_VIRTUAL_IP'              # e.g., '**********'
  subnet: 'YOUR_SUBNET'                      # e.g., '********/24'
  domain: 'YOUR_DOMAIN.local'                # e.g., 'yourcompany.local'
  monitoring_port: 5001
  
client_pcs:
  - name: 'EBO-Workstation-01'
    ip: 'YOUR_CLIENT_IP_1'                   # e.g., '*********'
  - name: 'EBO-Workstation-02'
    ip: 'YOUR_CLIENT_IP_2'                   # e.g., '*********'
  # Add all your actual client PCs

monitoring:
  check_interval: 30                         # Check every 30 seconds
  failover_threshold: 3                      # Fail after 3 consecutive failures
  recovery_threshold: 2                      # Recover after 2 consecutive successes
  email_alerts: true
  email_server: 'YOUR_SMTP_SERVER'           # e.g., 'mail.yourcompany.com'
  alert_recipients: 
    - '<EMAIL>'
    - '<EMAIL>'
```

### Phase 3: Service Installation

#### 3.1 Install Windows Service
```bash
# Run as Administrator:
cd C:\EBO_Redundancy_Production\scripts
install_service.bat
```

#### 3.2 Configure Service Auto-Start
```bash
# Set service to start automatically:
sc config "EBO_Redundancy_Service" start= auto

# Start the service:
sc start "EBO_Redundancy_Service"
```

### Phase 4: Network Configuration

#### 4.1 Configure Virtual IP
Set up a virtual IP address for client connections:

```powershell
# On primary server, configure virtual IP:
netsh interface ip add address "Local Area Connection" ********** *************

# Configure failover script to move IP during failover
```

#### 4.2 Configure DNS (Recommended)
Create DNS entries for seamless failover:

```dns
# DNS A Records:
ebo-cluster.yourcompany.local    → ********** (Virtual IP)
ebo-primary.yourcompany.local    → *********  (Primary Server)
ebo-secondary.yourcompany.local  → *********  (Secondary Server)
```

### Phase 5: Client PC Configuration

#### 5.1 Update EBO Client Connections
Update all client PCs to connect to the virtual IP:

```registry
# Registry update for EBO clients:
[HKEY_LOCAL_MACHINE\SOFTWARE\Schneider Electric\EcoStruxure Building Operation]
"ServerAddress"="**********"  # Virtual IP instead of specific server IP
```

#### 5.2 Test Client Connections
1. Verify all clients can connect through virtual IP
2. Test failover by stopping primary server
3. Confirm clients automatically reconnect to secondary

### Phase 6: Database Redundancy Setup

#### 6.1 Configure SQL Server Replication
```sql
-- On primary server:
-- Configure SQL Server for replication
EXEC sp_configure 'show advanced options', 1;
RECONFIGURE;
EXEC sp_configure 'Database Mail XPs', 1;
RECONFIGURE;

-- Set up database mirroring or Always On Availability Groups
-- (Specific steps depend on your SQL Server version)
```

#### 6.2 Configure Database Backup
```yaml
# Automated database backup configuration:
backup:
  enabled: true
  backup_path: 'D:\EBO_Backup\Database'
  schedule: 'every 4 hours'
  retention_days: 30
  compression: true
  encryption: true
```

### Phase 7: Testing and Validation

#### 7.1 Functional Testing
```bash
# Test redundancy system:
1. Access web interface: http://your-server:5001
2. Verify both servers are monitored
3. Check EBO service status
4. Test manual failover
5. Verify automatic failover
```

#### 7.2 Failover Testing
```bash
# Controlled failover test:
1. Document current state
2. Simulate primary server failure
3. Verify automatic failover occurs
4. Test client reconnection
5. Verify data integrity
6. Test failback procedure
```

## 🔧 Production Management

### Daily Operations

#### Start/Stop Commands
```bash
# Start redundancy system:
C:\EBO_Redundancy_Production\scripts\start_ebo_redundancy.bat

# Stop redundancy system:
C:\EBO_Redundancy_Production\scripts\stop_ebo_redundancy.bat

# Check status:
C:\EBO_Redundancy_Production\scripts\status_ebo_redundancy.bat
```

#### Web Interface Access
- **Primary Management**: http://your-primary-server:5001
- **Secondary Management**: http://your-secondary-server:5001
- **EBO Redundancy Dashboard**: http://your-server:5001/ebo-redundancy

### Monitoring and Alerts

#### Log Files
```bash
# System logs location:
C:\EBO_Redundancy_Production\logs\

# Key log files:
- ebo_redundancy.log      # Main system log
- failover.log           # Failover events
- monitoring.log         # Health monitoring
- database.log           # Database operations
```

#### Email Alerts
Configure SMTP settings for automatic alerts:
- Server failures
- Failover events
- Database issues
- Storage problems

### Maintenance Procedures

#### Weekly Tasks
- [ ] Review system logs
- [ ] Check disk space usage
- [ ] Verify backup integrity
- [ ] Test manual failover
- [ ] Update monitoring thresholds

#### Monthly Tasks
- [ ] Full failover test
- [ ] Database integrity check
- [ ] Performance review
- [ ] Update documentation
- [ ] Security updates

## 🛡️ Security Considerations

### Network Security
```yaml
# Firewall rules required:
Inbound_Rules:
  - Port 5001: Web interface access
  - Port 1433: SQL Server replication
  - Port 1947: EBO License server
  - Port 80/443: EBO Web services

Outbound_Rules:
  - SMTP: Email alerts
  - DNS: Name resolution
  - NTP: Time synchronization
```

### Access Control
- **Service Account**: Create dedicated service account for redundancy system
- **Web Interface**: Configure authentication and HTTPS
- **Database Access**: Use SQL Server authentication with strong passwords
- **File Permissions**: Restrict access to configuration and log files

## 📊 Performance Optimization

### System Resources
```yaml
# Recommended system resources:
CPU: 4+ cores per server
RAM: 16GB+ per server
Storage: SSD for database and logs
Network: 1Gbps between servers
```

### Monitoring Thresholds
```yaml
# Optimal monitoring settings:
check_interval: 30        # 30 seconds for responsive failover
failover_threshold: 3     # 90 seconds before failover
recovery_threshold: 2     # 60 seconds to confirm recovery
```

## 🚨 Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check service status:
sc query "EBO_Redundancy_Service"

# Check logs:
type C:\EBO_Redundancy_Production\logs\ebo_redundancy.log

# Restart service:
sc stop "EBO_Redundancy_Service"
sc start "EBO_Redundancy_Service"
```

#### Failover Not Working
1. Check network connectivity between servers
2. Verify EBO services are running
3. Check database replication status
4. Review failover logs
5. Test manual failover

#### Client Connection Issues
1. Verify virtual IP configuration
2. Check DNS resolution
3. Test EBO service accessibility
4. Review client configuration
5. Check firewall rules

## 📞 Support and Maintenance

### Documentation
- Keep deployment configuration documented
- Maintain network diagrams
- Document custom configurations
- Record failover procedures

### Backup Strategy
- **System Backup**: Full server backup weekly
- **Configuration Backup**: Daily backup of config files
- **Database Backup**: Automated database backup every 4 hours
- **Log Backup**: Archive logs monthly

### Update Procedures
1. Test updates in development environment
2. Schedule maintenance windows
3. Backup current configuration
4. Apply updates to secondary server first
5. Test failover functionality
6. Apply updates to primary server

## 🎯 Success Metrics

### Key Performance Indicators
- **Uptime**: Target 99.9% availability
- **Failover Time**: < 2 minutes
- **Recovery Time**: < 5 minutes
- **Data Loss**: Zero data loss during failover

### Monitoring Dashboard
Track these metrics in the web interface:
- Server health status
- EBO service availability
- Database replication lag
- Client connection count
- Failover frequency

---

## 📋 Production Checklist

### Pre-Deployment
- [ ] Environment validated
- [ ] Network configured
- [ ] Servers prepared
- [ ] EBO software installed
- [ ] Database configured

### Deployment
- [ ] Production deployment script executed
- [ ] Configuration files updated
- [ ] Windows service installed
- [ ] Network settings configured
- [ ] Client PCs updated

### Testing
- [ ] Functional testing completed
- [ ] Failover testing successful
- [ ] Client connectivity verified
- [ ] Database replication working
- [ ] Monitoring alerts configured

### Go-Live
- [ ] Production monitoring active
- [ ] Support team trained
- [ ] Documentation complete
- [ ] Backup procedures verified
- [ ] Emergency contacts updated

**Your EBO Redundancy System is now ready for production use!** 🚀
