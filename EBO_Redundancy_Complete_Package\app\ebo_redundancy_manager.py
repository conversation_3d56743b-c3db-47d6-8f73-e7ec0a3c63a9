#!/usr/bin/env python3
"""
EcoStruxure Building Operation (EBO) Redundancy Manager
Specialized redundancy management for EBO software on Dell PowerEdge servers
"""

import os
import sys
import yaml
import time
import threading
import subprocess
import socket
import psutil
import winreg
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
from heartbeat_manager import HeartbeatManager

class EBORedundancyManager:
    """Specialized redundancy manager for EBO software"""

    def __init__(self, config_file: str = "ebo_redundancy_config.yaml"):
        self.config_file = config_file
        self.config = {}
        self.monitoring_active = False
        self.failover_in_progress = False
        self.current_primary = None
        self.current_secondary = None
        self.heartbeat_manager = None

        # Setup logging
        self.logger = logging.getLogger('EBORedundancyManager')
        handler = logging.FileHandler('logs/ebo_redundancy.log')
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

        # Load configuration
        self.load_configuration()

    def load_configuration(self) -> bool:
        """Load EBO redundancy configuration"""
        try:
            if not os.path.exists(self.config_file):
                self.logger.error(f"Configuration file {self.config_file} not found")
                return False

            with open(self.config_file, 'r') as f:
                self.config = yaml.safe_load(f)

            # Identify primary and secondary servers
            for app_name, app_config in self.config.get('applications', {}).items():
                if app_config.get('is_primary') and 'enterprise' in app_name.lower():
                    self.current_primary = app_name
                elif not app_config.get('is_primary') and 'enterprise' in app_name.lower():
                    self.current_secondary = app_name

            self.logger.info(f"EBO configuration loaded. Primary: {self.current_primary}, Secondary: {self.current_secondary}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            return False

    def check_ebo_services(self, server_ip: str) -> Dict[str, bool]:
        """Check EBO services on a specific server"""
        service_status = {}

        ebo_services = [
            "EcoStruxure Building Operation Enterprise Server",
            "EcoStruxure Building Operation Database Service",
            "EcoStruxure Building Operation Web Service",
            "EcoStruxure Building Operation Automation Server",
            "EcoStruxure Building Operation License Server"
        ]

        try:
            for service_name in ebo_services:
                try:
                    # Check if service exists and is running
                    service = psutil.win_service_get(service_name)
                    service_status[service_name] = service.status() == 'running'
                except psutil.NoSuchProcess:
                    service_status[service_name] = False

        except Exception as e:
            self.logger.error(f"Error checking services on {server_ip}: {e}")

        return service_status

    def check_ebo_database(self, server_ip: str, port: int = 1433) -> bool:
        """Check EBO database connectivity"""
        try:
            # Test SQL Server connection
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((server_ip, port))
            sock.close()

            if result == 0:
                # Additional check for EBO database
                return self._check_ebo_database_files(server_ip)
            return False

        except Exception as e:
            self.logger.error(f"Database check failed for {server_ip}: {e}")
            return False

    def _check_ebo_database_files(self, server_ip: str) -> bool:
        """Check if EBO database files exist and are accessible"""
        try:
            db_path = "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation\\Database"

            if server_ip == "localhost" or server_ip == "127.0.0.1":
                return os.path.exists(db_path) and os.path.isdir(db_path)
            else:
                # For remote servers, try to access via UNC path
                unc_path = f"\\\\{server_ip}\\C$\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation\\Database"
                return os.path.exists(unc_path)

        except Exception as e:
            self.logger.error(f"Database file check failed: {e}")
            return False

    def check_ebo_web_interface(self, server_ip: str, port: int = 80) -> bool:
        """Check EBO web interface availability"""
        try:
            import requests
            url = f"http://{server_ip}:{port}/EBO"
            response = requests.get(url, timeout=10)
            return response.status_code == 200

        except Exception as e:
            self.logger.debug(f"Web interface check failed for {server_ip}: {e}")
            return False

    def check_ebo_license_server(self, server_ip: str, port: int = 1947) -> bool:
        """Check EBO license server availability"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((server_ip, port))
            sock.close()
            return result == 0

        except Exception as e:
            self.logger.error(f"License server check failed for {server_ip}: {e}")
            return False

    def get_ebo_system_health(self) -> Dict[str, Any]:
        """Get comprehensive EBO system health"""
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'primary_server': {},
            'secondary_server': {},
            'database_cluster': {},
            'license_server': {},
            'client_connectivity': {},
            'storage_status': {}
        }

        try:
            # Check primary server
            if self.current_primary:
                primary_config = self.config['applications'][self.current_primary]
                primary_ip = primary_config['server_ip']

                health_status['primary_server'] = {
                    'name': primary_config['server_name'],
                    'ip': primary_ip,
                    'services': self.check_ebo_services(primary_ip),
                    'database': self.check_ebo_database(primary_ip),
                    'web_interface': self.check_ebo_web_interface(primary_ip),
                    'license_server': self.check_ebo_license_server(primary_ip)
                }

            # Check secondary server
            if self.current_secondary:
                secondary_config = self.config['applications'][self.current_secondary]
                secondary_ip = secondary_config['server_ip']

                health_status['secondary_server'] = {
                    'name': secondary_config['server_name'],
                    'ip': secondary_ip,
                    'services': self.check_ebo_services(secondary_ip),
                    'database': self.check_ebo_database(secondary_ip),
                    'web_interface': self.check_ebo_web_interface(secondary_ip),
                    'license_server': self.check_ebo_license_server(secondary_ip)
                }

            # Determine overall status
            primary_healthy = self._is_server_healthy(health_status['primary_server'])
            secondary_healthy = self._is_server_healthy(health_status['secondary_server'])

            if primary_healthy and secondary_healthy:
                health_status['overall_status'] = 'healthy'
            elif primary_healthy or secondary_healthy:
                health_status['overall_status'] = 'degraded'
            else:
                health_status['overall_status'] = 'failed'

        except Exception as e:
            self.logger.error(f"Error getting system health: {e}")
            health_status['overall_status'] = 'error'

        return health_status

    def _is_server_healthy(self, server_status: Dict) -> bool:
        """Determine if a server is healthy based on its status"""
        if not server_status:
            return False

        # Check critical services
        services = server_status.get('services', {})
        critical_services = [
            "EcoStruxure Building Operation Enterprise Server",
            "EcoStruxure Building Operation Database Service"
        ]

        for service in critical_services:
            if not services.get(service, False):
                return False

        # Check database connectivity
        if not server_status.get('database', False):
            return False

        return True

    def initiate_ebo_failover(self) -> bool:
        """Initiate EBO failover from primary to secondary"""
        if self.failover_in_progress:
            self.logger.warning("Failover already in progress")
            return False

        try:
            self.failover_in_progress = True
            self.logger.critical("Initiating EBO failover")

            # Step 1: Stop EBO services on primary server
            primary_config = self.config['applications'][self.current_primary]
            primary_ip = primary_config['server_ip']

            self.logger.info("Stopping EBO services on primary server")
            self._stop_ebo_services(primary_ip)

            # Step 2: Start EBO services on secondary server
            secondary_config = self.config['applications'][self.current_secondary]
            secondary_ip = secondary_config['server_ip']

            self.logger.info("Starting EBO services on secondary server")
            self._start_ebo_services(secondary_ip)

            # Step 3: Update virtual IP or DNS
            virtual_ip = self.config.get('network_config', {}).get('virtual_ip')
            if virtual_ip:
                self._update_virtual_ip(virtual_ip, secondary_ip)

            # Step 4: Update client connections
            self._update_client_connections(secondary_ip)

            # Step 5: Verify failover success
            time.sleep(30)  # Wait for services to start
            secondary_health = self.check_ebo_services(secondary_ip)

            if self._is_server_healthy({'services': secondary_health}):
                self.logger.info("EBO failover completed successfully")

                # Swap primary and secondary
                self.current_primary, self.current_secondary = self.current_secondary, self.current_primary

                self.failover_in_progress = False
                return True
            else:
                self.logger.error("EBO failover failed - secondary server not healthy")
                self.failover_in_progress = False
                return False

        except Exception as e:
            self.logger.error(f"EBO failover failed: {e}")
            self.failover_in_progress = False
            return False

    def _stop_ebo_services(self, server_ip: str):
        """Stop EBO services on a server"""
        ebo_services = [
            "EcoStruxure Building Operation Enterprise Server",
            "EcoStruxure Building Operation Web Service",
            "EcoStruxure Building Operation Automation Server"
        ]

        for service_name in ebo_services:
            try:
                if server_ip in ["localhost", "127.0.0.1"]:
                    subprocess.run(['sc', 'stop', f'"{service_name}"'], check=False)
                else:
                    subprocess.run(['sc', f'\\\\{server_ip}', 'stop', f'"{service_name}"'], check=False)

                self.logger.info(f"Stopped service: {service_name} on {server_ip}")

            except Exception as e:
                self.logger.error(f"Failed to stop service {service_name} on {server_ip}: {e}")

    def _start_ebo_services(self, server_ip: str):
        """Start EBO services on a server"""
        ebo_services = [
            "EcoStruxure Building Operation Database Service",
            "EcoStruxure Building Operation Enterprise Server",
            "EcoStruxure Building Operation Web Service",
            "EcoStruxure Building Operation Automation Server"
        ]

        for service_name in ebo_services:
            try:
                if server_ip in ["localhost", "127.0.0.1"]:
                    subprocess.run(['sc', 'start', f'"{service_name}"'], check=False)
                else:
                    subprocess.run(['sc', f'\\\\{server_ip}', 'start', f'"{service_name}"'], check=False)

                self.logger.info(f"Started service: {service_name} on {server_ip}")
                time.sleep(5)  # Wait between service starts

            except Exception as e:
                self.logger.error(f"Failed to start service {service_name} on {server_ip}: {e}")

    def _update_virtual_ip(self, virtual_ip: str, new_server_ip: str):
        """Update virtual IP to point to new server"""
        try:
            # This would typically involve updating network configuration
            # For Windows, you might use netsh commands
            # For now, we'll log the action
            self.logger.info(f"Virtual IP {virtual_ip} should be updated to point to {new_server_ip}")

            # Example netsh command (would need to be customized for your network)
            # subprocess.run(['netsh', 'interface', 'ip', 'set', 'address', 'name="Local Area Connection"', 'source=static', f'addr={virtual_ip}', 'mask=*************'], check=False)

        except Exception as e:
            self.logger.error(f"Failed to update virtual IP: {e}")

    def _update_client_connections(self, new_server_ip: str):
        """Update client PC connections to point to new server"""
        try:
            # This would involve updating client configurations
            # Could be done via registry updates, config file changes, or DNS updates
            self.logger.info(f"Client connections should be updated to point to {new_server_ip}")

            # Example: Update registry entries for EBO client connections
            # This would need to be customized based on how EBO stores connection info

        except Exception as e:
            self.logger.error(f"Failed to update client connections: {e}")

    def start_ebo_monitoring(self) -> bool:
        """Start EBO monitoring"""
        if self.monitoring_active:
            return False

        self.monitoring_active = True

        # Initialize and start heartbeat manager
        self._initialize_heartbeat_manager()

        monitor_thread = threading.Thread(target=self._ebo_monitoring_loop, daemon=True)
        monitor_thread.start()

        self.logger.info("EBO monitoring started")
        return True

    def stop_ebo_monitoring(self) -> bool:
        """Stop EBO monitoring"""
        self.monitoring_active = False
        # Stop heartbeat manager
        if self.heartbeat_manager:
            self.heartbeat_manager.stop_heartbeat_monitoring()

        self.logger.info("EBO monitoring stopped")
        return True

    def _initialize_heartbeat_manager(self):
        """Initialize the heartbeat manager"""
        try:
            # Get heartbeat configuration from main config
            heartbeat_config = {
                'heartbeat_interval': 5,
                'heartbeat_timeout': 15,
                'failure_threshold': 3,
                'heartbeat_port': 5405,
                'peer_servers': []
            }

            # Add peer servers from configuration
            if self.current_secondary:
                secondary_config = self.config['applications'][self.current_secondary]
                heartbeat_config['peer_servers'].append({
                    'ip': secondary_config['server_ip'],
                    'name': secondary_config['server_name']
                })

            # Initialize heartbeat manager
            self.heartbeat_manager = HeartbeatManager(heartbeat_config)

            # Start heartbeat monitoring
            self.heartbeat_manager.start_heartbeat_monitoring()

            self.logger.info("Heartbeat manager initialized and started")

        except Exception as e:
            self.logger.error(f"Failed to initialize heartbeat manager: {e}")

    def _ebo_monitoring_loop(self):
        """Main EBO monitoring loop"""
        failure_count = 0

        while self.monitoring_active:
            try:
                # Get system health
                health = self.get_ebo_system_health()

                # Check if primary server is healthy
                primary_healthy = self._is_server_healthy(health['primary_server'])

                if not primary_healthy:
                    failure_count += 1
                    self.logger.warning(f"Primary server unhealthy. Failure count: {failure_count}")

                    # Check failover threshold
                    failover_threshold = self.config['applications'][self.current_primary].get('failover_threshold', 3)

                    if failure_count >= failover_threshold:
                        self.logger.critical("Failover threshold reached. Initiating failover.")
                        self.initiate_ebo_failover()
                        failure_count = 0
                else:
                    failure_count = 0

                # Wait before next check
                time.sleep(30)  # Check every 30 seconds

            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)  # Wait longer on error

    def manual_ebo_failover(self) -> bool:
        """Manually trigger EBO failover"""
        self.logger.info("Manual EBO failover requested")
        return self.initiate_ebo_failover()

    def get_ebo_status_summary(self) -> Dict[str, Any]:
        """Get EBO status summary for web interface"""
        health = self.get_ebo_system_health()

        return {
            'system_name': 'EcoStruxure Building Operation',
            'overall_status': health['overall_status'],
            'monitoring_active': self.monitoring_active,
            'failover_in_progress': self.failover_in_progress,
            'current_primary': self.current_primary,
            'current_secondary': self.current_secondary,
            'primary_server': health['primary_server'],
            'secondary_server': health['secondary_server'],
            'timestamp': health['timestamp']
        }
