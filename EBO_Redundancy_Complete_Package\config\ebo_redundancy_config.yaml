# EBO Redundancy Configuration
# Update these values with your actual server details

applications:
  ebo_enterprise_primary:
    description: "EBO Enterprise Server - Primary Dell R750xa"
    priority: 1
    is_primary: true
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: "ebo_enterprise_secondary"
    server_ip: "YOUR_PRIMARY_SERVER_IP"      # e.g., "*********"
    server_name: "YOUR_PRIMARY_SERVER_NAME"  # e.g., "EBO-PRIMARY"
    ebo_services:
      - "EcoStruxure Building Operation Enterprise Server"
      - "EcoStruxure Building Operation Database Service"
      - "EcoStruxure Building Operation Web Service"
      - "EcoStruxure Building Operation Automation Server"
      - "EcoStruxure Building Operation License Server"
    ebo_installation_path: "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation"
    ebo_data_path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation"
    
  ebo_enterprise_secondary:
    description: "EBO Enterprise Server - Secondary Dell R750xa"
    priority: 2
    is_primary: false
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: null
    server_ip: "YOUR_SECONDARY_SERVER_IP"    # e.g., "*********"
    server_name: "YOUR_SECONDARY_SERVER_NAME" # e.g., "EBO-SECONDARY"
    ebo_services:
      - "EcoStruxure Building Operation Enterprise Server"
      - "EcoStruxure Building Operation Database Service"
      - "EcoStruxure Building Operation Web Service"
      - "EcoStruxure Building Operation Automation Server"
      - "EcoStruxure Building Operation License Server"
    ebo_installation_path: "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation"
    ebo_data_path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation"

network_config:
  virtual_ip: "YOUR_VIRTUAL_IP"              # e.g., "*********0"
  primary_server: "YOUR_PRIMARY_SERVER_IP"   # e.g., "*********"
  secondary_server: "YOUR_SECONDARY_SERVER_IP" # e.g., "*********"
  client_redirect_method: "dns_update"

client_pcs:
  - name: "YOUR_CLIENT_PC_1"                 # e.g., "EBO-Workstation-01"
    ip: "YOUR_CLIENT_IP_1"                   # e.g., "*********"
    current_ebo_server: "YOUR_VIRTUAL_IP"
  - name: "YOUR_CLIENT_PC_2"                 # e.g., "EBO-Workstation-02"
    ip: "YOUR_CLIENT_IP_2"                   # e.g., "*********"
    current_ebo_server: "YOUR_VIRTUAL_IP"
  - name: "YOUR_CLIENT_PC_3"                 # e.g., "EBO-Workstation-03"
    ip: "YOUR_CLIENT_IP_3"                   # e.g., "*********"
    current_ebo_server: "YOUR_VIRTUAL_IP"
  - name: "YOUR_CLIENT_PC_4"                 # e.g., "EBO-Workstation-04"
    ip: "YOUR_CLIENT_IP_4"                   # e.g., "*********"
    current_ebo_server: "YOUR_VIRTUAL_IP"
  - name: "YOUR_CLIENT_PC_5"                 # e.g., "EBO-Workstation-05"
    ip: "YOUR_CLIENT_IP_5"                   # e.g., "*********"
    current_ebo_server: "YOUR_VIRTUAL_IP"

monitoring:
  check_interval: 30
  failover_threshold: 3
  recovery_threshold: 2
  email_alerts: true
  email_server: "YOUR_SMTP_SERVER"           # e.g., "mail.yourcompany.com"
  alert_recipients:
    - "<EMAIL>"
    - "<EMAIL>"
