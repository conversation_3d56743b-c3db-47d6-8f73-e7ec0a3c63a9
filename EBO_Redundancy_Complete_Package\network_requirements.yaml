# EBO Redundancy System - Network Requirements & Configuration
# Comprehensive network setup for Dell PowerEdge R750xa servers

# ============================================================================
# NETWORK TOPOLOGY REQUIREMENTS
# ============================================================================
network_topology:
  description: "Recommended network architecture for EBO redundancy"
  
  # Primary Production Network
  production_network:
    name: "Production LAN"
    vlan_id: 100
    subnet: "********/24"
    gateway: "********"
    dns_servers: ["********", "********"]
    purpose: "Client connections, EBO services, management"
    bandwidth_requirement: "1Gbps"
    
    # Server connections
    servers:
      primary:
        hostname: "EBO-PRIMARY-01"
        ip_address: "********0"
        mac_address: "00:1A:2B:3C:4D:10"
        interface: "NIC1 (Primary)"
        
      secondary:
        hostname: "EBO-SECONDARY-01"
        ip_address: "*********"
        mac_address: "00:1A:2B:3C:4D:11"
        interface: "NIC1 (Primary)"
        
      virtual_ip:
        ip_address: "**********"
        purpose: "Client connection point"
        failover_method: "IP takeover"
    
    # Client PC connections
    client_pcs:
      - name: "EBO-Workstation-01"
        ip_address: "*********"
        connection_target: "**********"  # Virtual IP
      - name: "EBO-Workstation-02"
        ip_address: "*********"
        connection_target: "**********"
      - name: "EBO-Workstation-03"
        ip_address: "*********"
        connection_target: "**********"
      - name: "EBO-Workstation-04"
        ip_address: "*********"
        connection_target: "**********"
      - name: "EBO-Workstation-05"
        ip_address: "*********"
        connection_target: "**********"

  # Dedicated Heartbeat Network (Recommended)
  heartbeat_network:
    name: "Heartbeat/Cluster Network"
    vlan_id: 200
    subnet: "*************/24"
    purpose: "Server-to-server heartbeat, cluster communication"
    bandwidth_requirement: "100Mbps"
    isolation: "Dedicated VLAN or physical network"
    
    servers:
      primary:
        ip_address: "**************"
        interface: "NIC2 (Heartbeat)"
      secondary:
        ip_address: "**************"
        interface: "NIC2 (Heartbeat)"

  # Management Network (Optional)
  management_network:
    name: "Management Network"
    vlan_id: 300
    subnet: "********/24"
    purpose: "Server management, monitoring, backup"
    bandwidth_requirement: "100Mbps"
    
    servers:
      primary:
        ip_address: "*********"
        interface: "iDRAC/IPMI"
      secondary:
        ip_address: "*********"
        interface: "iDRAC/IPMI"

# ============================================================================
# HEARTBEAT CONFIGURATION
# ============================================================================
heartbeat_configuration:
  description: "Heartbeat mechanism for server health monitoring"
  
  # Primary heartbeat settings
  primary_heartbeat:
    interval: 5                    # Send heartbeat every 5 seconds
    timeout: 15                    # Consider failed after 15 seconds
    retry_count: 3                 # Retry 3 times before declaring failure
    method: "UDP multicast"        # Heartbeat method
    port: 5405                     # Heartbeat port
    
  # Heartbeat types
  heartbeat_types:
    network_ping:
      enabled: true
      interval: 5
      timeout: 3
      target_ips: ["**************", "*********"]
      
    service_heartbeat:
      enabled: true
      interval: 10
      services:
        - "EcoStruxure Building Operation Enterprise Server"
        - "EcoStruxure Building Operation Database Service"
        - "EcoStruxure Building Operation Web Service"
        
    database_heartbeat:
      enabled: true
      interval: 15
      connection_string: "Server={server_ip},1433;Database=EBO_Database;Integrated Security=true"
      query: "SELECT 1"
      
    application_heartbeat:
      enabled: true
      interval: 30
      http_endpoints:
        - "http://********0/EBO/health"
        - "http://*********/EBO/health"

  # Failover triggers
  failover_triggers:
    network_failure:
      consecutive_failures: 3      # 3 consecutive ping failures
      time_window: 15             # Within 15 seconds
      
    service_failure:
      consecutive_failures: 2      # 2 consecutive service check failures
      time_window: 20             # Within 20 seconds
      
    database_failure:
      consecutive_failures: 2      # 2 consecutive database failures
      time_window: 30             # Within 30 seconds
      
    application_failure:
      consecutive_failures: 3      # 3 consecutive HTTP failures
      time_window: 90             # Within 90 seconds

# ============================================================================
# NETWORK PORTS & PROTOCOLS
# ============================================================================
network_ports:
  description: "Required network ports for EBO redundancy system"
  
  # EBO Application Ports
  ebo_ports:
    web_interface: 80             # EBO Web interface
    https_interface: 443          # EBO HTTPS interface
    license_server: 1947          # EBO License server
    automation_server: 8080       # EBO Automation server
    database_port: 1433           # SQL Server database
    
  # Redundancy System Ports
  redundancy_ports:
    web_management: 5001          # Redundancy web interface
    api_interface: 5002           # REST API interface
    heartbeat_udp: 5405           # Heartbeat communication
    cluster_tcp: 5406             # Cluster management
    monitoring: 5407              # Monitoring interface
    
  # System Management Ports
  management_ports:
    ssh: 22                       # SSH access (if Linux)
    rdp: 3389                     # Remote Desktop (Windows)
    snmp: 161                     # SNMP monitoring
    wmi: 135                      # WMI (Windows Management)
    
  # Database Replication Ports
  database_ports:
    sql_server: 1433              # SQL Server main port
    sql_browser: 1434             # SQL Server Browser
    sql_replication: 1435         # SQL Server replication
    
# ============================================================================
# FIREWALL RULES
# ============================================================================
firewall_rules:
  description: "Required firewall rules for EBO redundancy"
  
  # Inbound rules (allow these connections TO the servers)
  inbound_rules:
    - name: "EBO Web Interface"
      protocol: "TCP"
      port: 80
      source: "********/24"        # Client network
      
    - name: "EBO HTTPS Interface"
      protocol: "TCP"
      port: 443
      source: "********/24"
      
    - name: "EBO License Server"
      protocol: "TCP"
      port: 1947
      source: "********/24"
      
    - name: "SQL Server Database"
      protocol: "TCP"
      port: 1433
      source: "********0,*********"  # Between servers only
      
    - name: "Redundancy Web Management"
      protocol: "TCP"
      port: 5001
      source: "********/24"
      
    - name: "Heartbeat Communication"
      protocol: "UDP"
      port: 5405
      source: "*************/24"    # Heartbeat network
      
    - name: "Cluster Management"
      protocol: "TCP"
      port: 5406
      source: "********0,*********"
      
    - name: "Remote Desktop"
      protocol: "TCP"
      port: 3389
      source: "********/24"         # Management network
      
  # Outbound rules (allow these connections FROM the servers)
  outbound_rules:
    - name: "DNS Resolution"
      protocol: "UDP"
      port: 53
      destination: "********,********"
      
    - name: "NTP Time Sync"
      protocol: "UDP"
      port: 123
      destination: "any"
      
    - name: "Email Alerts (SMTP)"
      protocol: "TCP"
      port: 587
      destination: "mail.company.com"
      
    - name: "Heartbeat to Peer"
      protocol: "UDP"
      port: 5405
      destination: "*************/24"

# ============================================================================
# NETWORK QUALITY REQUIREMENTS
# ============================================================================
network_quality:
  description: "Network performance requirements"
  
  latency:
    server_to_server: "< 5ms"      # Between Dell PowerEdge servers
    client_to_server: "< 20ms"     # Client PCs to servers
    database_replication: "< 2ms"   # For synchronous replication
    
  bandwidth:
    minimum_total: "100Mbps"       # Minimum total bandwidth
    recommended_total: "1Gbps"     # Recommended total bandwidth
    heartbeat_dedicated: "10Mbps"  # Dedicated for heartbeat
    database_replication: "50Mbps" # For database sync
    
  availability:
    network_uptime: "99.9%"        # Network availability requirement
    switch_redundancy: "required"   # Redundant network switches
    cable_redundancy: "recommended" # Redundant network cables
    
  packet_loss:
    maximum_acceptable: "0.1%"     # Maximum packet loss
    heartbeat_network: "0.01%"     # Heartbeat network packet loss

# ============================================================================
# NETWORK MONITORING
# ============================================================================
network_monitoring:
  description: "Network monitoring and alerting"
  
  # Monitoring targets
  monitoring_targets:
    - target: "********0"          # Primary server
      type: "ping"
      interval: 5
      
    - target: "*********"          # Secondary server
      type: "ping"
      interval: 5
      
    - target: "**************"     # Primary heartbeat
      type: "ping"
      interval: 2
      
    - target: "**************"     # Secondary heartbeat
      type: "ping"
      interval: 2
      
    - target: "**********"         # Virtual IP
      type: "ping"
      interval: 10
      
  # Performance monitoring
  performance_monitoring:
    bandwidth_utilization:
      threshold: 80               # Alert if > 80% utilization
      interval: 60               # Check every minute
      
    latency_monitoring:
      threshold: 10              # Alert if > 10ms latency
      interval: 30               # Check every 30 seconds
      
    packet_loss_monitoring:
      threshold: 0.5             # Alert if > 0.5% packet loss
      interval: 60               # Check every minute

# ============================================================================
# REDUNDANCY NETWORK SCENARIOS
# ============================================================================
redundancy_scenarios:
  description: "Network failure scenarios and responses"
  
  # Scenario 1: Primary network failure
  primary_network_failure:
    detection: "Ping failure to primary server on production network"
    response: "Switch to heartbeat network for cluster communication"
    failover_trigger: "3 consecutive ping failures"
    recovery_action: "Monitor for primary network restoration"
    
  # Scenario 2: Heartbeat network failure
  heartbeat_network_failure:
    detection: "Heartbeat communication lost"
    response: "Use production network for cluster communication"
    failover_trigger: "5 consecutive heartbeat failures"
    recovery_action: "Continue monitoring via production network"
    
  # Scenario 3: Complete network isolation
  network_isolation:
    detection: "Both networks unreachable"
    response: "Assume split-brain scenario"
    failover_trigger: "Immediate"
    recovery_action: "Manual intervention required"
    
  # Scenario 4: Client network failure
  client_network_failure:
    detection: "Clients cannot reach virtual IP"
    response: "Investigate network infrastructure"
    failover_trigger: "Multiple client connection failures"
    recovery_action: "Restore client network connectivity"

# ============================================================================
# NETWORK SECURITY
# ============================================================================
network_security:
  description: "Network security requirements"
  
  # VLAN isolation
  vlan_isolation:
    production_vlan: 100
    heartbeat_vlan: 200
    management_vlan: 300
    isolation_required: true
    
  # Access control
  access_control:
    server_to_server: "full_access"
    client_to_server: "ebo_ports_only"
    management_to_server: "management_ports_only"
    internet_to_server: "deny_all"
    
  # Encryption
  encryption:
    database_replication: "TLS 1.2"
    web_interface: "HTTPS"
    management_traffic: "SSH/RDP"
    heartbeat_traffic: "optional"

# ============================================================================
# IMPLEMENTATION CHECKLIST
# ============================================================================
implementation_checklist:
  description: "Network implementation checklist"
  
  pre_deployment:
    - "Verify network infrastructure capacity"
    - "Configure VLANs and subnets"
    - "Set up redundant network switches"
    - "Configure firewall rules"
    - "Test network connectivity between servers"
    - "Verify DNS resolution"
    - "Configure NTP time synchronization"
    
  deployment:
    - "Configure server network interfaces"
    - "Set up virtual IP address"
    - "Configure heartbeat network"
    - "Test failover scenarios"
    - "Verify client connectivity"
    - "Configure monitoring and alerting"
    
  post_deployment:
    - "Monitor network performance"
    - "Test regular failover procedures"
    - "Review network logs"
    - "Update documentation"
    - "Train operations team"
