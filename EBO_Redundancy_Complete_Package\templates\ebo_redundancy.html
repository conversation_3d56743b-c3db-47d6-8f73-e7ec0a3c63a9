{% extends "redundancy_base.html" %}

{% block title %}EBO Redundancy - Professional Redundancy Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="fas fa-building me-2 text-primary"></i>
                EcoStruxure Building Operation (EBO) Redundancy
                <small class="text-muted ms-2">Dell PowerEdge R750xa Cluster</small>
            </h1>
            <div>
                <button class="btn btn-success me-2" id="startEBOMonitoring">
                    <i class="fas fa-play me-1"></i>Start Monitoring
                </button>
                <button class="btn btn-warning me-2" id="manualEBOFailover">
                    <i class="fas fa-exchange-alt me-1"></i>Manual Failover
                </button>
                <button class="btn btn-info" id="refreshEBOStatus">
                    <i class="fas fa-sync me-1"></i>Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- EBO System Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-server fa-2x text-primary mb-2"></i>
                <div class="metric-value text-primary" id="systemStatus">-</div>
                <div class="metric-label">System Status</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-database fa-2x text-success mb-2"></i>
                <div class="metric-value text-success" id="databaseStatus">-</div>
                <div class="metric-label">Database Status</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-users fa-2x text-info mb-2"></i>
                <div class="metric-value text-info" id="clientConnections">5</div>
                <div class="metric-label">Client PCs</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-key fa-2x text-warning mb-2"></i>
                <div class="metric-value text-warning" id="licenseStatus">-</div>
                <div class="metric-label">License Server</div>
            </div>
        </div>
    </div>
</div>

<!-- Dell PowerEdge Servers -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server me-2 text-primary"></i>
                    Primary Server - DELL-R750XA-01
                    <span class="badge bg-primary ms-2" id="primaryStatus">PRIMARY</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Server Details:</strong><br>
                        <small class="text-muted">
                            IP: <span id="primaryIP">************</span><br>
                            Model: Dell PowerEdge R750xa<br>
                            Role: <span id="primaryRole">Primary Enterprise Server</span>
                        </small>
                    </div>
                    <div class="col-md-6">
                        <strong>EBO Services:</strong><br>
                        <div id="primaryServices" class="mt-2">
                            <div class="service-status">
                                <i class="fas fa-circle text-secondary me-1"></i>
                                <small>Loading services...</small>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="metric-value-small" id="primaryDatabase">-</div>
                            <div class="metric-label-small">Database</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="metric-value-small" id="primaryWeb">-</div>
                            <div class="metric-label-small">Web Interface</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="metric-value-small" id="primaryLicense">-</div>
                            <div class="metric-label-small">License Server</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server me-2 text-secondary"></i>
                    Secondary Server - DELL-R750XA-02
                    <span class="badge bg-secondary ms-2" id="secondaryStatus">SECONDARY</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Server Details:</strong><br>
                        <small class="text-muted">
                            IP: <span id="secondaryIP">************</span><br>
                            Model: Dell PowerEdge R750xa<br>
                            Role: <span id="secondaryRole">Secondary Enterprise Server</span>
                        </small>
                    </div>
                    <div class="col-md-6">
                        <strong>EBO Services:</strong><br>
                        <div id="secondaryServices" class="mt-2">
                            <div class="service-status">
                                <i class="fas fa-circle text-secondary me-1"></i>
                                <small>Loading services...</small>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="metric-value-small" id="secondaryDatabase">-</div>
                            <div class="metric-label-small">Database</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="metric-value-small" id="secondaryWeb">-</div>
                            <div class="metric-label-small">Web Interface</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="metric-value-small" id="secondaryLicense">-</div>
                            <div class="metric-label-small">License Server</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- EBO Database Cluster -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database me-2"></i>
                    EBO Database Cluster (SQL Server)
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-database fa-2x text-primary mb-2"></i>
                            <div class="metric-value text-primary" id="dbClusterStatus">-</div>
                            <div class="metric-label">Cluster Status</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-sync fa-2x text-info mb-2"></i>
                            <div class="metric-value text-info" id="dbReplicationStatus">-</div>
                            <div class="metric-label">Replication</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-hdd fa-2x text-success mb-2"></i>
                            <div class="metric-value text-success" id="dbStorageStatus">-</div>
                            <div class="metric-label">Storage</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Client PC Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-desktop me-2"></i>
                    Client PC Connections
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="clientPCStatus">
                    <div class="col-md-12 text-center">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">Loading client PC status...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- EBO Storage Redundancy -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-hdd me-2"></i>
                    EBO Storage Redundancy
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-folder fa-2x text-primary mb-2"></i>
                                <h6>Application Data</h6>
                                <div class="metric-value-small text-primary" id="appStorageStatus">-</div>
                                <small class="text-muted">Program Files Sync</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-database fa-2x text-success mb-2"></i>
                                <h6>Database Files</h6>
                                <div class="metric-value-small text-success" id="dbStorageSyncStatus">-</div>
                                <small class="text-muted">Database Backup</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-cog fa-2x text-warning mb-2"></i>
                                <h6>Configuration</h6>
                                <div class="metric-value-small text-warning" id="configStorageStatus">-</div>
                                <small class="text-muted">Config & License</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let eboData = {};

$(document).ready(function() {
    loadEBOStatus();
    
    $('#startEBOMonitoring').click(startEBOMonitoring);
    $('#manualEBOFailover').click(manualEBOFailover);
    $('#refreshEBOStatus').click(loadEBOStatus);
    
    // Auto-refresh every 30 seconds
    setInterval(loadEBOStatus, 30000);
});

function loadEBOStatus() {
    $.get('/api/ebo-status')
        .done(function(response) {
            if (response.success) {
                eboData = response.data;
                displayEBOStatus(response.data);
            } else {
                showAlert('danger', 'Failed to load EBO status: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to connect to EBO redundancy system');
        });
}

function displayEBOStatus(data) {
    // Update system overview
    $('#systemStatus').text(data.overall_status.toUpperCase());
    $('#systemStatus').removeClass().addClass('metric-value').addClass(getStatusClass(data.overall_status));
    
    // Update primary server
    if (data.primary_server) {
        $('#primaryIP').text(data.primary_server.ip);
        displayServerServices('primary', data.primary_server.services);
        $('#primaryDatabase').text(data.primary_server.database ? 'ONLINE' : 'OFFLINE');
        $('#primaryDatabase').removeClass().addClass('metric-value-small').addClass(data.primary_server.database ? 'text-success' : 'text-danger');
        $('#primaryWeb').text(data.primary_server.web_interface ? 'ONLINE' : 'OFFLINE');
        $('#primaryWeb').removeClass().addClass('metric-value-small').addClass(data.primary_server.web_interface ? 'text-success' : 'text-danger');
        $('#primaryLicense').text(data.primary_server.license_server ? 'ONLINE' : 'OFFLINE');
        $('#primaryLicense').removeClass().addClass('metric-value-small').addClass(data.primary_server.license_server ? 'text-success' : 'text-danger');
    }
    
    // Update secondary server
    if (data.secondary_server) {
        $('#secondaryIP').text(data.secondary_server.ip);
        displayServerServices('secondary', data.secondary_server.services);
        $('#secondaryDatabase').text(data.secondary_server.database ? 'ONLINE' : 'OFFLINE');
        $('#secondaryDatabase').removeClass().addClass('metric-value-small').addClass(data.secondary_server.database ? 'text-success' : 'text-danger');
        $('#secondaryWeb').text(data.secondary_server.web_interface ? 'ONLINE' : 'OFFLINE');
        $('#secondaryWeb').removeClass().addClass('metric-value-small').addClass(data.secondary_server.web_interface ? 'text-success' : 'text-danger');
        $('#secondaryLicense').text(data.secondary_server.license_server ? 'ONLINE' : 'OFFLINE');
        $('#secondaryLicense').removeClass().addClass('metric-value-small').addClass(data.secondary_server.license_server ? 'text-success' : 'text-danger');
    }
    
    // Update database status
    const dbHealthy = (data.primary_server?.database || data.secondary_server?.database);
    $('#databaseStatus').text(dbHealthy ? 'HEALTHY' : 'OFFLINE');
    $('#databaseStatus').removeClass().addClass('metric-value').addClass(dbHealthy ? 'text-success' : 'text-danger');
    
    // Update license status
    const licenseHealthy = (data.primary_server?.license_server || data.secondary_server?.license_server);
    $('#licenseStatus').text(licenseHealthy ? 'ACTIVE' : 'OFFLINE');
    $('#licenseStatus').removeClass().addClass('metric-value').addClass(licenseHealthy ? 'text-success' : 'text-danger');
    
    // Update client PC status
    displayClientPCStatus();
    
    // Update storage status
    updateStorageStatus();
}

function displayServerServices(serverType, services) {
    let html = '';
    
    if (services) {
        Object.entries(services).forEach(([serviceName, isRunning]) => {
            const statusClass = isRunning ? 'text-success' : 'text-danger';
            const statusIcon = isRunning ? 'fas fa-check-circle' : 'fas fa-times-circle';
            const shortName = serviceName.replace('EcoStruxure Building Operation ', '');
            
            html += `
                <div class="service-status mb-1">
                    <i class="${statusIcon} ${statusClass} me-1"></i>
                    <small>${shortName}</small>
                </div>
            `;
        });
    } else {
        html = '<div class="service-status"><i class="fas fa-question-circle text-secondary me-1"></i><small>Unknown</small></div>';
    }
    
    $(`#${serverType}Services`).html(html);
}

function displayClientPCStatus() {
    const clientPCs = [
        { name: 'EBO-Client-01', ip: '************', status: 'connected' },
        { name: 'EBO-Client-02', ip: '************', status: 'connected' },
        { name: 'EBO-Client-03', ip: '************', status: 'connected' },
        { name: 'EBO-Client-04', ip: '************', status: 'disconnected' },
        { name: 'EBO-Client-05', ip: '************', status: 'connected' }
    ];
    
    let html = '';
    
    clientPCs.forEach(client => {
        const statusClass = client.status === 'connected' ? 'text-success' : 'text-danger';
        const statusIcon = client.status === 'connected' ? 'fas fa-check-circle' : 'fas fa-times-circle';
        
        html += `
            <div class="col-md-2 mb-3">
                <div class="text-center">
                    <i class="fas fa-desktop fa-2x ${statusClass} mb-2"></i>
                    <div class="small">
                        <strong>${client.name}</strong><br>
                        <span class="text-muted">${client.ip}</span><br>
                        <i class="${statusIcon} ${statusClass} me-1"></i>
                        <span class="${statusClass}">${client.status.toUpperCase()}</span>
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#clientPCStatus').html(html);
}

function updateStorageStatus() {
    // Simulate storage status - in real implementation, this would come from the storage redundancy manager
    $('#appStorageStatus').text('SYNCED').removeClass().addClass('metric-value-small text-success');
    $('#dbStorageSyncStatus').text('SYNCED').removeClass().addClass('metric-value-small text-success');
    $('#configStorageStatus').text('SYNCED').removeClass().addClass('metric-value-small text-success');
    
    $('#dbClusterStatus').text('HEALTHY').removeClass().addClass('metric-value text-success');
    $('#dbReplicationStatus').text('ACTIVE').removeClass().addClass('metric-value text-success');
    $('#dbStorageStatus').text('SYNCED').removeClass().addClass('metric-value text-success');
}

function startEBOMonitoring() {
    $.post('/api/ebo-monitoring/start')
        .done(function(response) {
            if (response.success) {
                showAlert('success', 'EBO monitoring started successfully');
                $('#startEBOMonitoring').prop('disabled', true).text('Monitoring Active');
                loadEBOStatus();
            } else {
                showAlert('danger', 'Failed to start EBO monitoring: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to start EBO monitoring');
        });
}

function manualEBOFailover() {
    if (!confirm('Are you sure you want to perform manual EBO failover?\n\nThis will switch the primary and secondary servers and may cause a brief service interruption.')) {
        return;
    }
    
    showAlert('info', 'Initiating EBO failover...');
    
    $.post('/api/ebo-failover/manual')
        .done(function(response) {
            if (response.success) {
                showAlert('success', 'EBO failover completed successfully');
                
                // Swap primary and secondary badges
                const primaryBadge = $('#primaryStatus');
                const secondaryBadge = $('#secondaryStatus');
                
                primaryBadge.removeClass('bg-primary').addClass('bg-secondary').text('SECONDARY');
                secondaryBadge.removeClass('bg-secondary').addClass('bg-primary').text('PRIMARY');
                
                loadEBOStatus();
            } else {
                showAlert('danger', 'EBO failover failed: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to initiate EBO failover');
        });
}

function getStatusClass(status) {
    switch(status.toLowerCase()) {
        case 'healthy': return 'text-success';
        case 'degraded': return 'text-warning';
        case 'failed': return 'text-danger';
        default: return 'text-secondary';
    }
}
</script>

<style>
.metric-value-small {
    font-size: 1.2rem;
    font-weight: bold;
}

.metric-label-small {
    font-size: 0.8rem;
    color: #6c757d;
}

.service-status {
    font-size: 0.85rem;
}

.card.border-primary {
    border-width: 2px !important;
}

.card.border-success {
    border-width: 2px !important;
}

.card.border-warning {
    border-width: 2px !important;
}
</style>
{% endblock %}
