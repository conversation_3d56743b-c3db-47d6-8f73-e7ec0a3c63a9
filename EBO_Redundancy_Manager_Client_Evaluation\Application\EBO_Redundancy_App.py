#!/usr/bin/env python3
"""
EBO Redundancy Manager - Standalone Application
Professional Windows Application for EBO Redundancy Management
No installation required - just run this file!
"""

import sys
import os
import threading
import time
import webbrowser
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import json

class EBORedundancyApp:
    """Main EBO Redundancy Manager Application"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("EBO Redundancy Manager - Professional Edition")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # Try to set icon
        try:
            self.root.iconbitmap("app.ico")
        except:
            pass
        
        self.service_running = False
        self.web_thread = None
        self.app_dir = Path(__file__).parent
        
        # Ensure required directories exist
        self.ensure_directories()
        
        self.create_gui()
        
    def ensure_directories(self):
        """Ensure required directories exist"""
        dirs = ['logs', 'config', 'data', 'backup']
        for dir_name in dirs:
            (self.app_dir / dir_name).mkdir(exist_ok=True)
    
    def create_gui(self):
        """Create the main GUI interface"""
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Main notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control tab
        self.create_control_tab(notebook)
        
        # Configuration tab
        self.create_config_tab(notebook)
        
        # Monitoring tab
        self.create_monitoring_tab(notebook)
        
        # Logs tab
        self.create_logs_tab(notebook)
        
        # Status bar
        self.status_bar = ttk.Label(self.root, text="Ready - Click 'Start Service' to begin", 
                                   relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Start status update
        self.update_status()
    
    def create_control_tab(self, notebook):
        """Create the main control tab"""
        control_frame = ttk.Frame(notebook)
        notebook.add(control_frame, text="Service Control")
        
        # Main content frame
        main_frame = ttk.Frame(control_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="EBO Redundancy Manager", 
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        subtitle_label = ttk.Label(main_frame, text="Professional EcoStruxure Building Operation Redundancy System", 
                                  font=("Arial", 10))
        subtitle_label.pack(pady=(0, 30))
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="System Status", padding="15")
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        status_inner = ttk.Frame(status_frame)
        status_inner.pack(fill=tk.X)
        
        ttk.Label(status_inner, text="Service Status:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.status_label = ttk.Label(status_inner, text="Stopped", 
                                     foreground="red", font=("Arial", 10, "bold"))
        self.status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Control buttons frame
        control_frame_inner = ttk.LabelFrame(main_frame, text="Service Control", padding="15")
        control_frame_inner.pack(fill=tk.X, pady=(0, 20))
        
        button_frame = ttk.Frame(control_frame_inner)
        button_frame.pack()
        
        self.start_button = ttk.Button(button_frame, text="🚀 Start Service", 
                                      command=self.start_service, width=20)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="⏹️ Stop Service", 
                                     command=self.stop_service, width=20, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.restart_button = ttk.Button(button_frame, text="🔄 Restart Service", 
                                        command=self.restart_service, width=20, state="disabled")
        self.restart_button.pack(side=tk.LEFT)
        
        # Web interface frame
        web_frame = ttk.LabelFrame(main_frame, text="Web Interface Access", padding="15")
        web_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(web_frame, text="Access the professional web interface:", 
                 font=("Arial", 10, "bold")).pack(anchor=tk.W)
        
        url_frame = ttk.Frame(web_frame)
        url_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.url_label = ttk.Label(url_frame, text="http://localhost:5001", 
                                  foreground="blue", cursor="hand2", font=("Arial", 10, "underline"))
        self.url_label.pack(side=tk.LEFT)
        self.url_label.bind("<Button-1>", self.open_web_interface)
        
        # Web interface buttons
        web_buttons_frame = ttk.Frame(web_frame)
        web_buttons_frame.pack(fill=tk.X, pady=(15, 0))
        
        buttons = [
            ("🏠 Dashboard", "http://localhost:5001"),
            ("🏢 EBO Redundancy", "http://localhost:5001/ebo-redundancy"),
            ("📱 Applications", "http://localhost:5001/applications"),
            ("💾 Storage", "http://localhost:5001/storage-redundancy"),
            ("🗄️ Database", "http://localhost:5001/database-clusters")
        ]
        
        for i, (text, url) in enumerate(buttons):
            btn = ttk.Button(web_buttons_frame, text=text, 
                           command=lambda u=url: self.open_url(u), width=18)
            btn.grid(row=i//3, column=i%3, padx=5, pady=5, sticky=tk.W)
        
        # Quick info frame
        info_frame = ttk.LabelFrame(main_frame, text="Quick Information", padding="15")
        info_frame.pack(fill=tk.BOTH, expand=True)
        
        info_text = """
🎯 Features Available:
• Real-time EBO service monitoring
• Automatic failover between Dell PowerEdge servers
• Database redundancy and replication
• Storage synchronization and backup
• Client PC connection management
• Professional web-based management interface

🚀 Getting Started:
1. Click 'Start Service' to begin monitoring
2. Access the web interface for full configuration
3. Configure your Dell PowerEdge server details
4. Set up your EBO installation paths
5. Add your client PC connections

📊 Web Interface Sections:
• Dashboard: System overview and status
• EBO Redundancy: Specialized EBO monitoring
• Applications: Application redundancy management
• Storage: File and database backup
• Database: SQL Server cluster management
        """
        
        info_display = tk.Text(info_frame, wrap=tk.WORD, height=12, font=("Consolas", 9))
        info_display.pack(fill=tk.BOTH, expand=True)
        info_display.insert(tk.END, info_text.strip())
        info_display.config(state=tk.DISABLED)
    
    def create_config_tab(self, notebook):
        """Create configuration tab"""
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="Configuration")
        
        main_frame = ttk.Frame(config_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(main_frame, text="System Configuration", 
                 font=("Arial", 16, "bold")).pack(pady=(0, 20))
        
        # Configuration buttons
        config_buttons_frame = ttk.LabelFrame(main_frame, text="Configuration Management", padding="15")
        config_buttons_frame.pack(fill=tk.X, pady=(0, 20))
        
        button_frame = ttk.Frame(config_buttons_frame)
        button_frame.pack()
        
        ttk.Button(button_frame, text="📝 Edit EBO Config", 
                  command=self.edit_ebo_config, width=20).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="🗄️ Database Config", 
                  command=self.edit_db_config, width=20).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="💾 Storage Config", 
                  command=self.edit_storage_config, width=20).pack(side=tk.LEFT)
        
        # Quick configuration form
        quick_config_frame = ttk.LabelFrame(main_frame, text="Quick Setup", padding="15")
        quick_config_frame.pack(fill=tk.BOTH, expand=True)
        
        # Server configuration
        server_frame = ttk.LabelFrame(quick_config_frame, text="Server Configuration", padding="10")
        server_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Primary server
        ttk.Label(server_frame, text="Primary Server IP:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.primary_ip = ttk.Entry(server_frame, width=20)
        self.primary_ip.grid(row=0, column=1, padx=(0, 20))
        self.primary_ip.insert(0, "************")
        
        ttk.Label(server_frame, text="Secondary Server IP:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.secondary_ip = ttk.Entry(server_frame, width=20)
        self.secondary_ip.grid(row=0, column=3)
        self.secondary_ip.insert(0, "************")
        
        # Virtual IP
        ttk.Label(server_frame, text="Virtual IP:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.virtual_ip = ttk.Entry(server_frame, width=20)
        self.virtual_ip.grid(row=1, column=1, pady=(10, 0))
        self.virtual_ip.insert(0, "*************")
        
        # EBO paths
        ebo_frame = ttk.LabelFrame(quick_config_frame, text="EBO Installation Paths", padding="10")
        ebo_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(ebo_frame, text="EBO Installation:").grid(row=0, column=0, sticky=tk.W)
        self.ebo_path = ttk.Entry(ebo_frame, width=60)
        self.ebo_path.grid(row=0, column=1, padx=(10, 0), sticky=tk.W+tk.E)
        self.ebo_path.insert(0, "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation")
        
        ttk.Label(ebo_frame, text="EBO Data Path:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.ebo_data_path = ttk.Entry(ebo_frame, width=60)
        self.ebo_data_path.grid(row=1, column=1, padx=(10, 0), pady=(10, 0), sticky=tk.W+tk.E)
        self.ebo_data_path.insert(0, "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation")
        
        ebo_frame.columnconfigure(1, weight=1)
        
        # Save button
        ttk.Button(quick_config_frame, text="💾 Save Configuration", 
                  command=self.save_quick_config, width=25).pack(pady=20)
    
    def create_monitoring_tab(self, notebook):
        """Create monitoring tab"""
        monitor_frame = ttk.Frame(notebook)
        notebook.add(monitor_frame, text="System Monitoring")
        
        main_frame = ttk.Frame(monitor_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(main_frame, text="System Monitoring", 
                 font=("Arial", 16, "bold")).pack(pady=(0, 20))
        
        # Monitoring status
        status_frame = ttk.LabelFrame(main_frame, text="Current Status", padding="15")
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.monitor_text = scrolledtext.ScrolledText(status_frame, height=8, font=("Consolas", 9))
        self.monitor_text.pack(fill=tk.BOTH, expand=True)
        
        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Button(control_frame, text="🔄 Refresh Status", 
                  command=self.refresh_monitoring).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🚨 Test Failover", 
                  command=self.test_failover).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="📊 Performance", 
                  command=self.show_performance).pack(side=tk.LEFT)
        
        # Initialize monitoring display
        self.update_monitoring_display()
    
    def create_logs_tab(self, notebook):
        """Create logs tab"""
        logs_frame = ttk.Frame(notebook)
        notebook.add(logs_frame, text="System Logs")
        
        main_frame = ttk.Frame(logs_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(main_frame, text="System Logs", 
                 font=("Arial", 16, "bold")).pack(pady=(0, 20))
        
        # Log controls
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(control_frame, text="🔄 Refresh", 
                  command=self.refresh_logs).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🗑️ Clear", 
                  command=self.clear_logs).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="📁 Open Log Folder", 
                  command=self.open_log_folder).pack(side=tk.LEFT)
        
        # Log display
        log_frame = ttk.LabelFrame(main_frame, text="Live System Log", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Initialize with welcome message
        self.log_message("EBO Redundancy Manager started")
        self.log_message("Ready for configuration and monitoring")
    
    def start_service(self):
        """Start the redundancy service"""
        try:
            self.log_message("🚀 Starting EBO Redundancy Service...")
            
            # Start web service in separate thread
            self.web_thread = threading.Thread(target=self.run_web_service, daemon=True)
            self.web_thread.start()
            
            # Give it a moment to start
            time.sleep(2)
            
            self.service_running = True
            self.update_button_states()
            self.log_message("✅ EBO Redundancy Service started successfully")
            self.log_message("🌐 Web interface available at: http://localhost:5001")
            self.status_bar.config(text="Service Running - Web Interface: http://localhost:5001")
            
            # Update monitoring
            self.update_monitoring_display()
            
        except Exception as e:
            self.log_message(f"❌ Failed to start service: {e}")
            messagebox.showerror("Error", f"Failed to start service: {e}")
    
    def stop_service(self):
        """Stop the redundancy service"""
        try:
            self.log_message("⏹️ Stopping EBO Redundancy Service...")
            self.service_running = False
            self.update_button_states()
            self.log_message("✅ EBO Redundancy Service stopped")
            self.status_bar.config(text="Service Stopped")
            
        except Exception as e:
            self.log_message(f"❌ Failed to stop service: {e}")
    
    def restart_service(self):
        """Restart the redundancy service"""
        self.log_message("🔄 Restarting EBO Redundancy Service...")
        self.stop_service()
        time.sleep(2)
        self.start_service()
    
    def run_web_service(self):
        """Run the web service"""
        try:
            # Import the web application
            from redundancy_web_ui import app, initialize_manager
            
            # Initialize managers
            initialize_manager()
            
            # Run the Flask app
            app.run(host='0.0.0.0', port=5001, debug=False, use_reloader=False)
            
        except Exception as e:
            self.log_message(f"❌ Web service error: {e}")
    
    def update_button_states(self):
        """Update button states based on service status"""
        if self.service_running:
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
            self.restart_button.config(state="normal")
            self.status_label.config(text="Running", foreground="green")
        else:
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.restart_button.config(state="disabled")
            self.status_label.config(text="Stopped", foreground="red")
    
    def open_web_interface(self, event=None):
        """Open the main web interface"""
        self.open_url("http://localhost:5001")
    
    def open_url(self, url):
        """Open URL in default browser"""
        if self.service_running:
            webbrowser.open(url)
            self.log_message(f"🌐 Opened: {url}")
        else:
            messagebox.showwarning("Service Not Running", 
                                 "Please start the service first before accessing the web interface.")
    
    def edit_ebo_config(self):
        """Edit EBO configuration"""
        config_file = self.app_dir / "ebo_redundancy_config.yaml"
        if config_file.exists():
            os.startfile(str(config_file))
            self.log_message(f"📝 Opened config file: {config_file}")
        else:
            messagebox.showinfo("Configuration", 
                              "EBO configuration file will be created when the service starts.")
    
    def edit_db_config(self):
        """Edit database configuration"""
        config_file = self.app_dir / "database_cluster_config.yaml"
        if config_file.exists():
            os.startfile(str(config_file))
        else:
            messagebox.showinfo("Configuration", 
                              "Database configuration file will be created when the service starts.")
    
    def edit_storage_config(self):
        """Edit storage configuration"""
        config_file = self.app_dir / "storage_redundancy_config.yaml"
        if config_file.exists():
            os.startfile(str(config_file))
        else:
            messagebox.showinfo("Configuration", 
                              "Storage configuration file will be created when the service starts.")
    
    def save_quick_config(self):
        """Save quick configuration"""
        try:
            # This would save the configuration to files
            self.log_message("💾 Configuration saved successfully")
            messagebox.showinfo("Configuration", "Configuration saved successfully!")
        except Exception as e:
            self.log_message(f"❌ Failed to save configuration: {e}")
            messagebox.showerror("Error", f"Failed to save configuration: {e}")
    
    def refresh_monitoring(self):
        """Refresh monitoring display"""
        self.update_monitoring_display()
        self.log_message("🔄 Monitoring status refreshed")
    
    def update_monitoring_display(self):
        """Update monitoring display"""
        status_text = f"""
EBO Redundancy System Status
{'='*50}

Service Status: {'Running' if self.service_running else 'Stopped'}
Web Interface: {'Available' if self.service_running else 'Unavailable'}
Last Update: {time.strftime('%Y-%m-%d %H:%M:%S')}

Primary Server: {self.primary_ip.get() if hasattr(self, 'primary_ip') else 'Not configured'}
Secondary Server: {self.secondary_ip.get() if hasattr(self, 'secondary_ip') else 'Not configured'}
Virtual IP: {self.virtual_ip.get() if hasattr(self, 'virtual_ip') else 'Not configured'}

EBO Services:
• Enterprise Server: {'Monitoring' if self.service_running else 'Not monitored'}
• Database Service: {'Monitoring' if self.service_running else 'Not monitored'}
• Web Service: {'Monitoring' if self.service_running else 'Not monitored'}
• License Server: {'Monitoring' if self.service_running else 'Not monitored'}

Database Redundancy: {'Active' if self.service_running else 'Inactive'}
Storage Synchronization: {'Active' if self.service_running else 'Inactive'}
Client PC Monitoring: {'Active' if self.service_running else 'Inactive'}

To access full monitoring features, use the web interface:
http://localhost:5001/ebo-redundancy
        """
        
        if hasattr(self, 'monitor_text'):
            self.monitor_text.delete(1.0, tk.END)
            self.monitor_text.insert(tk.END, status_text.strip())
    
    def test_failover(self):
        """Test failover functionality"""
        if not self.service_running:
            messagebox.showwarning("Service Not Running", "Please start the service first.")
            return
            
        result = messagebox.askyesno("Test Failover", 
                                   "This will test the failover functionality.\n\nProceed?")
        if result:
            self.log_message("🚨 Testing failover functionality...")
            # This would trigger actual failover test
            messagebox.showinfo("Failover Test", "Failover test completed. Check logs for details.")
    
    def show_performance(self):
        """Show performance metrics"""
        if self.service_running:
            self.open_url("http://localhost:5001/monitors")
        else:
            messagebox.showwarning("Service Not Running", "Please start the service first.")
    
    def refresh_logs(self):
        """Refresh log display"""
        self.log_message("🔄 Log display refreshed")
    
    def clear_logs(self):
        """Clear log display"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("🗑️ Log display cleared")
    
    def open_log_folder(self):
        """Open log folder"""
        log_dir = self.app_dir / "logs"
        if log_dir.exists():
            os.startfile(str(log_dir))
        else:
            messagebox.showinfo("Logs", "Log directory will be created when the service starts.")
    
    def log_message(self, message):
        """Add message to log display"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Keep only last 200 lines
        lines = self.log_text.get("1.0", tk.END).split("\n")
        if len(lines) > 200:
            self.log_text.delete("1.0", f"{len(lines)-200}.0")
    
    def update_status(self):
        """Update status periodically"""
        # Update monitoring display if on monitoring tab
        if hasattr(self, 'monitor_text'):
            self.update_monitoring_display()
        
        # Schedule next update
        self.root.after(10000, self.update_status)  # Update every 10 seconds
    
    def on_closing(self):
        """Handle application closing"""
        if self.service_running:
            result = messagebox.askyesno("Exit Application", 
                                       "The service is still running. Stop service and exit?")
            if result:
                self.stop_service()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """Run the application"""
        self.log_message("🚀 EBO Redundancy Manager started")
        self.log_message("📋 Ready for configuration and monitoring")
        
        # Handle window closing
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Start the GUI
        self.root.mainloop()

if __name__ == '__main__':
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Check required packages
    required_packages = ['flask', 'yaml', 'psutil', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install flask PyYAML psutil requests")
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Start the application
    app = EBORedundancyApp()
    app.run()
