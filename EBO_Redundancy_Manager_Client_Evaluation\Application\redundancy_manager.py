#!/usr/bin/env python3
"""
Professional Hardware Redundancy Management System
A production-ready solution for managing application redundancy and failover
"""

import os
import sys
import json
import yaml
import time
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.redundancy.monitors import DiskMonitor, NetworkMonitor, HTTPMonitor
from src.redundancy.utils.logger import setup_logger

class HealthStatus(Enum):
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    CRITICAL = "critical"

class RedundancyState(Enum):
    PRIMARY_ACTIVE = "primary_active"
    SECONDARY_ACTIVE = "secondary_active"
    BOTH_ACTIVE = "both_active"
    BOTH_FAILED = "both_failed"
    FAILOVER_IN_PROGRESS = "failover_in_progress"

@dataclass
class ApplicationConfig:
    """Configuration for a monitored application"""
    name: str
    description: str
    priority: int  # 1=highest, 10=lowest
    is_primary: bool
    monitors: List[Dict[str, Any]]
    failover_target: Optional[str] = None
    auto_failover: bool = True
    failover_threshold: int = 3  # consecutive failures before failover
    recovery_threshold: int = 2  # consecutive successes before recovery

@dataclass
class MonitorResult:
    """Result of a monitor check"""
    monitor_name: str
    application: str
    status: HealthStatus
    message: str
    timestamp: datetime
    response_time: Optional[float] = None
    details: Optional[Dict[str, Any]] = None

@dataclass
class RedundancyEvent:
    """Redundancy system event"""
    timestamp: datetime
    event_type: str
    application: str
    description: str
    severity: str
    details: Optional[Dict[str, Any]] = None

class RedundancyManager:
    """Main redundancy management system"""

    def __init__(self, config_file: str = "redundancy_config.yaml"):
        self.config_file = config_file
        self.applications: Dict[str, ApplicationConfig] = {}
        self.monitor_instances: Dict[str, Any] = {}
        self.monitor_results: Dict[str, List[MonitorResult]] = {}
        self.events: List[RedundancyEvent] = []
        self.current_state: Dict[str, RedundancyState] = {}
        self.failure_counts: Dict[str, int] = {}
        self.success_counts: Dict[str, int] = {}

        # Threading
        self.monitoring_active = False
        self.monitoring_thread = None
        self.lock = threading.Lock()

        # Logging
        self.logger = setup_logger('RedundancyManager', log_to_file=True,
                                 log_file='logs/redundancy_manager.log')

        # Load configuration
        self.load_configuration()

    def load_configuration(self) -> bool:
        """Load configuration from file"""
        try:
            if not os.path.exists(self.config_file):
                self.logger.warning(f"Configuration file {self.config_file} not found. Creating default.")
                self.create_default_config()
                return True

            with open(self.config_file, 'r') as f:
                config_data = yaml.safe_load(f)

            self.applications.clear()
            for app_name, app_config in config_data.get('applications', {}).items():
                self.applications[app_name] = ApplicationConfig(
                    name=app_name,
                    description=app_config.get('description', ''),
                    priority=app_config.get('priority', 5),
                    is_primary=app_config.get('is_primary', False),
                    monitors=app_config.get('monitors', []),
                    failover_target=app_config.get('failover_target'),
                    auto_failover=app_config.get('auto_failover', True),
                    failover_threshold=app_config.get('failover_threshold', 3),
                    recovery_threshold=app_config.get('recovery_threshold', 2)
                )

                # Initialize state tracking
                if app_config.get('is_primary', False):
                    self.current_state[app_name] = RedundancyState.PRIMARY_ACTIVE
                else:
                    self.current_state[app_name] = RedundancyState.SECONDARY_ACTIVE
                self.failure_counts[app_name] = 0
                self.success_counts[app_name] = 0
                self.monitor_results[app_name] = []

            self.logger.info(f"Loaded configuration for {len(self.applications)} applications")
            self._initialize_monitors()
            return True

        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            return False

    def save_configuration(self) -> bool:
        """Save current configuration to file"""
        try:
            config_data = {
                'applications': {}
            }

            for app_name, app_config in self.applications.items():
                config_data['applications'][app_name] = asdict(app_config)

            with open(self.config_file, 'w') as f:
                yaml.dump(config_data, f, default_flow_style=False, indent=2)

            self.logger.info("Configuration saved successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
            return False

    def create_default_config(self):
        """Create a default configuration file"""
        default_config = {
            'applications': {
                'primary_app': {
                    'description': 'Primary Application Server',
                    'priority': 1,
                    'is_primary': True,
                    'auto_failover': True,
                    'failover_threshold': 3,
                    'recovery_threshold': 2,
                    'failover_target': 'secondary_app',
                    'monitors': []
                },
                'secondary_app': {
                    'description': 'Secondary Application Server (Backup)',
                    'priority': 2,
                    'is_primary': False,
                    'auto_failover': True,
                    'failover_threshold': 3,
                    'recovery_threshold': 2,
                    'failover_target': None,
                    'monitors': []
                }
            }
        }

        with open(self.config_file, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False, indent=2)

    def _initialize_monitors(self):
        """Initialize monitor instances"""
        self.monitor_instances.clear()

        for app_name, app_config in self.applications.items():
            for monitor_config in app_config.monitors:
                monitor_key = f"{app_name}_{monitor_config['name']}"

                try:
                    # Create monitor configuration
                    config_dict = {
                        "name": monitor_config['name'],
                        "type": monitor_config['type'],
                        "params": monitor_config.get('params', {})
                    }

                    # Create monitor instance
                    if monitor_config['type'] == 'disk':
                        monitor = DiskMonitor(config_dict, self.logger)
                    elif monitor_config['type'] == 'network':
                        monitor = NetworkMonitor(config_dict, self.logger)
                    elif monitor_config['type'] == 'http':
                        monitor = HTTPMonitor(config_dict, self.logger)
                    else:
                        self.logger.error(f"Unknown monitor type: {monitor_config['type']}")
                        continue

                    self.monitor_instances[monitor_key] = monitor
                    self.logger.info(f"Initialized monitor: {monitor_key}")

                except Exception as e:
                    self.logger.error(f"Failed to initialize monitor {monitor_key}: {e}")

    def add_application(self, app_config: ApplicationConfig) -> bool:
        """Add a new application configuration"""
        try:
            self.applications[app_config.name] = app_config
            if app_config.is_primary:
                self.current_state[app_config.name] = RedundancyState.PRIMARY_ACTIVE
            else:
                self.current_state[app_config.name] = RedundancyState.SECONDARY_ACTIVE
            self.failure_counts[app_config.name] = 0
            self.success_counts[app_config.name] = 0
            self.monitor_results[app_config.name] = []

            self._initialize_monitors()
            self.save_configuration()

            self._log_event("APPLICATION_ADDED", app_config.name,
                          f"Application '{app_config.name}' added to monitoring", "INFO")

            return True

        except Exception as e:
            self.logger.error(f"Failed to add application {app_config.name}: {e}")
            return False

    def remove_application(self, app_name: str) -> bool:
        """Remove an application configuration"""
        try:
            if app_name not in self.applications:
                return False

            # Remove from all tracking
            del self.applications[app_name]
            del self.current_state[app_name]
            del self.failure_counts[app_name]
            del self.success_counts[app_name]
            del self.monitor_results[app_name]

            # Remove monitor instances
            to_remove = [key for key in self.monitor_instances.keys() if key.startswith(f"{app_name}_")]
            for key in to_remove:
                del self.monitor_instances[key]

            self.save_configuration()

            self._log_event("APPLICATION_REMOVED", app_name,
                          f"Application '{app_name}' removed from monitoring", "INFO")

            return True

        except Exception as e:
            self.logger.error(f"Failed to remove application {app_name}: {e}")
            return False

    def add_monitor_to_application(self, app_name: str, monitor_config: Dict[str, Any]) -> bool:
        """Add a monitor to an existing application"""
        try:
            if app_name not in self.applications:
                return False

            self.applications[app_name].monitors.append(monitor_config)
            self._initialize_monitors()
            self.save_configuration()

            self._log_event("MONITOR_ADDED", app_name,
                          f"Monitor '{monitor_config['name']}' added to application", "INFO")

            return True

        except Exception as e:
            self.logger.error(f"Failed to add monitor to {app_name}: {e}")
            return False

    def start_monitoring(self) -> bool:
        """Start the monitoring process"""
        if self.monitoring_active:
            return False

        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()

        self._log_event("MONITORING_STARTED", "SYSTEM", "Monitoring started", "INFO")
        self.logger.info("Monitoring started")
        return True

    def stop_monitoring(self) -> bool:
        """Stop the monitoring process"""
        if not self.monitoring_active:
            return False

        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)

        self._log_event("MONITORING_STOPPED", "SYSTEM", "Monitoring stopped", "INFO")
        self.logger.info("Monitoring stopped")
        return True

    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                self._check_all_monitors()
                self._evaluate_redundancy_state()
                time.sleep(10)  # Check every 10 seconds
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(5)

    def _check_all_monitors(self):
        """Check all configured monitors"""
        for app_name, app_config in self.applications.items():
            app_healthy = True

            for monitor_config in app_config.monitors:
                monitor_key = f"{app_name}_{monitor_config['name']}"

                if monitor_key not in self.monitor_instances:
                    continue

                monitor = self.monitor_instances[monitor_key]
                start_time = time.time()

                try:
                    monitor.check_status()
                    response_time = time.time() - start_time

                    status = HealthStatus.HEALTHY if monitor.is_healthy else HealthStatus.UNHEALTHY
                    message = "Monitor check successful" if monitor.is_healthy else "Monitor check failed"

                    result = MonitorResult(
                        monitor_name=monitor_config['name'],
                        application=app_name,
                        status=status,
                        message=message,
                        timestamp=datetime.now(),
                        response_time=response_time
                    )

                    with self.lock:
                        self.monitor_results[app_name].append(result)
                        # Keep only last 100 results per application
                        if len(self.monitor_results[app_name]) > 100:
                            self.monitor_results[app_name] = self.monitor_results[app_name][-100:]

                    if not monitor.is_healthy:
                        app_healthy = False

                except Exception as e:
                    self.logger.error(f"Error checking monitor {monitor_key}: {e}")
                    app_healthy = False

            # Update application health tracking
            with self.lock:
                if app_healthy:
                    self.success_counts[app_name] += 1
                    self.failure_counts[app_name] = 0
                else:
                    self.failure_counts[app_name] += 1
                    self.success_counts[app_name] = 0

    def _evaluate_redundancy_state(self):
        """Evaluate and update redundancy state"""
        for app_name, app_config in self.applications.items():
            current_failures = self.failure_counts[app_name]
            current_successes = self.success_counts[app_name]

            # Check for failover conditions
            if (current_failures >= app_config.failover_threshold and
                app_config.auto_failover and
                app_config.failover_target):

                self._trigger_failover(app_name, app_config.failover_target)

            # Check for recovery conditions
            elif (current_successes >= app_config.recovery_threshold and
                  self.current_state[app_name] == RedundancyState.SECONDARY_ACTIVE):

                self._trigger_recovery(app_name)

    def _trigger_failover(self, failed_app: str, target_app: str):
        """Trigger failover from failed app to target app"""
        try:
            if self.current_state[failed_app] == RedundancyState.FAILOVER_IN_PROGRESS:
                return  # Already in progress

            self.current_state[failed_app] = RedundancyState.FAILOVER_IN_PROGRESS

            self._log_event("FAILOVER_INITIATED", failed_app,
                          f"Failover initiated from {failed_app} to {target_app}", "CRITICAL")

            # Simulate failover process (implement your actual failover logic here)
            self._execute_failover_script(failed_app, target_app)

            # Update states
            self.current_state[failed_app] = RedundancyState.BOTH_FAILED
            if target_app in self.current_state:
                self.current_state[target_app] = RedundancyState.SECONDARY_ACTIVE

            self._log_event("FAILOVER_COMPLETED", failed_app,
                          f"Failover completed to {target_app}", "WARNING")

        except Exception as e:
            self.logger.error(f"Failover failed: {e}")
            self._log_event("FAILOVER_FAILED", failed_app,
                          f"Failover failed: {str(e)}", "CRITICAL")

    def _trigger_recovery(self, recovered_app: str):
        """Trigger recovery of primary application"""
        try:
            self._log_event("RECOVERY_INITIATED", recovered_app,
                          f"Recovery initiated for {recovered_app}", "INFO")

            # Simulate recovery process (implement your actual recovery logic here)
            self._execute_recovery_script(recovered_app)

            # Update state
            if self.applications[recovered_app].is_primary:
                self.current_state[recovered_app] = RedundancyState.PRIMARY_ACTIVE

            self._log_event("RECOVERY_COMPLETED", recovered_app,
                          f"Recovery completed for {recovered_app}", "INFO")

        except Exception as e:
            self.logger.error(f"Recovery failed: {e}")
            self._log_event("RECOVERY_FAILED", recovered_app,
                          f"Recovery failed: {str(e)}", "WARNING")

    def _execute_failover_script(self, failed_app: str, target_app: str):
        """Execute custom failover script (implement your logic here)"""
        # This is where you would implement your actual failover logic
        # Examples:
        # - Stop services on failed application
        # - Start services on target application
        # - Update load balancer configuration
        # - Update DNS records
        # - Notify administrators

        self.logger.info(f"Executing failover from {failed_app} to {target_app}")

        # Example implementation:
        # os.system(f"systemctl stop {failed_app}")
        # os.system(f"systemctl start {target_app}")
        # self._update_load_balancer(target_app)
        # self._send_notification(f"Failover: {failed_app} -> {target_app}")

    def _execute_recovery_script(self, recovered_app: str):
        """Execute custom recovery script (implement your logic here)"""
        # This is where you would implement your actual recovery logic

        self.logger.info(f"Executing recovery for {recovered_app}")

        # Example implementation:
        # os.system(f"systemctl start {recovered_app}")
        # self._update_load_balancer(recovered_app)
        # self._send_notification(f"Recovery completed: {recovered_app}")

    def _log_event(self, event_type: str, application: str, description: str, severity: str):
        """Log a redundancy event"""
        event = RedundancyEvent(
            timestamp=datetime.now(),
            event_type=event_type,
            application=application,
            description=description,
            severity=severity
        )

        with self.lock:
            self.events.append(event)
            # Keep only last 1000 events
            if len(self.events) > 1000:
                self.events = self.events[-1000:]

        # Log to file
        log_method = getattr(self.logger, severity.lower(), self.logger.info)
        log_method(f"[{event_type}] {application}: {description}")

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        with self.lock:
            status = {
                'monitoring_active': self.monitoring_active,
                'applications': {},
                'recent_events': [asdict(event) for event in self.events[-10:]],
                'timestamp': datetime.now().isoformat()
            }

            for app_name, app_config in self.applications.items():
                recent_results = self.monitor_results[app_name][-5:] if self.monitor_results[app_name] else []

                status['applications'][app_name] = {
                    'config': asdict(app_config),
                    'current_state': self.current_state[app_name].value,
                    'failure_count': self.failure_counts[app_name],
                    'success_count': self.success_counts[app_name],
                    'recent_results': [asdict(result) for result in recent_results],
                    'overall_health': self._calculate_overall_health(app_name)
                }

            return status

    def _calculate_overall_health(self, app_name: str) -> str:
        """Calculate overall health status for an application"""
        if not self.monitor_results[app_name]:
            return HealthStatus.UNKNOWN.value

        recent_results = self.monitor_results[app_name][-5:]
        healthy_count = sum(1 for result in recent_results if result.status == HealthStatus.HEALTHY)

        if healthy_count == len(recent_results):
            return HealthStatus.HEALTHY.value
        elif healthy_count == 0:
            return HealthStatus.CRITICAL.value
        else:
            return HealthStatus.UNHEALTHY.value

    def manual_failover(self, app_name: str, target_app: str) -> bool:
        """Manually trigger failover"""
        try:
            if app_name not in self.applications or target_app not in self.applications:
                return False

            self._trigger_failover(app_name, target_app)
            return True

        except Exception as e:
            self.logger.error(f"Manual failover failed: {e}")
            return False

    def test_monitor(self, app_name: str, monitor_name: str) -> Optional[MonitorResult]:
        """Test a specific monitor manually"""
        try:
            monitor_key = f"{app_name}_{monitor_name}"
            if monitor_key not in self.monitor_instances:
                return None

            monitor = self.monitor_instances[monitor_key]
            start_time = time.time()

            monitor.check_status()
            response_time = time.time() - start_time

            status = HealthStatus.HEALTHY if monitor.is_healthy else HealthStatus.UNHEALTHY
            message = "Manual test successful" if monitor.is_healthy else "Manual test failed"

            result = MonitorResult(
                monitor_name=monitor_name,
                application=app_name,
                status=status,
                message=message,
                timestamp=datetime.now(),
                response_time=response_time
            )

            self._log_event("MANUAL_TEST", app_name,
                          f"Manual test of monitor '{monitor_name}': {status.value}", "INFO")

            return result

        except Exception as e:
            self.logger.error(f"Manual monitor test failed: {e}")
            return None
