{% extends "redundancy_base.html" %}

{% block title %}Database Clusters - Professional Redundancy Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="fas fa-database me-2 text-primary"></i>
                Database Cluster Management
                <small class="text-muted ms-2">Microsoft Failover Cluster Style</small>
            </h1>
            <div>
                <button class="btn btn-success me-2" id="startAllClusters">
                    <i class="fas fa-play me-1"></i>Start All Monitoring
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClusterModal">
                    <i class="fas fa-plus me-1"></i>Add Cluster
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Cluster Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-server fa-2x text-primary mb-2"></i>
                <div class="metric-value text-primary" id="totalClusters">-</div>
                <div class="metric-label">Total Clusters</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <div class="metric-value text-success" id="healthyClusters">-</div>
                <div class="metric-label">Healthy Clusters</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                <div class="metric-value text-warning" id="degradedClusters">-</div>
                <div class="metric-label">Degraded Clusters</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                <div class="metric-value text-danger" id="failedClusters">-</div>
                <div class="metric-label">Failed Clusters</div>
            </div>
        </div>
    </div>
</div>

<!-- Database Clusters List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Database Clusters
                </h5>
            </div>
            <div class="card-body">
                <div id="clustersList">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">Loading database clusters...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Cluster Modal -->
<div class="modal fade" id="addClusterModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Database Cluster</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addClusterForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="clusterName" class="form-label">Cluster Name *</label>
                                <input type="text" class="form-control" id="clusterName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="clusterDescription" class="form-label">Description</label>
                                <input type="text" class="form-control" id="clusterDescription">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="databaseType" class="form-label">Database Type *</label>
                                <select class="form-select" id="databaseType" required>
                                    <option value="">Select Database</option>
                                    <option value="postgresql">PostgreSQL</option>
                                    <option value="mysql">MySQL</option>
                                    <option value="sqlserver">SQL Server</option>
                                    <option value="mongodb">MongoDB</option>
                                    <option value="oracle">Oracle</option>
                                    <option value="redis">Redis</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="virtualIP" class="form-label">Virtual IP</label>
                                <input type="text" class="form-control" id="virtualIP" placeholder="*************">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="replicationMode" class="form-label">Replication Mode</label>
                                <select class="form-select" id="replicationMode">
                                    <option value="synchronous">Synchronous</option>
                                    <option value="asynchronous">Asynchronous</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoFailover" checked>
                                    <label class="form-check-label" for="autoFailover">
                                        Auto Failover
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="failoverThreshold" class="form-label">Failover Threshold</label>
                                <input type="number" class="form-control" id="failoverThreshold" value="3" min="1" max="10">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="heartbeatInterval" class="form-label">Heartbeat (seconds)</label>
                                <input type="number" class="form-control" id="heartbeatInterval" value="5" min="1" max="60">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="quorumNodes" class="form-label">Quorum Nodes</label>
                                <input type="number" class="form-control" id="quorumNodes" value="2" min="1" max="10">
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    <h6>Database Nodes</h6>
                    <div id="nodesList">
                        <!-- Nodes will be added dynamically -->
                    </div>
                    
                    <button type="button" class="btn btn-outline-primary" id="addNodeBtn">
                        <i class="fas fa-plus me-1"></i>Add Database Node
                    </button>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveCluster">Create Cluster</button>
            </div>
        </div>
    </div>
</div>

<!-- Node Configuration Template -->
<template id="nodeTemplate">
    <div class="card mb-3 node-card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">Database Node</h6>
            <button type="button" class="btn btn-sm btn-outline-danger remove-node">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Node Name *</label>
                        <input type="text" class="form-control node-name" required>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Host/IP *</label>
                        <input type="text" class="form-control node-host" required>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label class="form-label">Port *</label>
                        <input type="number" class="form-control node-port" required>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label class="form-label">Role *</label>
                        <select class="form-select node-role" required>
                            <option value="primary">Primary</option>
                            <option value="secondary">Secondary</option>
                            <option value="witness">Witness</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <div class="form-check mt-4">
                            <input class="form-check-input node-active" type="checkbox" checked>
                            <label class="form-check-label">Active</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">Username</label>
                        <input type="text" class="form-control node-username">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" class="form-control node-password">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">Connection String</label>
                        <input type="text" class="form-control node-connection" placeholder="Auto-generated">
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block extra_js %}
<script>
let clusterData = {};

$(document).ready(function() {
    loadClusters();
    
    $('#addNodeBtn').click(addNode);
    $('#saveCluster').click(saveCluster);
    $('#startAllClusters').click(startAllClusters);
    
    // Auto-refresh every 30 seconds
    setInterval(loadClusters, 30000);
    
    // Add initial nodes
    addNode(); // Primary
    addNode(); // Secondary
});

function loadClusters() {
    $.get('/api/database-clusters')
        .done(function(response) {
            if (response.success) {
                clusterData = response.data;
                displayClusters(response.data);
                updateClusterMetrics(response.data);
            } else {
                showAlert('danger', 'Failed to load clusters: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to connect to server');
        });
}

function displayClusters(data) {
    let html = '';
    
    if (!data.clusters || Object.keys(data.clusters).length === 0) {
        html = '<div class="text-center py-5"><p class="text-muted">No database clusters configured.</p></div>';
    } else {
        Object.entries(data.clusters).forEach(([clusterName, cluster]) => {
            const stateClass = getStateClass(cluster.state);
            const stateIcon = getStateIcon(cluster.state);
            
            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">
                                    <i class="${stateIcon} me-2 ${stateClass}"></i>
                                    ${clusterName}
                                    <span class="badge bg-secondary ms-2">${cluster.database_type.toUpperCase()}</span>
                                    <span class="badge bg-${getStateBadgeClass(cluster.state)} ms-1">${cluster.state.toUpperCase()}</span>
                                </h6>
                                <small class="text-muted">${cluster.description}</small>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="manualFailover('${clusterName}')">
                                    <i class="fas fa-exchange-alt"></i> Failover
                                </button>
                                <button class="btn btn-sm btn-outline-success me-1" onclick="startClusterMonitoring('${clusterName}')">
                                    <i class="fas fa-play"></i> Start
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="stopClusterMonitoring('${clusterName}')">
                                    <i class="fas fa-stop"></i> Stop
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Configuration:</strong><br>
                                <small class="text-muted">
                                    Virtual IP: ${cluster.virtual_ip || 'Not configured'}<br>
                                    Auto Failover: ${cluster.auto_failover ? 'Enabled' : 'Disabled'}<br>
                                    Monitoring: ${data.monitoring_active ? 'Active' : 'Inactive'}
                                </small>
                            </div>
                            <div class="col-md-6">
                                <strong>Nodes (${cluster.nodes.length}):</strong><br>
                                <div class="mt-2">
            `;
            
            cluster.nodes.forEach(node => {
                const nodeStateClass = node.is_active ? 'text-success' : 'text-danger';
                const nodeIcon = node.is_active ? 'fas fa-check-circle' : 'fas fa-times-circle';
                const roleClass = node.role === 'primary' ? 'bg-primary' : node.role === 'secondary' ? 'bg-info' : 'bg-secondary';
                
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>
                            <i class="${nodeIcon} ${nodeStateClass} me-1"></i>
                            ${node.name} (${node.host}:${node.port})
                            <span class="badge ${roleClass} ms-1">${node.role.toUpperCase()}</span>
                        </span>
                        <small class="text-muted">
                            Health: ${node.health_score}%
                            ${node.last_heartbeat ? '• Last: ' + new Date(node.last_heartbeat).toLocaleTimeString() : ''}
                        </small>
                    </div>
                `;
            });
            
            html += `
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    $('#clustersList').html(html);
}

function updateClusterMetrics(data) {
    const clusters = data.clusters || {};
    const total = Object.keys(clusters).length;
    let healthy = 0, degraded = 0, failed = 0;
    
    Object.values(clusters).forEach(cluster => {
        switch(cluster.state) {
            case 'healthy': healthy++; break;
            case 'degraded': degraded++; break;
            case 'failed': failed++; break;
        }
    });
    
    $('#totalClusters').text(total);
    $('#healthyClusters').text(healthy);
    $('#degradedClusters').text(degraded);
    $('#failedClusters').text(failed);
}

function addNode() {
    const template = document.getElementById('nodeTemplate');
    const clone = template.content.cloneNode(true);
    
    // Set default port based on database type
    const dbType = $('#databaseType').val();
    const defaultPorts = {
        'postgresql': 5432,
        'mysql': 3306,
        'sqlserver': 1433,
        'mongodb': 27017,
        'oracle': 1521,
        'redis': 6379
    };
    
    if (dbType && defaultPorts[dbType]) {
        clone.querySelector('.node-port').value = defaultPorts[dbType];
    }
    
    // Add remove functionality
    clone.querySelector('.remove-node').addEventListener('click', function() {
        this.closest('.node-card').remove();
    });
    
    document.getElementById('nodesList').appendChild(clone);
}

function saveCluster() {
    const clusterName = $('#clusterName').val();
    const description = $('#clusterDescription').val();
    const databaseType = $('#databaseType').val();
    const virtualIP = $('#virtualIP').val();
    const replicationMode = $('#replicationMode').val();
    const autoFailover = $('#autoFailover').is(':checked');
    const failoverThreshold = parseInt($('#failoverThreshold').val());
    const heartbeatInterval = parseInt($('#heartbeatInterval').val());
    const quorumNodes = parseInt($('#quorumNodes').val());
    
    if (!clusterName || !databaseType) {
        showAlert('warning', 'Please fill in required fields');
        return;
    }
    
    // Collect nodes
    const nodes = [];
    $('.node-card').each(function() {
        const card = $(this);
        const node = {
            name: card.find('.node-name').val(),
            host: card.find('.node-host').val(),
            port: parseInt(card.find('.node-port').val()),
            database_type: databaseType,
            role: card.find('.node-role').val(),
            username: card.find('.node-username').val(),
            password: card.find('.node-password').val(),
            connection_string: card.find('.node-connection').val() || generateConnectionString(databaseType, card.find('.node-host').val(), card.find('.node-port').val(), card.find('.node-username').val()),
            is_active: card.find('.node-active').is(':checked')
        };
        
        if (node.name && node.host && node.port) {
            nodes.push(node);
        }
    });
    
    if (nodes.length < 2) {
        showAlert('warning', 'At least 2 nodes are required for a cluster');
        return;
    }
    
    const clusterConfig = {
        name: clusterName,
        description: description,
        database_type: databaseType,
        virtual_ip: virtualIP,
        auto_failover: autoFailover,
        failover_threshold: failoverThreshold,
        heartbeat_interval: heartbeatInterval,
        replication_mode: replicationMode,
        quorum_nodes: quorumNodes,
        nodes: nodes
    };
    
    $.ajax({
        url: '/api/database-clusters',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(clusterConfig),
        success: function(response) {
            if (response.success) {
                showAlert('success', 'Database cluster created successfully');
                $('#addClusterModal').modal('hide');
                $('#addClusterForm')[0].reset();
                $('#nodesList').empty();
                loadClusters();
            } else {
                showAlert('danger', 'Failed to create cluster: ' + response.message);
            }
        },
        error: function() {
            showAlert('danger', 'Failed to create cluster');
        }
    });
}

function generateConnectionString(dbType, host, port, username) {
    const templates = {
        'postgresql': `postgresql://${username}:password@${host}:${port}/database`,
        'mysql': `mysql://${username}:password@${host}:${port}/database`,
        'sqlserver': `mssql://${username}:password@${host}:${port}/database`,
        'mongodb': `mongodb://${username}:password@${host}:${port}/database`,
        'oracle': `oracle://${username}:password@${host}:${port}/database`,
        'redis': `redis://${username}:password@${host}:${port}/0`
    };
    
    return templates[dbType] || `${dbType}://${username}:password@${host}:${port}/database`;
}

function startClusterMonitoring(clusterName) {
    $.post(`/api/database-clusters/${clusterName}/start`)
        .done(function(response) {
            if (response.success) {
                showAlert('success', `Monitoring started for cluster: ${clusterName}`);
                loadClusters();
            } else {
                showAlert('danger', 'Failed to start monitoring: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to start monitoring');
        });
}

function stopClusterMonitoring(clusterName) {
    $.post(`/api/database-clusters/${clusterName}/stop`)
        .done(function(response) {
            if (response.success) {
                showAlert('warning', `Monitoring stopped for cluster: ${clusterName}`);
                loadClusters();
            } else {
                showAlert('danger', 'Failed to stop monitoring: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to stop monitoring');
        });
}

function startAllClusters() {
    $.post('/api/database-clusters/start-all')
        .done(function(response) {
            if (response.success) {
                showAlert('success', 'All cluster monitoring started');
                loadClusters();
            } else {
                showAlert('danger', 'Failed to start all clusters: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to start all clusters');
        });
}

function manualFailover(clusterName) {
    // Get available secondary nodes
    const cluster = clusterData.clusters[clusterName];
    if (!cluster) return;
    
    const secondaryNodes = cluster.nodes.filter(node => node.role === 'secondary' && node.is_active);
    
    if (secondaryNodes.length === 0) {
        showAlert('warning', 'No available secondary nodes for failover');
        return;
    }
    
    let options = secondaryNodes.map(node => `<option value="${node.name}">${node.name} (${node.host})</option>`).join('');
    
    const html = `
        <div class="mb-3">
            <label class="form-label">Select target node for failover:</label>
            <select class="form-select" id="failoverTarget">
                ${options}
            </select>
        </div>
    `;
    
    if (confirm(`Are you sure you want to perform manual failover for cluster "${clusterName}"?\n\nThis will promote a secondary node to primary.`)) {
        const targetNode = prompt('Enter target node name:');
        if (targetNode) {
            $.ajax({
                url: `/api/database-clusters/${clusterName}/failover`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({target_node: targetNode}),
                success: function(response) {
                    if (response.success) {
                        showAlert('success', 'Manual failover initiated successfully');
                        loadClusters();
                    } else {
                        showAlert('danger', 'Failover failed: ' + response.message);
                    }
                },
                error: function() {
                    showAlert('danger', 'Failed to initiate failover');
                }
            });
        }
    }
}

function getStateClass(state) {
    switch(state) {
        case 'healthy': return 'text-success';
        case 'degraded': return 'text-warning';
        case 'failed': return 'text-danger';
        case 'failover_in_progress': return 'text-info';
        default: return 'text-secondary';
    }
}

function getStateIcon(state) {
    switch(state) {
        case 'healthy': return 'fas fa-check-circle';
        case 'degraded': return 'fas fa-exclamation-triangle';
        case 'failed': return 'fas fa-times-circle';
        case 'failover_in_progress': return 'fas fa-sync fa-spin';
        default: return 'fas fa-question-circle';
    }
}

function getStateBadgeClass(state) {
    switch(state) {
        case 'healthy': return 'success';
        case 'degraded': return 'warning';
        case 'failed': return 'danger';
        case 'failover_in_progress': return 'info';
        default: return 'secondary';
    }
}
</script>
{% endblock %}
