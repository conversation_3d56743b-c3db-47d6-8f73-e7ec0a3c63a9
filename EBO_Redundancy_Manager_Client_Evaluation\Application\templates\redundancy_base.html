<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Professional Redundancy Management System{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }

        body {
            background-color: var(--light-color);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
        }

        .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            transform: translateY(-1px);
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background: linear-gradient(135deg, var(--secondary-color), #60a5fa);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            font-weight: 600;
            padding: 1rem 1.5rem;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-healthy {
            background-color: var(--success-color);
        }

        .status-unhealthy {
            background-color: var(--danger-color);
        }

        .status-unknown {
            background-color: #6b7280;
        }

        .status-critical {
            background-color: #dc2626;
            animation: pulse-critical 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes pulse-critical {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.1); }
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), #60a5fa);
            border: none;
            border-radius: 8px;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #34d399);
            border: none;
            border-radius: 8px;
            font-weight: 600;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #f87171);
            border: none;
            border-radius: 8px;
            font-weight: 600;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #fbbf24);
            border: none;
            border-radius: 8px;
            font-weight: 600;
            color: white;
        }

        .alert {
            border: none;
            border-radius: 8px;
            font-weight: 500;
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--dark-color), #374151);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem;
        }

        .table tbody tr:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }

        .metric-card {
            text-align: center;
            padding: 1.5rem;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0.5rem 0;
        }

        .metric-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .sidebar {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        .modal-content {
            border-radius: 12px;
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--secondary-color), #60a5fa);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .redundancy-state {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .state-primary-active {
            background-color: #dcfdf7;
            color: #065f46;
        }

        .state-secondary-active {
            background-color: #fef3c7;
            color: #92400e;
        }

        .state-both-active {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .state-both-failed {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .state-failover-in-progress {
            background-color: #fde68a;
            color: #d97706;
            animation: pulse 1s infinite;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .event-item {
            border-left: 4px solid var(--secondary-color);
            padding: 1rem;
            margin-bottom: 1rem;
            background: white;
            border-radius: 0 8px 8px 0;
        }

        .event-critical {
            border-left-color: var(--danger-color);
        }

        .event-warning {
            border-left-color: var(--warning-color);
        }

        .event-info {
            border-left-color: var(--success-color);
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-shield-alt me-2"></i>
                Redundancy Manager Pro
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('applications') }}">
                            <i class="fas fa-server me-1"></i>Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('monitors') }}">
                            <i class="fas fa-heartbeat me-1"></i>Monitors
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('events') }}">
                            <i class="fas fa-list-alt me-1"></i>Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('database_clusters') }}">
                            <i class="fas fa-database me-1"></i>Database Clusters
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('storage_redundancy') }}">
                            <i class="fas fa-hdd me-1"></i>Storage Redundancy
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('ebo_redundancy') }}">
                            <i class="fas fa-building me-1"></i>EBO Redundancy
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="fas fa-circle text-success me-1" id="systemStatus"></i>
                            <span id="systemStatusText">System Online</span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <!-- Global JavaScript -->
    <script>
        // Global variables
        let refreshInterval;
        let isMonitoring = false;

        // Initialize on page load
        $(document).ready(function() {
            updateSystemStatus();
            startAutoRefresh();
        });

        // Auto-refresh system status
        function startAutoRefresh() {
            refreshInterval = setInterval(updateSystemStatus, 10000); // Every 10 seconds
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        // Update system status indicator
        function updateSystemStatus() {
            $.get('/api/status')
                .done(function(response) {
                    if (response.success) {
                        isMonitoring = response.data.monitoring_active;
                        updateStatusIndicator(true, isMonitoring);
                    } else {
                        updateStatusIndicator(false, false);
                    }
                })
                .fail(function() {
                    updateStatusIndicator(false, false);
                });
        }

        function updateStatusIndicator(online, monitoring) {
            const statusIcon = $('#systemStatus');
            const statusText = $('#systemStatusText');

            if (online) {
                statusIcon.removeClass('text-danger').addClass('text-success');
                statusText.text(monitoring ? 'Monitoring Active' : 'System Online');
            } else {
                statusIcon.removeClass('text-success').addClass('text-danger');
                statusText.text('System Offline');
            }
        }

        // Utility functions
        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.container-fluid').prepend(alertHtml);
        }

        function formatTimestamp(timestamp) {
            return new Date(timestamp).toLocaleString();
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function getStatusClass(status) {
            switch(status) {
                case 'healthy': return 'status-healthy';
                case 'unhealthy': return 'status-unhealthy';
                case 'critical': return 'status-critical';
                default: return 'status-unknown';
            }
        }

        function getRedundancyStateClass(state) {
            return 'state-' + state.replace(/_/g, '-');
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
