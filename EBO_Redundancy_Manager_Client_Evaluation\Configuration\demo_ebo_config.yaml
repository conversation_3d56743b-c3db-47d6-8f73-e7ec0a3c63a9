# EBO Redundancy Manager - Demo Configuration
# This is a sample configuration for evaluation purposes
# Update these values with your actual server details

applications:
  ebo_enterprise_primary:
    description: "EBO Enterprise Server - Primary Dell R750xa"
    priority: 1
    is_primary: true
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: "ebo_enterprise_secondary"
    server_ip: "************"           # UPDATE: Your primary server IP
    server_name: "EBO-PRIMARY-01"       # UPDATE: Your primary server name
    ebo_services:
      - "EcoStruxure Building Operation Enterprise Server"
      - "EcoStruxure Building Operation Database Service"
      - "EcoStruxure Building Operation Web Service"
      - "EcoStruxure Building Operation Automation Server"
      - "EcoStruxure Building Operation License Server"
    ebo_installation_path: "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation"
    ebo_data_path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation"
    
  ebo_enterprise_secondary:
    description: "EBO Enterprise Server - Secondary Dell R750xa"
    priority: 2
    is_primary: false
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: null
    server_ip: "************"           # UPDATE: Your secondary server IP
    server_name: "EBO-SECONDARY-01"     # UPDATE: Your secondary server name
    ebo_services:
      - "EcoStruxure Building Operation Enterprise Server"
      - "EcoStruxure Building Operation Database Service"
      - "EcoStruxure Building Operation Web Service"
      - "EcoStruxure Building Operation Automation Server"
      - "EcoStruxure Building Operation License Server"
    ebo_installation_path: "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation"
    ebo_data_path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation"

# Network Configuration
network_config:
  virtual_ip: "*************"          # UPDATE: Your virtual IP for clients
  primary_server: "************"       # UPDATE: Your primary server IP
  secondary_server: "************"     # UPDATE: Your secondary server IP
  heartbeat_network: "*************/24" # UPDATE: Your heartbeat network
  client_redirect_method: "dns_update"

# Client PC Configuration
client_pcs:
  - name: "EBO-Workstation-01"          # UPDATE: Your client PC names
    ip: "************"                  # UPDATE: Your client PC IPs
    current_ebo_server: "*************"
    location: "Control Room"
  - name: "EBO-Workstation-02"
    ip: "************"
    current_ebo_server: "*************"
    location: "Engineering Office"
  - name: "EBO-Workstation-03"
    ip: "************"
    current_ebo_server: "*************"
    location: "Maintenance Shop"
  - name: "EBO-Workstation-04"
    ip: "************"
    current_ebo_server: "*************"
    location: "Supervisor Office"
  - name: "EBO-Workstation-05"
    ip: "************"
    current_ebo_server: "*************"
    location: "IT Office"

# Monitoring Configuration
monitoring:
  check_interval: 30                    # Check every 30 seconds
  failover_threshold: 3                 # Fail after 3 consecutive failures
  recovery_threshold: 2                 # Recover after 2 consecutive successes
  email_alerts: true
  email_server: "mail.yourcompany.com"  # UPDATE: Your SMTP server
  email_port: 587
  email_username: "<EMAIL>"  # UPDATE: Your email
  email_password: "your_email_password"          # UPDATE: Your password
  alert_recipients:
    - "<EMAIL>"           # UPDATE: Your admin emails
    - "<EMAIL>"
    - "<EMAIL>"

# Performance Thresholds
performance:
  cpu_threshold: 85                     # Alert if CPU > 85%
  memory_threshold: 85                  # Alert if Memory > 85%
  disk_threshold: 90                    # Alert if Disk > 90%
  network_latency_threshold: 10         # Alert if latency > 10ms

# Backup Configuration
backup:
  enabled: true
  backup_path: "D:\\EBO_Backup"      # UPDATE: Your backup path
  retention_days: 30
  schedule: "daily"
  compression: true
  encryption: true
