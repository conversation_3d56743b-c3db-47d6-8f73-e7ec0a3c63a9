# EBO Redundancy Manager - Installation Guide

## 🚀 Quick Start Installation

### Step 1: Extract Package
1. Extract the evaluation package to a folder on your Dell PowerEdge server
2. Recommended location: `C:\EBO_Redundancy_Evaluation\`

### Step 2: Run Installer
1. Navigate to the `Installation\` folder
2. Right-click `SIMPLE_AUTO_INSTALLER.bat`
3. Select "Run as administrator"
4. Follow the on-screen instructions

### Step 3: Launch Application
1. Double-click the desktop shortcut "EBO Redundancy Manager"
2. Or run `Start_EBO_Redundancy_Manager.bat`

### Step 4: Configure Settings
1. Click the "Configuration" tab in the GUI
2. Update server IP addresses
3. Set EBO installation paths
4. Configure client PC details

### Step 5: Start Monitoring
1. Click "Start Service" in the GUI
2. Access web interface at http://localhost:5001
3. Verify all systems are monitored

## 📋 Installation Requirements

### System Requirements:
- Windows Server 2019/2022 or Windows 10/11
- Administrator privileges
- Internet connection (for Python download if needed)
- 2GB free disk space
- EcoStruxure Building Operation installed (for full functionality)

### Network Requirements:
- TCP Port 5001: Web interface
- UDP Port 5405: Heartbeat communication
- TCP Port 80/443: EBO services
- TCP Port 1433: SQL Server

## 🔧 What the Installer Does

The automatic installer will:
✅ Check for Python installation
✅ Download and install Python 3.11 if needed
✅ Install all required packages automatically
✅ Create necessary directories
✅ Configure Windows Firewall rules
✅ Create desktop shortcuts
✅ Set up default configuration

## 🌐 Accessing the System

### GUI Application:
- Desktop shortcut: "EBO Redundancy Manager"
- Start Menu: Programs > EBO Redundancy Manager
- Direct: Start_EBO_Redundancy_Manager.bat

### Web Interface:
- URL: http://localhost:5001
- Available after starting the service
- Professional dashboard with real-time monitoring

## ⚙️ Configuration

### Required Configuration:
1. **Server Details**: Update IP addresses and names
2. **EBO Paths**: Set installation and data paths
3. **Client PCs**: Configure workstation details
4. **Network**: Set virtual IP and network settings

### Optional Configuration:
1. **Email Alerts**: SMTP server and recipients
2. **Monitoring**: Thresholds and intervals
3. **Backup**: Paths and retention policies
4. **Performance**: CPU, memory, disk limits

## 🧪 Testing the Installation

### Basic Functionality Test:
1. Start the GUI application
2. Click "Start Service"
3. Verify web interface loads
4. Check monitoring displays

### Configuration Test:
1. Update server IP addresses
2. Save configuration
3. Restart service
4. Verify settings applied

### Network Test:
1. Check firewall rules created
2. Test web interface access
3. Verify port connectivity
4. Test from client PCs

## 🔍 Troubleshooting

### Common Issues:

**Python Installation Fails:**
- Check internet connection
- Run installer as administrator
- Manually install Python from python.org

**Firewall Blocks Access:**
- Check Windows Firewall settings
- Verify ports 5001 and 5405 are open
- Run installer as administrator

**Service Won't Start:**
- Check Python installation
- Verify all packages installed
- Review logs in logs\ folder

**Web Interface Not Accessible:**
- Verify service is running
- Check firewall settings
- Try http://127.0.0.1:5001

### Getting Help:
- Review documentation in Documentation\ folder
- Check logs in Application\logs\ folder
- Contact technical support
- Schedule demonstration session

## 📞 Support Information

For installation support:
- Email: <EMAIL>
- Phone: ******-EBO-HELP
- Documentation: See Documentation\ folder
- Online: www.ebo-redundancy.com/support

**Ready to start? Run the installer and begin your evaluation!**
