@echo off
title SafeKit Professional Installer
color 0A

echo ================================================================
echo    SafeKit Redundancy Management System
echo    Professional Windows Installer Package
echo ================================================================
echo.

echo Extracting installer files...
powershell -command "Expand-Archive -Path 'SafeKit_Professional_Windows_Installer.zip' -DestinationPath '.' -Force"

echo.
echo Files extracted successfully!
echo.
echo Next steps:
echo 1. Navigate to SafeKit_Windows_Installer folder
echo 2. Right-click INSTALL.bat and "Run as administrator"
echo 3. Follow the installation prompts
echo.
pause

cd SafeKit_Windows_Installer
echo.
echo Opening installer folder...
explorer .
