# 🔧 EBO Hardware Redundancy Setup - REAL HARDWARE TEST

## 🎯 **For Your Two EBO Servers**

This is a **simple, working solution** you can test **immediately** on your actual hardware.

---

## 📋 **What You Need**

- ✅ **Two EBO servers** (<PERSON> PowerEdge R750xa)
- ✅ **Network connection** between them
- ✅ **Python installed** on both servers
- ✅ **5 minutes** to set up

---

## 🚀 **Step-by-Step Setup**

### **Step 1: Copy Files to Both Servers**

Copy these files to **BOTH** of your EBO servers:
- `SIMPLE_HARDWARE_TEST.py`
- `START_HARDWARE_MONITOR.bat`

### **Step 2: Run on First Server**

On your **first EBO server**:
1. **Double-click**: `START_HARDWARE_MONITOR.bat`
2. **Wait** for it to start
3. **Note the IP address** it shows
4. **Open browser**: `http://localhost:5003`

### **Step 3: Run on Second Server**

On your **second EBO server**:
1. **Double-click**: `START_HARDWARE_MONITOR.bat`
2. **Wait** for it to start
3. **Open browser**: `http://localhost:5003`

### **Step 4: Configure Connection**

On **either server's web interface**:
1. **Scroll down** to "Configuration" section
2. **Enter the IP** of the other server
3. **Click** "Update Configuration"
4. **Refresh** the page

---

## 🌐 **What You'll See**

### **Real-Time Monitoring**
- ✅ **CPU usage** of both servers
- ✅ **Memory usage** of both servers  
- ✅ **Disk usage** of both servers
- ✅ **EBO service status** on both servers
- ✅ **Network connectivity** between servers
- ✅ **Last check time** for each server

### **Live Dashboard**
- **Green status** = Server online and working
- **Red status** = Server offline or unreachable
- **Auto-refresh** every 30 seconds
- **Manual refresh** button available

---

## 🔧 **Testing Your Setup**

### **Test 1: Basic Monitoring**
1. **Check** that both servers show "Online"
2. **Verify** CPU/Memory/Disk stats are updating
3. **Confirm** EBO service status is detected

### **Test 2: Network Connectivity**
1. **Disconnect** network cable from one server
2. **Watch** the other server detect it as "Offline"
3. **Reconnect** and see it come back "Online"

### **Test 3: Service Monitoring**
1. **Stop** EBO service on one server
2. **Check** if the monitor detects service stopped
3. **Restart** service and verify detection

---

## 📊 **Network Access**

### **Access from Other Computers**
Replace `localhost` with the server's IP:
- Example: `http://*************:5003`
- Example: `http://*************:5003`

### **Monitor Both Servers from One Location**
- **Open two browser tabs**
- **Tab 1**: `http://*************:5003` (Server 1)
- **Tab 2**: `http://*************:5003` (Server 2)

---

## ⚡ **Quick Troubleshooting**

### **Problem**: "Python not found"
**Solution**: Install Python from https://python.org/downloads/
- ✅ Check "Add Python to PATH" during installation

### **Problem**: "Can't access from other computer"
**Solution**: Check Windows Firewall
- Allow port 5003 through firewall
- Or temporarily disable firewall for testing

### **Problem**: "Remote server shows Offline"
**Solution**: Check network connectivity
- Ping the other server: `ping 192.168.1.xxx`
- Verify IP addresses are correct
- Check network cables and switches

### **Problem**: "EBO service not detected"
**Solution**: Check service names
- The script looks for "EcoStruxure" or "Building" in process names
- Verify EBO is actually running

---

## 🎯 **Expected Results**

After setup, you should see:

### **Server 1 Dashboard**
```
EBO-SERVER-1 (Local)
IP Address: *************
Status: Online
EBO Service: Running
CPU Usage: 15%
Memory Usage: 45%
Disk Usage: 60%

EBO-SERVER-2 (Remote)  
IP Address: *************
Status: Online
Last Check: 2024-01-15 14:30:25
```

### **Server 2 Dashboard**
```
EBO-SERVER-1 (Local)
IP Address: *************  
Status: Online
EBO Service: Running
CPU Usage: 12%
Memory Usage: 38%
Disk Usage: 55%

EBO-SERVER-2 (Remote)
IP Address: *************
Status: Online
Last Check: 2024-01-15 14:30:25
```

---

## ✅ **Success Indicators**

You'll know it's working when:
- ✅ **Both servers** show in web interface
- ✅ **Real-time stats** are updating
- ✅ **Network connectivity** is detected
- ✅ **EBO services** are monitored
- ✅ **Auto-refresh** works every 30 seconds

---

## 🚀 **Next Steps**

Once this basic monitoring works:
1. **Test failover scenarios** (disconnect network, stop services)
2. **Add data replication** monitoring
3. **Configure alerts** for failures
4. **Set up automatic failover** procedures

This gives you a **real, working foundation** for your EBO redundancy system that you can build upon!

---

## 📞 **If It Still Doesn't Work**

If you're still having issues:
1. **Check Python installation** on both servers
2. **Verify network connectivity** between servers
3. **Test with Windows Firewall disabled** temporarily
4. **Check EBO service names** in Task Manager
5. **Try different port** if 5003 is blocked

The goal is to get **basic monitoring working first**, then add more advanced features.
