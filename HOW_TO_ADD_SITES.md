# 🏗️ How to Add New Sites to EBO Multi-Site Redundancy

## Complete Guide for Adding Sites to Your Multi-Site Redundancy System

### 🎯 Overview

You can add new sites to your EBO Multi-Site Redundancy system in **three different ways**:

1. **🌐 Web Interface** - Easy graphical interface (Recommended)
2. **📝 Configuration File** - Direct YAML editing
3. **🔧 API Calls** - Programmatic integration

---

## 🌐 Method 1: Web Interface (Recommended)

### **Step 1: Access the Add Site Page**
```
URL: http://localhost:5002/add-site
OR
Navigate: Dashboard → Sites → ➕ Add New Site
```

### **Step 2: Fill Out Site Information**

#### **📍 Basic Site Information:**
```yaml
Site ID: unique_site_identifier (e.g., "branch_office_south")
Description: Human-readable name (e.g., "Branch Office - South Region")
Priority: 1-10 (1 = highest priority for failover)
Client Count: Expected number of EBO client PCs
```

#### **📍 Location Information:**
```yaml
Address: Physical address of the site
City: City name
Country: Country name
Timezone: Select appropriate timezone (UTC-5, UTC+1, etc.)
```

#### **🌐 Network Configuration:**
```yaml
Site Network: CIDR notation (e.g., "********/16")
WAN/Public IP: External IP address (e.g., "************")
VPN Endpoint: VPN server address (e.g., "south-vpn.yourcompany.com")
Bandwidth: Available bandwidth (100Mbps, 500Mbps, 1Gbps, 10Gbps)
```

#### **🖥️ Server Configuration:**
```yaml
Primary Server:
  - Hostname: Server name (e.g., "SOUTH-EBO-PRIMARY")
  - IP Address: Internal IP (e.g., "*********")

Secondary Server:
  - Hostname: Server name (e.g., "SOUTH-EBO-SECONDARY")
  - IP Address: Internal IP (e.g., "*********")

Server Type: Dell PowerEdge R750xa (or your server model)
```

#### **🏗️ EBO Configuration:**
```yaml
Installation Path: "C:\Program Files\Schneider Electric\EcoStruxure Building Operation"
Database Server: Usually same as primary server IP
License Server: Usually same as primary server IP
```

### **Step 3: Submit and Verify**
1. Click **"🏗️ Add Site"** button
2. Wait for confirmation message
3. Verify site appears in sites list
4. Check site status in dashboard

---

## 📝 Method 2: Configuration File Editing

### **Step 1: Edit Configuration File**
```bash
# Open the configuration file
notepad multisite_redundancy_config.yaml
```

### **Step 2: Add New Site Section**
```yaml
sites:
  # Existing sites...
  
  new_site_id:  # Replace with your site ID
    description: "Your Site Description"
    priority: 5  # Set appropriate priority
    location:
      address: "123 Business Park Drive"
      city: "Your City"
      country: "Your Country"
      timezone: "UTC-5"
    network:
      site_network: "********/16"
      wan_ip: "************"
      vpn_endpoint: "site-vpn.yourcompany.com"
      bandwidth: "500Mbps"
    servers:
      primary:
        hostname: "SITE-EBO-PRIMARY"
        ip_address: "*********"
        server_type: "Dell PowerEdge R750xa"
      secondary:
        hostname: "SITE-EBO-SECONDARY"
        ip_address: "*********"
        server_type: "Dell PowerEdge R750xa"
    ebo_config:
      installation_path: "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation"
      database_server: "*********"
      license_server: "*********"
      client_count: 8
```

### **Step 3: Restart Multi-Site Manager**
```bash
# Restart the web interface to reload configuration
# The system will automatically detect the new site
```

---

## 🔧 Method 3: API Integration

### **Step 1: Prepare Site Data**
```json
{
  "site_id": "new_site_id",
  "config": {
    "description": "New Site Description",
    "priority": 5,
    "location": {
      "address": "123 Business Park Drive",
      "city": "Your City",
      "country": "Your Country",
      "timezone": "UTC-5"
    },
    "network": {
      "site_network": "********/16",
      "wan_ip": "************",
      "vpn_endpoint": "site-vpn.yourcompany.com",
      "bandwidth": "500Mbps"
    },
    "servers": {
      "primary": {
        "hostname": "SITE-EBO-PRIMARY",
        "ip_address": "*********",
        "server_type": "Dell PowerEdge R750xa"
      },
      "secondary": {
        "hostname": "SITE-EBO-SECONDARY",
        "ip_address": "*********",
        "server_type": "Dell PowerEdge R750xa"
      }
    },
    "ebo_config": {
      "installation_path": "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation",
      "database_server": "*********",
      "license_server": "*********",
      "client_count": 8
    }
  }
}
```

### **Step 2: Make API Call**
```bash
# Using curl
curl -X POST http://localhost:5002/api/sites/add \
  -H "Content-Type: application/json" \
  -d @site_data.json

# Using PowerShell
$siteData = Get-Content site_data.json
Invoke-RestMethod -Uri "http://localhost:5002/api/sites/add" -Method POST -Body $siteData -ContentType "application/json"
```

---

## 🎯 Real-World Examples

### **Example 1: Adding a Branch Office**
```yaml
Site ID: branch_office_chicago
Description: "Branch Office - Chicago"
Priority: 3
Location:
  Address: "456 Michigan Avenue"
  City: "Chicago"
  Country: "United States"
  Timezone: "UTC-6"
Network:
  Site Network: "*************/24"
  WAN IP: "*************"
  VPN Endpoint: "chicago-vpn.yourcompany.com"
  Bandwidth: "1Gbps"
Servers:
  Primary: CHI-EBO-PRIMARY (**************)
  Secondary: CHI-EBO-SECONDARY (**************)
```

### **Example 2: Adding a Manufacturing Plant**
```yaml
Site ID: manufacturing_plant_texas
Description: "Manufacturing Plant - Texas"
Priority: 2
Location:
  Address: "789 Industrial Blvd"
  City: "Houston"
  Country: "United States"
  Timezone: "UTC-6"
Network:
  Site Network: "**********/16"
  WAN IP: "*************"
  VPN Endpoint: "texas-plant-vpn.yourcompany.com"
  Bandwidth: "10Gbps"
Servers:
  Primary: TX-PLANT-EBO-PRIMARY (***********)
  Secondary: TX-PLANT-EBO-SECONDARY (***********)
```

### **Example 3: Adding a Cloud DR Site**
```yaml
Site ID: cloud_dr_aws
Description: "Cloud DR Site - AWS"
Priority: 10
Location:
  Address: "AWS US-East-1"
  City: "Virginia"
  Country: "United States"
  Timezone: "UTC-5"
Network:
  Site Network: "**********/16"
  WAN IP: "************"
  VPN Endpoint: "aws-dr-vpn.yourcompany.com"
  Bandwidth: "10Gbps"
Servers:
  Primary: AWS-DR-EBO-PRIMARY (***********)
  Secondary: AWS-DR-EBO-SECONDARY (***********)
```

---

## 🔧 Network Configuration Requirements

### **Before Adding a Site:**

#### **1. Network Connectivity**
```yaml
✅ VPN or WAN connection established
✅ Routing configured between sites
✅ Firewall rules allow required ports
✅ DNS resolution working
✅ Bandwidth sufficient for replication
```

#### **2. Server Preparation**
```yaml
✅ Dell PowerEdge servers installed and configured
✅ Windows Server 2019/2022 installed
✅ EcoStruxure Building Operation installed
✅ SQL Server installed and configured
✅ Network interfaces configured
```

#### **3. Required Ports**
```yaml
EBO Communication: TCP 80, 443, 1433
Database Replication: TCP 1433, 5022
File Replication: TCP 445, 135
Management Interface: TCP 5001, 5002
Heartbeat/Monitoring: ICMP, TCP 80
```

---

## 📊 Site Priority Guidelines

### **Priority Levels:**
```yaml
Priority 1: Primary headquarters site
Priority 2: Main branch offices
Priority 3: Secondary branch offices
Priority 4: Regional offices
Priority 5-9: Smaller sites
Priority 10: Disaster recovery sites
```

### **Failover Order:**
```yaml
When primary site fails:
1. System checks Priority 2 sites first
2. Then Priority 3 sites
3. Then Priority 4 sites
4. Finally DR sites (Priority 10)
```

---

## ✅ Verification Steps

### **After Adding a Site:**

#### **1. Check Site Status**
```yaml
✅ Site appears in sites list
✅ Site status shows "unknown" initially
✅ Monitoring begins automatically
✅ Site status changes to "healthy" or "unhealthy"
```

#### **2. Test Connectivity**
```yaml
✅ Ping test to site servers
✅ HTTP test to EBO web interface
✅ Database connectivity test
✅ File replication test
```

#### **3. Test Failover**
```yaml
✅ Manual failover test to new site
✅ Verify client redirection
✅ Test database access
✅ Verify file synchronization
```

---

## 🚨 Troubleshooting

### **Common Issues:**

#### **Site Shows as "Unhealthy"**
```yaml
Cause: Network connectivity issues
Solution: Check VPN, routing, firewall rules

Cause: EBO services not running
Solution: Start EBO services on servers

Cause: Incorrect IP addresses
Solution: Verify server IP configuration
```

#### **Site Not Appearing**
```yaml
Cause: Configuration syntax error
Solution: Check YAML formatting

Cause: Duplicate site ID
Solution: Use unique site identifier

Cause: Web interface cache
Solution: Refresh browser or restart service
```

#### **Replication Not Working**
```yaml
Cause: Database connectivity
Solution: Check SQL Server configuration

Cause: File share permissions
Solution: Verify NTFS and share permissions

Cause: Bandwidth limitations
Solution: Check network capacity
```

---

## 📞 Support

### **Getting Help:**
```yaml
Web Interface: Check browser console for errors
Log Files: Check logs/multisite_redundancy.log
Configuration: Validate YAML syntax
Network: Test connectivity between sites
```

### **Best Practices:**
```yaml
✅ Test in development environment first
✅ Document all site configurations
✅ Monitor site health after adding
✅ Test failover procedures regularly
✅ Keep configuration backups
```

---

**Your EBO Multi-Site Redundancy system now supports easy site addition through multiple methods! Add as many sites as needed for your enterprise deployment.** 🌍🚀
