@echo off
title EBO Redundancy Manager - One-Click Installer
color 0A

echo.
echo ===============================================================================
echo                    EBO REDUNDANCY MANAGER - ONE-CLICK INSTALLER
echo ===============================================================================
echo.
echo                Professional EcoStruxure Building Operation Redundancy System
echo                         For Dell PowerEdge R750xa Servers
echo.
echo                        EVERYTHING INSTALLED AUTOMATICALLY!
echo.
echo ===============================================================================
echo.

echo 🚀 Starting automatic installation...
echo.

REM Check for admin rights
net session >nul 2>&1
if errorlevel 1 (
    echo ⚠️  This installer needs administrator privileges.
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo ✅ Running with administrator privileges

echo.
echo [1/6] Installing Python (if needed)...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Downloading and installing Python 3.11...
    
    REM Download Python using PowerShell
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile 'python_installer.exe'}"
    
    if exist python_installer.exe (
        echo Installing Python (this takes 2-3 minutes)...
        python_installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_pip=1
        timeout /t 90 /nobreak >nul
        del python_installer.exe
        
        REM Refresh PATH
        call :RefreshPath
        
        echo ✅ Python installed successfully
    ) else (
        echo ❌ Failed to download Python. Please check internet connection.
        pause
        exit /b 1
    )
) else (
    echo ✅ Python already installed
)

echo.
echo [2/6] Installing required packages...

REM Upgrade pip first
python -m pip install --upgrade pip --quiet

REM Install packages
echo Installing Flask web framework...
python -m pip install flask>=2.0.0 --quiet

echo Installing PyYAML for configuration...
python -m pip install PyYAML>=6.0 --quiet

echo Installing psutil for system monitoring...
python -m pip install psutil>=5.8.0 --quiet

echo Installing requests for networking...
python -m pip install requests>=2.25.0 --quiet

echo Installing additional dependencies...
python -m pip install Jinja2>=3.0.0 Werkzeug>=2.0.0 MarkupSafe>=2.0.0 --quiet

echo ✅ All packages installed

echo.
echo [3/6] Creating application structure...

REM Create directories
if not exist "logs" mkdir logs
if not exist "config" mkdir config
if not exist "data" mkdir data
if not exist "backup" mkdir backup
if not exist "templates" mkdir templates
if not exist "static" mkdir static

echo ✅ Directory structure created

echo.
echo [4/6] Creating configuration files...

REM Create basic EBO configuration
(
echo # EBO Redundancy Configuration
echo applications:
echo   ebo_enterprise_primary:
echo     description: "EBO Enterprise Server - Primary"
echo     priority: 1
echo     is_primary: true
echo     auto_failover: true
echo     server_ip: "************"
echo     server_name: "EBO-PRIMARY"
echo   ebo_enterprise_secondary:
echo     description: "EBO Enterprise Server - Secondary"
echo     priority: 2
echo     is_primary: false
echo     auto_failover: true
echo     server_ip: "************"
echo     server_name: "EBO-SECONDARY"
) > ebo_redundancy_config.yaml

echo ✅ Configuration files created

echo.
echo [5/6] Setting up Windows Firewall...

echo Configuring firewall for EBO Redundancy...
netsh advfirewall firewall add rule name="EBO Redundancy Web" dir=in action=allow protocol=TCP localport=5001 >nul 2>&1
netsh advfirewall firewall add rule name="EBO Heartbeat" dir=in action=allow protocol=UDP localport=5405 >nul 2>&1
netsh advfirewall firewall add rule name="EBO HTTP" dir=in action=allow protocol=TCP localport=80 >nul 2>&1
netsh advfirewall firewall add rule name="EBO HTTPS" dir=in action=allow protocol=TCP localport=443 >nul 2>&1

echo ✅ Firewall configured

echo.
echo [6/6] Creating shortcuts and finalizing...

REM Create desktop shortcut
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\EBO Redundancy Manager.lnk'); $Shortcut.TargetPath = '%CD%\Start_EBO_Redundancy_Manager.bat'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.Description = 'EBO Redundancy Manager'; $Shortcut.Save()}" >nul 2>&1

echo ✅ Desktop shortcut created

echo.
echo ===============================================================================
echo                            INSTALLATION COMPLETE!
echo ===============================================================================
echo.
echo 🎉 EBO Redundancy Manager is now installed and ready to use!
echo.
echo 🚀 How to start:
echo   • Double-click the desktop shortcut "EBO Redundancy Manager"
echo   • Or run: Start_EBO_Redundancy_Manager.bat
echo.
echo 🌐 Web Interface: http://localhost:5001 (after starting the service)
echo.
echo 📋 Quick Start:
echo   1. Double-click desktop shortcut to open the GUI
echo   2. Click "Start Service" in the application
echo   3. Configure your Dell PowerEdge server IPs
echo   4. Access web interface for monitoring
echo.
echo 🔧 Your Dell PowerEdge R750xa servers:
echo   • Update server IPs in Configuration tab
echo   • Set EBO installation paths
echo   • Configure your 5 client PCs
echo.
echo ===============================================================================
echo.

echo Would you like to start EBO Redundancy Manager now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo.
    echo 🚀 Starting EBO Redundancy Manager...
    start "" "%CD%\Start_EBO_Redundancy_Manager.bat"
    echo.
    echo EBO Redundancy Manager is starting in a new window.
    echo You can close this installer window.
) else (
    echo.
    echo You can start EBO Redundancy Manager anytime using the desktop shortcut.
)

echo.
echo Installation completed successfully!
pause

goto :eof

:RefreshPath
REM Refresh PATH environment variable
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH') do set "PATH=%%b"
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "PATH=%PATH%;%%b"
goto :eof
