# 🚀 Quick Start Guide - Hardware Redundancy System

Get your hardware redundancy system up and running in minutes with this comprehensive quick start guide.

## 📋 Prerequisites

- Python 3.7 or higher
- Windows, Linux, or macOS
- Administrator/root privileges (for some monitoring features)

## ⚡ 5-Minute Setup

### 1. Install Dependencies
```bash
# Install core dependencies
pip install -r requirements.txt

# Install web UI dependencies
pip install -r web_ui/requirements.txt
```

### 2. Create Demo Configuration
```bash
# Run the demo setup script
python demo_setup.py
```
This will:
- Analyze your system
- Create a sample configuration
- Set up monitoring for disk, network, and HTTP services

### 3. Start the Web Interface
```bash
# Start the web UI
python start_web_ui.py
```

### 4. Access the Dashboard
Open your browser and go to: **http://localhost:5000**

## 🎯 What You'll See

### Dashboard (http://localhost:5000)
- **System Overview**: Total monitors, health status, last update
- **Monitor Status**: Real-time health of all configured monitors
- **System Information**: Network interfaces and disk usage
- **Control Panel**: Start/stop monitoring, refresh status

### Configuration (http://localhost:5000/config)
- **Add Monitors**: Easy forms for disk, network, and HTTP monitors
- **Global Settings**: Check intervals and logging preferences
- **Monitor Management**: Edit or delete existing monitors

## 🔧 Monitor Types

### 💾 Disk Monitor
Monitors disk usage and alerts when thresholds are exceeded.

**Example Configuration:**
```yaml
- name: SystemDisk
  type: disk
  path: /
  threshold_percentage: 90
```

### 🌐 Network Monitor
Monitors network interface status (up/down).

**Example Configuration:**
```yaml
- name: PrimaryNIC
  type: network
  interface: eth0
  expected_status: up
```

### 🌍 HTTP Monitor
Monitors web service health by checking HTTP endpoints.

**Example Configuration:**
```yaml
- name: WebService
  type: http
  url: https://example.com/health
  expected_status: 200
  timeout_seconds: 10
```

## 🎮 Using the Web Interface

### Starting Monitoring
1. Go to the Dashboard
2. Click **"Start Monitoring"**
3. Watch real-time status updates

### Adding a New Monitor
1. Go to Configuration page
2. Click **"Add Monitor"**
3. Fill in the form:
   - **Name**: Unique identifier
   - **Type**: Disk, Network, or HTTP
   - **Parameters**: Specific to monitor type
4. Click **"Add Monitor"**
5. Click **"Save Configuration"**

### Viewing Monitor Status
- **Green indicator**: Monitor is healthy
- **Red indicator**: Monitor detected an issue
- **Gray indicator**: Monitor not checked yet

## 🔄 Command Line Usage

### Run Monitoring (Without Web UI)
```bash
# Run the core monitoring system
python main.py
```

### Test Individual Monitors
```bash
# Test disk monitor
python -m src.redundancy.monitors.disk_monitor

# Test network monitor
python -m src.redundancy.monitors.network_monitor

# Test HTTP monitor
python -m src.redundancy.monitors.http_monitor
```

### Run Tests
```bash
# Run all tests
python -m pytest src/tests/ -v

# Run specific monitor tests
python -m pytest src/tests/monitors/test_network_monitor.py -v
```

## 📊 Real-World Examples

### High Availability Web Server
```yaml
monitors:
  - name: WebServerDisk
    type: disk
    path: /var/www
    threshold_percentage: 85
    
  - name: WebServerNIC
    type: network
    interface: eth0
    expected_status: up
    
  - name: WebServerHealth
    type: http
    url: http://localhost:80/health
    expected_status: 200
    
  - name: DatabaseHealth
    type: http
    url: http://localhost:3306/ping
    expected_status: 200
```

### Database Server Monitoring
```yaml
monitors:
  - name: DatabaseDisk
    type: disk
    path: /var/lib/mysql
    threshold_percentage: 80
    
  - name: DatabaseNetwork
    type: network
    interface: eth0
    expected_status: up
    
  - name: DatabaseAPI
    type: http
    url: http://localhost:8080/api/health
    expected_status: 200
```

### Development Environment
```yaml
monitors:
  - name: DevDisk
    type: disk
    path: /home/<USER>
    threshold_percentage: 95
    
  - name: DevNetwork
    type: network
    interface: wlan0
    expected_status: up
    
  - name: LocalAPI
    type: http
    url: http://localhost:3000/api/status
    expected_status: 200
```

## 🛠️ Customization

### Adjust Check Intervals
```yaml
global_settings:
  check_interval_seconds: 60  # Check every minute
```

### Configure Logging
```yaml
logging:
  log_level: DEBUG
  log_file: logs/my_app.log
  log_to_console: true
  log_to_file: true
```

### Set Custom Thresholds
```yaml
monitors:
  - name: CriticalDisk
    type: disk
    path: /critical
    threshold_percentage: 75  # Alert at 75% instead of 90%
```

## 🚨 Troubleshooting

### Web UI Won't Start
```bash
# Check if port is in use
python start_web_ui.py --port 8080

# Install dependencies
pip install -r web_ui/requirements.txt
```

### Monitors Not Working
1. Check configuration syntax in `config.yaml`
2. Verify paths and interfaces exist
3. Check permissions for disk/network access
4. Review logs in `logs/redundancy_app.log`

### Permission Issues
```bash
# Run with elevated privileges (if needed)
sudo python main.py  # Linux/Mac
# Run as Administrator on Windows
```

## 📈 Next Steps

### Production Deployment
1. **Security**: Change Flask secret key, add authentication
2. **Reverse Proxy**: Use Nginx or Apache for HTTPS
3. **Process Management**: Use systemd, supervisor, or PM2
4. **Monitoring**: Set up log rotation and alerting

### Advanced Features
1. **Email Alerts**: Add SMTP configuration for notifications
2. **Multiple Servers**: Monitor remote systems via HTTP
3. **Custom Monitors**: Create new monitor types
4. **Historical Data**: Add database for trend analysis

### Integration
1. **CI/CD**: Include monitoring in deployment pipelines
2. **Alerting**: Integrate with PagerDuty, Slack, or Teams
3. **Metrics**: Export to Prometheus, Grafana, or DataDog
4. **Automation**: Trigger remediation scripts on failures

## 📞 Support

- **Documentation**: See README.md and WEB_UI_README.md
- **Issues**: Check logs in `logs/` directory
- **Testing**: Run `python -m pytest src/tests/ -v`
- **Configuration**: Use the web interface for easy setup

## 🎉 Success!

You now have a fully functional hardware redundancy monitoring system! 

- ✅ Real-time monitoring of critical components
- ✅ Web-based configuration and dashboard
- ✅ Automated health checks and alerting
- ✅ Extensible architecture for custom monitors

Your system is now protecting against hardware failures and ensuring high availability! 🛡️
