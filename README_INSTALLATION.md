# 📦 SafeKit-Style Redundancy Management System - Installation Guide

## 🎯 Overview
Professional redundancy management system for EcoStruxure Building Operation (EBO) and other critical applications. Provides Microsoft Failover Cluster-style functionality with web-based management interface.

## 📋 System Requirements

### Minimum Requirements
- **Operating System**: Windows Server 2016/2019/2022 or Windows 10/11 Pro
- **Memory**: 4 GB RAM minimum, 8 GB recommended
- **Storage**: 2 GB free disk space
- **Network**: TCP/IP connectivity between cluster nodes
- **Python**: 3.8 or higher (will be installed if missing)

### Recommended Production Setup
- **Primary Server**: Windows Server 2022, 16 GB RAM, SSD storage
- **Secondary Server**: Windows Server 2022, 16 GB RAM, SSD storage
- **Network**: Dedicated heartbeat network + production network
- **Firewall**: Port 5002 open for web interface access

## 🚀 Installation Methods

### Method 1: Automated Installation (Recommended)

1. **Download** the complete package (ZIP file)
2. **Extract** to desired location (e.g., `C:\SafeKitRedundancy\`)
3. **Right-click** on `install.bat` → **Run as administrator**
4. **Follow** the installation prompts
5. **Start** the system using `start_redundancy_system.bat`

### Method 2: Manual Installation

1. **Install Python 3.8+** from https://python.org
2. **Install dependencies**:
   ```cmd
   pip install flask flask-cors psutil requests werkzeug
   ```
3. **Create directories**:
   ```cmd
   mkdir logs config data backup
   ```
4. **Run the application**:
   ```cmd
   python multisite_web_interface.py
   ```

## 🔧 Configuration

### Network Configuration
- **Default Port**: 5002
- **Access URL**: `http://[server-ip]:5002`
- **SafeKit Console**: `http://[server-ip]:5002/safekit-console`

### Firewall Settings
```cmd
netsh advfirewall firewall add rule name="SafeKit Redundancy" dir=in action=allow protocol=TCP localport=5002
```

### Service Installation (Optional)
Run as administrator:
```cmd
install_service.bat
```

## 🎯 Quick Start Guide

### 1. Start the System
```cmd
# Option A: Manual start
start_redundancy_system.bat

# Option B: Quick start (opens browser)
quick_start.bat

# Option C: Service start
net start SafeKitRedundancy
```

### 2. Access Web Interface
- **Main Dashboard**: http://localhost:5002
- **SafeKit Console**: http://localhost:5002/safekit-console
- **Site Management**: http://localhost:5002/sites
- **Add New Site**: http://localhost:5002/add-site

### 3. Initial Configuration

#### A. Configure First Cluster Node
1. Go to **SafeKit Console** → **Nodes** tab
2. Click **Add Node**
3. Fill in node details:
   - **Node Name**: `EBO-PRIMARY`
   - **IP Address**: `*********`
   - **Role**: `Primary`
   - **Priority**: `1000`

#### B. Add Directory Replication
1. Go to **Replication** tab
2. Click **Add Directory**
3. Configure EBO data replication:
   - **Path**: `C:\Program Files\Schneider Electric\EcoStruxure Building Operation\Data`
   - **Mode**: `Synchronous (Real-time)`
   - **Priority**: `High`

#### C. Add Site Configuration
1. Go to **Site Management** → **Add Site**
2. Fill in complete site information
3. Configure network and server details

## 🔍 Verification

### Check Installation
1. **Web Interface**: Browse to http://localhost:5002
2. **Console Access**: Verify SafeKit console loads
3. **Node Management**: Test adding/editing nodes
4. **Directory Management**: Test replication configuration

### Test Functionality
1. **Add test nodes** and verify they appear in the interface
2. **Configure test directories** for replication
3. **Test manual failover** operations
4. **Verify logging** and monitoring features

## 🌐 Network Access

### Local Access
- Use `localhost` or `127.0.0.1`
- Example: http://localhost:5002

### Remote Access
- Use server's IP address
- Example: http://*************:5002
- Ensure firewall allows port 5002

### Multi-Site Access
- Configure each site with unique IP ranges
- Use VPN for secure inter-site communication
- Configure WAN IPs for external access

## 🛠 Troubleshooting

### Common Issues

#### Python Not Found
```
Error: Python is not installed or not in PATH
Solution: Install Python 3.8+ and add to PATH
```

#### Port Already in Use
```
Error: Port 5002 is already in use
Solution: Stop other services or change port in config
```

#### Permission Denied
```
Error: Access denied
Solution: Run as administrator
```

### Log Files
- **Application Logs**: `logs\application.log`
- **Error Logs**: `logs\error.log`
- **Access Logs**: `logs\access.log`

## 📞 Support

### Documentation
- **User Manual**: Available in web interface
- **API Documentation**: http://localhost:5002/api/docs
- **Configuration Guide**: Built-in help system

### System Information
- **Version**: 1.0.0
- **Build**: Production Ready
- **License**: Enterprise Use
- **Support**: Professional installation support available

## 🔄 Updates

### Updating the System
1. **Stop** the current service
2. **Backup** configuration files
3. **Replace** application files
4. **Restart** the service
5. **Verify** functionality

### Backup Recommendations
- **Daily**: Configuration files
- **Weekly**: Complete system backup
- **Before Updates**: Full system snapshot

---

**Ready for Production Deployment!** 🚀

This system provides enterprise-grade redundancy management with professional web interface, comprehensive monitoring, and Microsoft Failover Cluster-style functionality.
