# Professional Hardware Redundancy Management System
## Complete User Manual

### 🎯 Overview

This is a production-ready hardware redundancy management system designed to ensure high availability of your critical applications. The system provides:

- **Real-time monitoring** of application health
- **Automatic failover** when issues are detected
- **Professional web interface** for configuration and monitoring
- **Comprehensive logging** and event tracking
- **Manual override** capabilities for testing and maintenance

### 🚀 Quick Start

#### 1. System Requirements
- Python 3.7 or higher
- 2GB RAM minimum (4GB recommended)
- 1GB free disk space
- Network connectivity for monitoring

#### 2. Installation
```bash
# Download or clone the system files
# Navigate to the system directory
cd redundancy-system

# Start the system (auto-installs dependencies)
python start_redundancy_system.py --install-deps
```

#### 3. First Access
- Open your browser to: **http://localhost:5001**
- You'll see the professional dashboard
- Default configuration includes two example applications

### 📊 Web Interface Guide

#### Dashboard (Main Page)
- **System Metrics**: Overview of applications and monitors
- **Application Status**: Real-time health of all applications
- **Redundancy States**: Current failover status
- **Recent Events**: Latest system activities
- **Control Panel**: Start/stop monitoring, manual failover

#### Applications Page
- **Add Applications**: Configure new applications to monitor
- **Edit Applications**: Modify existing application settings
- **Remove Applications**: Delete applications from monitoring
- **View Monitors**: See monitors assigned to each application

#### Monitors Page
- **Add Monitors**: Create disk, network, or HTTP monitors
- **Test Monitors**: Manually test monitor functionality
- **Monitor Results**: View historical monitoring data

#### Events Page
- **Event Log**: Complete history of system events
- **Filter Events**: Search by application, type, or severity
- **Export Events**: Download event logs for analysis

### 🔧 Configuration Guide

#### Application Configuration

**Basic Settings:**
- **Name**: Unique identifier for your application
- **Description**: Brief description of the application's purpose
- **Priority**: 1 (Critical) to 5 (Normal) - affects failover order
- **Primary/Secondary**: Designate primary and backup applications

**Redundancy Settings:**
- **Auto-Failover**: Enable automatic failover on failure
- **Failover Target**: Which application to failover to
- **Failover Threshold**: Number of consecutive failures before failover
- **Recovery Threshold**: Number of consecutive successes for recovery

#### Monitor Types

**1. Disk Monitor**
```yaml
- name: "disk_monitor"
  type: "disk"
  params:
    path: "/"                    # Path to monitor
    threshold_percentage: 90     # Alert when usage exceeds this %
```

**2. Network Monitor**
```yaml
- name: "network_monitor"
  type: "network"
  params:
    interface_name: "eth0"       # Network interface to monitor
    expected_status: "up"        # Expected status (up/down)
```

**3. HTTP Monitor**
```yaml
- name: "http_monitor"
  type: "http"
  params:
    url: "http://localhost:8080/health"  # URL to check
    expected_status_code: 200            # Expected HTTP status
    timeout_seconds: 10                  # Request timeout
```

### 🏗️ Real-World Setup Examples

#### Example 1: Web Application with Database
```yaml
applications:
  web_primary:
    description: "Primary Web Server"
    priority: 1
    is_primary: true
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: "web_secondary"
    monitors:
      - name: "web_disk"
        type: "disk"
        params:
          path: "/var/www"
          threshold_percentage: 85
      - name: "web_network"
        type: "network"
        params:
          interface_name: "eth0"
          expected_status: "up"
      - name: "web_health"
        type: "http"
        params:
          url: "http://localhost:80/health"
          expected_status_code: 200
          timeout_seconds: 5
      - name: "database_health"
        type: "http"
        params:
          url: "http://localhost:3306/ping"
          expected_status_code: 200
          timeout_seconds: 10

  web_secondary:
    description: "Secondary Web Server (Backup)"
    priority: 2
    is_primary: false
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: null
    monitors:
      - name: "backup_disk"
        type: "disk"
        params:
          path: "/var/backup"
          threshold_percentage: 90
      - name: "backup_network"
        type: "network"
        params:
          interface_name: "eth1"
          expected_status: "up"
```

#### Example 2: Microservices Architecture
```yaml
applications:
  api_service:
    description: "Main API Service"
    priority: 1
    is_primary: true
    auto_failover: true
    failover_threshold: 2
    recovery_threshold: 3
    failover_target: "api_backup"
    monitors:
      - name: "api_health"
        type: "http"
        params:
          url: "http://localhost:8080/api/health"
          expected_status_code: 200
          timeout_seconds: 5
      - name: "api_disk"
        type: "disk"
        params:
          path: "/opt/api"
          threshold_percentage: 80

  auth_service:
    description: "Authentication Service"
    priority: 1
    is_primary: true
    auto_failover: true
    failover_threshold: 2
    recovery_threshold: 3
    failover_target: "auth_backup"
    monitors:
      - name: "auth_health"
        type: "http"
        params:
          url: "http://localhost:8081/auth/health"
          expected_status_code: 200
          timeout_seconds: 5
```

### 🔄 Failover Process

#### Automatic Failover
1. **Detection**: Monitor detects failure
2. **Threshold Check**: System counts consecutive failures
3. **Failover Decision**: When threshold reached, failover initiates
4. **Execution**: Custom failover scripts execute
5. **Verification**: System verifies failover success
6. **Notification**: Events logged and alerts sent

#### Manual Failover
1. Go to Dashboard
2. Click "Manual Failover" button
3. Select source and target applications
4. Confirm the action
5. Monitor the failover process in Events

#### Recovery Process
1. **Health Restoration**: Primary application becomes healthy
2. **Recovery Threshold**: System counts consecutive successes
3. **Recovery Decision**: When threshold reached, recovery initiates
4. **Execution**: Custom recovery scripts execute
5. **State Update**: System updates redundancy states

### 🛠️ Customization

#### Custom Failover Scripts
Edit the `_execute_failover_script` method in `redundancy_manager.py`:

```python
def _execute_failover_script(self, failed_app: str, target_app: str):
    """Your custom failover logic"""
    
    # Example: Stop failed service
    os.system(f"systemctl stop {failed_app}")
    
    # Example: Start backup service
    os.system(f"systemctl start {target_app}")
    
    # Example: Update load balancer
    self._update_load_balancer(target_app)
    
    # Example: Send notifications
    self._send_alert(f"Failover: {failed_app} -> {target_app}")
```

#### Custom Recovery Scripts
Edit the `_execute_recovery_script` method:

```python
def _execute_recovery_script(self, recovered_app: str):
    """Your custom recovery logic"""
    
    # Example: Start primary service
    os.system(f"systemctl start {recovered_app}")
    
    # Example: Update configuration
    self._update_primary_config(recovered_app)
    
    # Example: Send notifications
    self._send_alert(f"Recovery completed: {recovered_app}")
```

### 📈 Monitoring and Alerting

#### Built-in Monitoring
- **Health Checks**: Every 10 seconds
- **Status Updates**: Real-time web interface updates
- **Event Logging**: Comprehensive event tracking
- **Performance Metrics**: Response times and success rates

#### Integration Options
- **Email Alerts**: Configure SMTP for email notifications
- **Slack/Teams**: Webhook integration for team notifications
- **Syslog**: Forward events to centralized logging
- **Prometheus**: Export metrics for monitoring systems

### 🔒 Security Considerations

#### Production Deployment
1. **Change Secret Key**: Update Flask secret key in `redundancy_web_ui.py`
2. **Enable HTTPS**: Use reverse proxy with SSL certificates
3. **Authentication**: Add user authentication for web interface
4. **Firewall**: Restrict access to trusted networks
5. **Permissions**: Run with minimal required privileges

#### Network Security
- Use VPN for remote access
- Implement network segmentation
- Monitor access logs
- Regular security updates

### 🐛 Troubleshooting

#### Common Issues

**Web Interface Won't Start**
```bash
# Check if port is in use
netstat -an | grep :5001

# Try different port
python start_redundancy_system.py --port 8080

# Check dependencies
python start_redundancy_system.py --check-only
```

**Monitors Not Working**
1. Check monitor configuration syntax
2. Verify paths and URLs exist
3. Check network connectivity
4. Review logs in `logs/redundancy_manager.log`

**Failover Not Triggering**
1. Verify failover thresholds are set correctly
2. Check auto-failover is enabled
3. Ensure failover target is configured
4. Review failure counts in dashboard

**Configuration Errors**
1. Validate YAML syntax
2. Check required fields are present
3. Verify application names are unique
4. Test monitor parameters manually

#### Log Files
- **System Logs**: `logs/redundancy_manager.log`
- **Web Interface**: Console output
- **Monitor Results**: Available in web interface

### 📞 Support and Maintenance

#### Regular Maintenance
- **Log Rotation**: Implement log rotation for large deployments
- **Database Cleanup**: Clear old events periodically
- **Configuration Backup**: Regular backup of configuration files
- **System Updates**: Keep dependencies updated

#### Performance Tuning
- **Check Intervals**: Adjust monitoring frequency based on needs
- **Threshold Tuning**: Optimize failover/recovery thresholds
- **Resource Monitoring**: Monitor system resource usage
- **Network Optimization**: Optimize network timeouts

### 🎯 Best Practices

1. **Start Simple**: Begin with basic disk and HTTP monitors
2. **Test Thoroughly**: Use manual failover to test your setup
3. **Monitor the Monitor**: Ensure the redundancy system itself is monitored
4. **Document Changes**: Keep track of configuration changes
5. **Regular Testing**: Perform regular failover drills
6. **Gradual Rollout**: Deploy to non-critical systems first
7. **Team Training**: Ensure team understands the system

### 📋 Checklist for Production

- [ ] System requirements verified
- [ ] Dependencies installed
- [ ] Configuration customized for your environment
- [ ] Failover scripts implemented and tested
- [ ] Security measures implemented
- [ ] Monitoring and alerting configured
- [ ] Team trained on system operation
- [ ] Documentation updated
- [ ] Backup and recovery procedures established
- [ ] Regular maintenance schedule created

Your Professional Hardware Redundancy Management System is now ready to ensure high availability of your critical applications! 🛡️
