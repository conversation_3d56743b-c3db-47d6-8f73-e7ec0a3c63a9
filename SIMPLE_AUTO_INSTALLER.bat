@echo off
title EBO Redundancy Manager - Simple Auto Installer
color 0A

echo.
echo ===============================================================================
echo                    EBO REDUNDANCY MANAGER - AUTO INSTALLER
echo ===============================================================================
echo.
echo                Professional EcoStruxure Building Operation Redundancy System
echo                         For Dell PowerEdge R750xa Servers
echo.
echo                        AUTOMATIC INSTALLATION - NO MANUAL STEPS!
echo.
echo ===============================================================================
echo.

echo Starting automatic installation...
echo.

REM Step 1: Check Python
echo [1/4] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo Python not found. Installing Python automatically...
    echo.
    echo Downloading Python 3.11...
    powershell -Command "Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile 'python_installer.exe'"
    
    if exist python_installer.exe (
        echo Installing Python (this may take 3-5 minutes)...
        python_installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_pip=1
        timeout /t 120 /nobreak >nul
        del python_installer.exe
        echo Python installation completed!
    ) else (
        echo Failed to download Python. Please install manually from python.org
        pause
        exit /b 1
    )
) else (
    echo Python is already installed!
)

echo.
echo [2/4] Installing all required packages automatically...

REM Install packages one by one with error checking
echo Installing Flask web framework...
python -m pip install flask>=2.0.0 --quiet --disable-pip-version-check

echo Installing PyYAML for configuration...
python -m pip install PyYAML>=6.0 --quiet --disable-pip-version-check

echo Installing psutil for system monitoring...
python -m pip install psutil>=5.8.0 --quiet --disable-pip-version-check

echo Installing requests for networking...
python -m pip install requests>=2.25.0 --quiet --disable-pip-version-check

echo Installing web framework dependencies...
python -m pip install Jinja2>=3.0.0 --quiet --disable-pip-version-check
python -m pip install Werkzeug>=2.0.0 --quiet --disable-pip-version-check
python -m pip install MarkupSafe>=2.0.0 --quiet --disable-pip-version-check

echo All packages installed successfully!

echo.
echo [3/4] Setting up application environment...

REM Create directories
if not exist "logs" mkdir logs
if not exist "config" mkdir config
if not exist "data" mkdir data
if not exist "backup" mkdir backup
if not exist "templates" mkdir templates
if not exist "static" mkdir static

REM Create basic configuration if it doesn't exist
if not exist "ebo_redundancy_config.yaml" (
    echo Creating default configuration...
    (
    echo # EBO Redundancy Configuration - Update with your server details
    echo applications:
    echo   ebo_enterprise_primary:
    echo     description: "EBO Enterprise Server - Primary"
    echo     server_ip: "************"
    echo     server_name: "EBO-PRIMARY"
    echo   ebo_enterprise_secondary:
    echo     description: "EBO Enterprise Server - Secondary"
    echo     server_ip: "************"
    echo     server_name: "EBO-SECONDARY"
    ) > ebo_redundancy_config.yaml
)

echo Environment setup completed!

echo.
echo [4/4] Finalizing installation...

REM Configure Windows Firewall
echo Configuring Windows Firewall...
netsh advfirewall firewall add rule name="EBO Redundancy Web" dir=in action=allow protocol=TCP localport=5001 >nul 2>&1
netsh advfirewall firewall add rule name="EBO Heartbeat" dir=in action=allow protocol=UDP localport=5405 >nul 2>&1

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "try { $WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\EBO Redundancy Manager.lnk'); $Shortcut.TargetPath = '%CD%\Start_EBO_Redundancy_Manager.bat'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.Description = 'EBO Redundancy Manager'; $Shortcut.Save() } catch { }" >nul 2>&1

echo Installation completed!

echo.
echo ===============================================================================
echo                            INSTALLATION SUCCESSFUL!
echo ===============================================================================
echo.
echo EBO Redundancy Manager has been installed and is ready to use!
echo.
echo HOW TO START:
echo   1. Double-click desktop shortcut "EBO Redundancy Manager"
echo   2. Or run: Start_EBO_Redundancy_Manager.bat
echo.
echo WHAT HAPPENS NEXT:
echo   1. GUI application will open
echo   2. Click "Start Service" to begin monitoring
echo   3. Configure your Dell PowerEdge server IPs
echo   4. Access web interface at: http://localhost:5001
echo.
echo YOUR DELL POWEREDGE R750XA SERVERS:
echo   - Update server IPs in the Configuration tab
echo   - Set EBO installation paths
echo   - Configure your 5 client PCs
echo   - Test failover functionality
echo.
echo FEATURES READY:
echo   - Real-time EBO service monitoring
echo   - Automatic failover between servers
echo   - Database redundancy management
echo   - Storage synchronization
echo   - Client PC connection management
echo   - Professional web interface
echo   - Enterprise-grade logging
echo.
echo ===============================================================================
echo.

set /p start_now="Would you like to start EBO Redundancy Manager now? (Y/N): "
if /i "%start_now%"=="Y" (
    echo.
    echo Starting EBO Redundancy Manager...
    start "" "%CD%\Start_EBO_Redundancy_Manager.bat"
    echo.
    echo EBO Redundancy Manager is starting in a new window.
    echo You can close this installer window.
    echo.
) else (
    echo.
    echo You can start EBO Redundancy Manager anytime using:
    echo - Desktop shortcut "EBO Redundancy Manager"
    echo - Or run Start_EBO_Redundancy_Manager.bat
    echo.
)

echo Installation completed successfully!
echo Thank you for using EBO Redundancy Manager!
echo.
pause
