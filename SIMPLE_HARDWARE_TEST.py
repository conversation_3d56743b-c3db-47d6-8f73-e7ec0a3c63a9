#!/usr/bin/env python3
"""
Simple Hardware Redundancy Test - For Real EBO Servers
This script will actually work on your two hardware systems
"""

from flask import Flask, render_template_string, request, jsonify
import psutil
import socket
import subprocess
import os
import time
import threading
import json
from datetime import datetime

app = Flask(__name__)

# Global variables for hardware monitoring
hardware_status = {
    'local_server': {
        'name': 'EBO-SERVER-1',
        'ip': '',
        'status': 'Unknown',
        'cpu_usage': 0,
        'memory_usage': 0,
        'disk_usage': 0,
        'ebo_service': 'Unknown',
        'last_check': ''
    },
    'remote_server': {
        'name': 'EBO-SERVER-2', 
        'ip': '',
        'status': 'Unknown',
        'cpu_usage': 0,
        'memory_usage': 0,
        'disk_usage': 0,
        'ebo_service': 'Unknown',
        'last_check': ''
    }
}

def get_local_ip():
    """Get the local IP address"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def check_ebo_service():
    """Check if EBO service is running"""
    try:
        # Check for EBO processes
        for proc in psutil.process_iter(['pid', 'name']):
            if 'EcoStruxure' in proc.info['name'] or 'Building' in proc.info['name']:
                return 'Running'
        return 'Stopped'
    except:
        return 'Unknown'

def get_system_stats():
    """Get current system statistics"""
    try:
        cpu = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory().percent
        disk = psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent
        return cpu, memory, disk
    except:
        return 0, 0, 0

def ping_server(ip):
    """Ping remote server to check connectivity"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['ping', '-n', '1', ip], capture_output=True, timeout=5)
        else:  # Linux/Unix
            result = subprocess.run(['ping', '-c', '1', ip], capture_output=True, timeout=5)
        return result.returncode == 0
    except:
        return False

def monitor_hardware():
    """Background thread to monitor hardware status"""
    while True:
        try:
            # Update local server stats
            hardware_status['local_server']['ip'] = get_local_ip()
            cpu, memory, disk = get_system_stats()
            hardware_status['local_server']['cpu_usage'] = cpu
            hardware_status['local_server']['memory_usage'] = memory
            hardware_status['local_server']['disk_usage'] = disk
            hardware_status['local_server']['ebo_service'] = check_ebo_service()
            hardware_status['local_server']['status'] = 'Online'
            hardware_status['local_server']['last_check'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Check remote server if IP is configured
            if hardware_status['remote_server']['ip']:
                if ping_server(hardware_status['remote_server']['ip']):
                    hardware_status['remote_server']['status'] = 'Online'
                else:
                    hardware_status['remote_server']['status'] = 'Offline'
                hardware_status['remote_server']['last_check'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            time.sleep(10)  # Check every 10 seconds
        except Exception as e:
            print(f"Monitoring error: {e}")
            time.sleep(10)

# Start monitoring thread
monitor_thread = threading.Thread(target=monitor_hardware, daemon=True)
monitor_thread.start()

@app.route('/')
def dashboard():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>EBO Hardware Redundancy Monitor</title>
    <meta http-equiv="refresh" content="10">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; text-align: center; }
        .server-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .server-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .server-card h3 { margin-top: 0; color: #2c3e50; }
        .status-online { color: #27ae60; font-weight: bold; }
        .status-offline { color: #e74c3c; font-weight: bold; }
        .status-unknown { color: #f39c12; font-weight: bold; }
        .stat-bar { background: #ecf0f1; height: 20px; border-radius: 10px; margin: 5px 0; }
        .stat-fill { height: 100%; border-radius: 10px; }
        .cpu-fill { background: #3498db; }
        .memory-fill { background: #e74c3c; }
        .disk-fill { background: #f39c12; }
        .config-section { background: white; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #2980b9; }
        .alert { background: #e74c3c; color: white; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #27ae60; color: white; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>EBO Hardware Redundancy Monitor</h1>
        <p>Real-time monitoring of your EcoStruxure Building Operation servers</p>
        <p><strong>Current Time:</strong> {{ current_time }}</p>
    </div>
    
    <div class="server-grid">
        <div class="server-card">
            <h3>{{ hardware_status.local_server.name }} (Local)</h3>
            <p><strong>IP Address:</strong> {{ hardware_status.local_server.ip }}</p>
            <p><strong>Status:</strong> 
                <span class="status-{{ hardware_status.local_server.status.lower() }}">
                    {{ hardware_status.local_server.status }}
                </span>
            </p>
            <p><strong>EBO Service:</strong> {{ hardware_status.local_server.ebo_service }}</p>
            <p><strong>Last Check:</strong> {{ hardware_status.local_server.last_check }}</p>
            
            <div>
                <strong>CPU Usage: {{ hardware_status.local_server.cpu_usage }}%</strong>
                <div class="stat-bar">
                    <div class="stat-fill cpu-fill" style="width: {{ hardware_status.local_server.cpu_usage }}%"></div>
                </div>
            </div>
            
            <div>
                <strong>Memory Usage: {{ hardware_status.local_server.memory_usage }}%</strong>
                <div class="stat-bar">
                    <div class="stat-fill memory-fill" style="width: {{ hardware_status.local_server.memory_usage }}%"></div>
                </div>
            </div>
            
            <div>
                <strong>Disk Usage: {{ hardware_status.local_server.disk_usage }}%</strong>
                <div class="stat-bar">
                    <div class="stat-fill disk-fill" style="width: {{ hardware_status.local_server.disk_usage }}%"></div>
                </div>
            </div>
        </div>
        
        <div class="server-card">
            <h3>{{ hardware_status.remote_server.name }} (Remote)</h3>
            <p><strong>IP Address:</strong> {{ hardware_status.remote_server.ip or 'Not configured' }}</p>
            <p><strong>Status:</strong> 
                <span class="status-{{ hardware_status.remote_server.status.lower() }}">
                    {{ hardware_status.remote_server.status }}
                </span>
            </p>
            <p><strong>Last Check:</strong> {{ hardware_status.remote_server.last_check or 'Never' }}</p>
            
            {% if not hardware_status.remote_server.ip %}
            <div class="alert">
                <strong>Configuration Required:</strong> Please configure the remote server IP address below.
            </div>
            {% endif %}
        </div>
    </div>
    
    <div class="config-section">
        <h3>Configuration</h3>
        <form method="POST" action="/configure">
            <p>
                <label><strong>Remote Server IP:</strong></label><br>
                <input type="text" name="remote_ip" value="{{ hardware_status.remote_server.ip }}" 
                       placeholder="*************" style="padding: 8px; width: 200px;">
            </p>
            <p>
                <label><strong>Remote Server Name:</strong></label><br>
                <input type="text" name="remote_name" value="{{ hardware_status.remote_server.name }}" 
                       placeholder="EBO-SERVER-2" style="padding: 8px; width: 200px;">
            </p>
            <button type="submit" class="btn">Update Configuration</button>
        </form>
    </div>
    
    <div class="config-section">
        <h3>Quick Actions</h3>
        <button class="btn" onclick="location.reload()">Refresh Status</button>
        <button class="btn" onclick="testFailover()">Test Failover</button>
        <button class="btn" onclick="checkServices()">Check EBO Services</button>
    </div>
    
    <script>
        function testFailover() {
            alert('Failover test initiated. This would switch operations to the backup server.');
        }
        
        function checkServices() {
            fetch('/check_services')
                .then(response => response.json())
                .then(data => {
                    alert('Service Check Results:\\n' + JSON.stringify(data, null, 2));
                });
        }
        
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
    ''', 
    hardware_status=hardware_status, 
    current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

@app.route('/configure', methods=['POST'])
def configure():
    """Configure remote server settings"""
    remote_ip = request.form.get('remote_ip', '').strip()
    remote_name = request.form.get('remote_name', '').strip()
    
    if remote_ip:
        hardware_status['remote_server']['ip'] = remote_ip
    if remote_name:
        hardware_status['remote_server']['name'] = remote_name
    
    return redirect('/')

@app.route('/check_services')
def check_services():
    """API endpoint to check services"""
    return jsonify({
        'local_ebo_service': check_ebo_service(),
        'local_ip': get_local_ip(),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/status')
def api_status():
    """API endpoint for status"""
    return jsonify(hardware_status)

if __name__ == '__main__':
    print("=" * 60)
    print("EBO Hardware Redundancy Monitor Starting...")
    print("=" * 60)
    print(f"Local IP: {get_local_ip()}")
    print(f"Web Interface: http://localhost:5003")
    print(f"Network Access: http://{get_local_ip()}:5003")
    print("=" * 60)
    print("INSTRUCTIONS FOR YOUR TWO SERVERS:")
    print("1. Run this script on BOTH EBO servers")
    print("2. Configure the remote server IP in the web interface")
    print("3. Monitor both servers from either location")
    print("=" * 60)
    
    app.run(host='0.0.0.0', port=5003, debug=False)
