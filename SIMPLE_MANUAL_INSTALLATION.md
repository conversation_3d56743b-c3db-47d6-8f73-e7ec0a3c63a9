# 🚀 SafeKit Redundancy System - Simple Manual Installation

## ❌ **If Automated Installer Fails**

If you're seeing pip errors or the automated installer isn't working, follow these **simple manual steps**:

## 📋 **Manual Installation Steps**

### **Step 1: Check Python**
Open Command Prompt and type:
```cmd
python --version
```
- ✅ **If you see Python 3.x**: Continue to Step 2
- ❌ **If you get an error**: Install Python from https://python.org/downloads/
  - **IMPORTANT**: Check "Add Python to PATH" during installation

### **Step 2: Install Dependencies Manually**
In Command Prompt, run these commands **one by one**:
```cmd
python -m pip install flask
python -m pip install flask-cors
python -m pip install psutil
python -m pip install requests
```

### **Step 3: Start the System**
Navigate to the extracted folder and run:
```cmd
python multisite_web_interface.py
```

### **Step 4: Access Web Interface**
Open your browser and go to:
- **SafeKit Console**: `http://localhost:5002/safekit-console`
- **Main Dashboard**: `http://localhost:5002`

## 🎯 **Quick Test Method**

If you want to test immediately without installation:

1. **Extract** the ZIP file: `SafeKit_Redundancy_System_v1.0.zip`
2. **Open Command Prompt** in the extracted folder
3. **Run**: `python multisite_web_interface.py`
4. **Open browser**: `http://localhost:5002/safekit-console`

## 🔧 **Troubleshooting**

### **Problem: "python is not recognized"**
**Solution**: Install Python and add to PATH, or try:
```cmd
py multisite_web_interface.py
```

### **Problem: "No module named flask"**
**Solution**: Install dependencies:
```cmd
python -m pip install flask flask-cors psutil requests
```

### **Problem: "Permission denied"**
**Solution**: Run Command Prompt as Administrator

### **Problem: "Port already in use"**
**Solution**: Close other applications using port 5002, or restart computer

## 📁 **What's in the Package**

Your extracted folder should contain:
```
SafeKit_Redundancy_System_v1.0/
├── multisite_web_interface.py    ← Main application file
├── redundancy_manager.py         ← Core logic
├── redundancy_web_ui.py          ← Alternative UI
├── install.bat                   ← Automated installer
├── quick_start.bat              ← Quick launcher
├── start_redundancy_system.bat  ← Manual launcher
├── requirements.txt             ← Dependencies list
├── README_INSTALLATION.md       ← Detailed guide
├── logs/                        ← Log files
├── config/                      ← Configuration
├── data/                        ← Application data
└── backup/                      ← Backup storage
```

## ✅ **Success Indicators**

When working correctly, you should see:
```
* Serving Flask app 'multisite_web_interface'
* Debug mode: on
* Running on all addresses (0.0.0.0)
* Running on http://127.0.0.1:5002
```

## 🌐 **Access Points**

Once running, access these URLs:

| **Interface** | **URL** | **Purpose** |
|---------------|---------|-------------|
| **SafeKit Console** | `http://localhost:5002/safekit-console` | **Main management interface** |
| **Dashboard** | `http://localhost:5002` | System overview |
| **Sites** | `http://localhost:5002/sites` | Multi-site management |
| **Add Site** | `http://localhost:5002/add-site` | Site configuration |

## 🎉 **You're Ready!**

Once you can access `http://localhost:5002/safekit-console`, you can:

- ✅ **Add cluster nodes** (EBO servers)
- ✅ **Configure directory replication** (EBO data)
- ✅ **Test manual failover** procedures
- ✅ **Monitor real-time status** of your systems
- ✅ **Manage multi-site redundancy** for geographic distribution

## 📞 **Still Having Issues?**

If manual installation still doesn't work:

1. **Check Windows version** - Ensure you have Windows 10/11 or Server 2016+
2. **Try different Python version** - Download Python 3.9 or 3.10 specifically
3. **Disable antivirus temporarily** - Some antivirus software blocks Python scripts
4. **Run as Administrator** - Right-click Command Prompt → "Run as administrator"

The system is designed to work on any Windows machine with Python installed!
