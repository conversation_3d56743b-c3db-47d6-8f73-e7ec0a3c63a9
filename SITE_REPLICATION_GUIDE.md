# 🔄 Site Replication Guide: From Site A to Site B

## Complete Guide for Replicating Existing Software to New Sites

### 🎯 **EXACTLY What You Want!**

You have:
- **Site A**: Already installed and running EBO software
- **Site B**: New location where you want redundancy

You want to **replicate/mirror** everything from Site A to Site B for redundancy.

---

## 🔄 **Site Replication Wizard**

### **✅ Access the Wizard:**
```
URL: http://localhost:5002/replicate-site
OR
Dashboard → Quick Actions → 🔄 Replicate Site
```

### **✅ 4-Step Process:**

#### **Step 1: Scan Source Site (Site A)**
- **Purpose**: Discover what's installed on your existing site
- **Action**: Click "🔍 Scan Source Site"
- **Discovers**:
  - ✅ EcoStruxure Building Operation installation
  - ✅ SQL Server databases
  - ✅ Configuration files
  - ✅ Data directories
  - ✅ Services and licenses
  - ✅ Network configuration

#### **Step 2: Configure Target Site (Site B)**
- **Purpose**: Define where to replicate the software
- **Required Information**:
  - Target site name (e.g., "Site B - Backup Location")
  - Target server IP address (e.g., "************")
  - Target server hostname (e.g., "SITE-B-SERVER")
  - Network path to target (e.g., "\\\\************\\c$")

#### **Step 3: Review Replication Plan**
- **Purpose**: See exactly what will be replicated
- **Shows**:
  - Estimated time for replication
  - List of all items to be copied
  - Requirements and prerequisites
  - Step-by-step plan

#### **Step 4: Execute Replication**
- **Purpose**: Actually perform the replication
- **Features**:
  - Real-time progress bar
  - Status updates
  - Error handling
  - Completion confirmation

---

## 🔍 **What Gets Discovered and Replicated**

### **✅ EcoStruxure Building Operation:**
```yaml
Installation Detection:
  ✅ Installation path discovery
  ✅ Version identification
  ✅ Service configuration
  ✅ License information
  ✅ Configuration files

Replication Process:
  ✅ Install EBO on target site
  ✅ Copy configuration files
  ✅ Configure services
  ✅ Transfer licenses
  ✅ Set up client connections
```

### **✅ SQL Server and Databases:**
```yaml
Database Detection:
  ✅ SQL Server instance discovery
  ✅ EBO database identification
  ✅ Database size calculation
  ✅ Backup requirements

Replication Process:
  ✅ Install SQL Server on target
  ✅ Backup source databases
  ✅ Restore to target site
  ✅ Configure replication
  ✅ Set up Always On (if applicable)
```

### **✅ Configuration Files:**
```yaml
Configuration Discovery:
  ✅ EBO configuration files
  ✅ System configuration
  ✅ Network settings
  ✅ Security certificates
  ✅ Custom configurations

Replication Process:
  ✅ Copy all configuration files
  ✅ Update IP addresses for target site
  ✅ Preserve security settings
  ✅ Maintain file permissions
```

### **✅ Data Directories:**
```yaml
Data Discovery:
  ✅ EBO data directories
  ✅ Log files
  ✅ Backup directories
  ✅ Custom data folders
  ✅ Historical data

Replication Process:
  ✅ Copy all data directories
  ✅ Preserve directory structure
  ✅ Maintain data integrity
  ✅ Set up ongoing synchronization
```

### **✅ Services and Applications:**
```yaml
Service Discovery:
  ✅ EBO Enterprise Server
  ✅ EBO Automation Server
  ✅ EBO WebStation
  ✅ SQL Server services
  ✅ Custom services

Replication Process:
  ✅ Install and configure services
  ✅ Set startup types
  ✅ Configure service accounts
  ✅ Test service functionality
```

---

## 🏗️ **Real-World Example**

### **Scenario: Manufacturing Company**
```yaml
Site A (Existing):
  Location: Main Plant - Detroit
  Server: DETROIT-EBO-01 (************)
  Software: EBO + SQL Server + 15 clients
  Status: Fully operational

Site B (New):
  Location: Backup Plant - Chicago  
  Server: CHICAGO-EBO-01 (************)
  Purpose: Redundancy for Site A
  Status: New installation needed
```

### **Replication Process:**
```yaml
Step 1 - Scan Site A:
  ✅ Found EBO installation at C:\Program Files\Schneider Electric\EcoStruxure Building Operation
  ✅ Found SQL Server with EBO_Database (2.5GB)
  ✅ Found 47 configuration files
  ✅ Found 3 data directories
  ✅ Found 8 EBO services

Step 2 - Configure Site B:
  ✅ Target: CHICAGO-EBO-01 (************)
  ✅ Network path: \\************\c$
  ✅ Validation: Connectivity confirmed

Step 3 - Replication Plan:
  ✅ Install EBO on Chicago server (45 minutes)
  ✅ Install SQL Server (30 minutes)
  ✅ Copy database (20 minutes)
  ✅ Copy configuration files (10 minutes)
  ✅ Copy data directories (15 minutes)
  ✅ Configure services (15 minutes)
  ✅ Total estimated time: 2 hours 15 minutes

Step 4 - Execute:
  ✅ Replication completed successfully
  ✅ Site B now mirrors Site A
  ✅ Redundancy established
```

---

## 🔧 **Manual Replication Steps**

### **If you prefer manual replication:**

#### **1. Prepare Target Site (Site B):**
```powershell
# Install Windows Server 2019/2022
# Configure network settings
# Join domain (if applicable)
# Install prerequisites
```

#### **2. Install EBO on Target Site:**
```powershell
# Copy EBO installation media to target
# Run EBO installer
# Use same installation path as source
# Apply same license
```

#### **3. Install SQL Server on Target Site:**
```powershell
# Install SQL Server (same version as source)
# Configure SQL Server instance
# Set up SQL Server services
# Configure security settings
```

#### **4. Replicate Database:**
```sql
-- On Source Site (Site A):
BACKUP DATABASE EBO_Database 
TO DISK = 'C:\Backup\EBO_Database.bak'

-- Copy backup file to target site

-- On Target Site (Site B):
RESTORE DATABASE EBO_Database 
FROM DISK = 'C:\Backup\EBO_Database.bak'
```

#### **5. Copy Configuration Files:**
```powershell
# Copy EBO configuration
robocopy "\\SITE-A-SERVER\c$\ProgramData\Schneider Electric" "C:\ProgramData\Schneider Electric" /E /Z /R:3

# Copy custom configurations
robocopy "\\SITE-A-SERVER\c$\EBO_Config" "C:\EBO_Config" /E /Z /R:3
```

#### **6. Copy Data Directories:**
```powershell
# Copy EBO data
robocopy "\\SITE-A-SERVER\c$\EBO_Data" "C:\EBO_Data" /E /Z /R:3

# Copy logs and backups
robocopy "\\SITE-A-SERVER\c$\EBO_Backup" "C:\EBO_Backup" /E /Z /R:3
```

#### **7. Configure Services:**
```powershell
# Configure EBO services
sc config "EcoStruxure Building Operation Enterprise Server" start= auto
sc config "EcoStruxure Building Operation Automation Server" start= auto

# Start services
net start "EcoStruxure Building Operation Enterprise Server"
net start "EcoStruxure Building Operation Automation Server"
```

#### **8. Update Network Configuration:**
```powershell
# Update IP addresses in configuration files
# Update client connection settings
# Configure firewall rules
# Test connectivity
```

---

## 📊 **Verification Steps**

### **After Replication:**

#### **1. Verify EBO Installation:**
```yaml
✅ EBO services running
✅ Web interface accessible
✅ Database connectivity working
✅ License valid
✅ Client connections successful
```

#### **2. Verify Database Replication:**
```sql
-- Check database status
SELECT name, state_desc FROM sys.databases WHERE name = 'EBO_Database'

-- Verify data integrity
SELECT COUNT(*) FROM [EBO_Database].[dbo].[YourMainTable]
```

#### **3. Verify Configuration:**
```yaml
✅ All configuration files present
✅ Network settings correct
✅ Security certificates valid
✅ Custom configurations preserved
```

#### **4. Test Functionality:**
```yaml
✅ EBO web interface loads
✅ Client PCs can connect
✅ Data collection working
✅ Alarms and events functioning
✅ Reports generating correctly
```

---

## 🚀 **Ongoing Synchronization**

### **After Initial Replication:**

#### **Database Synchronization:**
```yaml
Method: SQL Server Always On Availability Groups
Frequency: Real-time synchronization
RPO: <5 minutes
RTO: <2 minutes
```

#### **File Synchronization:**
```yaml
Method: DFS Replication or Robocopy scripts
Frequency: Every 15 minutes
Files: Configuration and data files
Monitoring: Automated alerts for sync failures
```

#### **Configuration Management:**
```yaml
Method: PowerShell scripts
Frequency: Daily checks
Purpose: Ensure configurations stay synchronized
Alerts: Email notifications for discrepancies
```

---

## 🎯 **Benefits of Site Replication**

### **✅ Business Continuity:**
- **Zero Downtime**: Automatic failover to Site B
- **Data Protection**: Complete data redundancy
- **Service Continuity**: Uninterrupted building operations
- **Disaster Recovery**: Protection against site-level failures

### **✅ Operational Benefits:**
- **Load Distribution**: Distribute client connections
- **Maintenance Windows**: Perform maintenance on one site while other runs
- **Testing Environment**: Use Site B for testing updates
- **Geographic Redundancy**: Protection against regional disasters

### **✅ Technical Benefits:**
- **Identical Configuration**: Exact mirror of Site A
- **Proven Reliability**: Same software versions and settings
- **Easy Management**: Familiar interface and procedures
- **Scalable Solution**: Add more sites as needed

---

## 📞 **Support and Troubleshooting**

### **Common Issues:**

#### **Network Connectivity:**
```yaml
Issue: Cannot connect to target site
Solution: Check VPN, firewall rules, network routing

Issue: Slow file transfer
Solution: Check bandwidth, use compression, schedule during off-hours
```

#### **Software Installation:**
```yaml
Issue: EBO installation fails
Solution: Check prerequisites, run as administrator, verify license

Issue: SQL Server installation fails
Solution: Check system requirements, verify permissions, check disk space
```

#### **Database Replication:**
```yaml
Issue: Database restore fails
Solution: Check backup file integrity, verify disk space, check permissions

Issue: Database synchronization issues
Solution: Check network connectivity, verify SQL Server configuration
```

---

**Your Site Replication Wizard provides a complete solution for replicating your existing EBO installation to create redundancy at a new site!** 🔄🚀

**Access the wizard at: http://localhost:5002/replicate-site** 🌐
