@echo off
title EBO Hardware Redundancy Monitor
color 0A

echo ================================================================
echo    EBO Hardware Redundancy Monitor
echo    Simple Test for Your Two Servers
echo ================================================================
echo.

echo Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    echo.
    echo Please install Python from: https://python.org/downloads/
    echo Make sure to check "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo Installing required packages...
python -m pip install flask psutil --quiet

echo.
echo Starting Hardware Monitor...
echo.
echo INSTRUCTIONS:
echo 1. Run this on BOTH of your EBO servers
echo 2. Open web browser to: http://localhost:5003
echo 3. Configure the IP of your other server
echo 4. Monitor both servers in real-time
echo.
echo Press Ctrl+C to stop
echo.

python SIMPLE_HARDWARE_TEST.py

pause
