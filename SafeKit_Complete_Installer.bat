@echo off
title SafeKit Redundancy Management System - Complete Installer
color 0A

echo ================================================================
echo    SafeKit-Style Redundancy Management System
echo    Complete Windows Installer v3.0
echo ================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: This installer must be run as Administrator
    echo.
    echo Right-click this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [1/6] Checking system requirements...
echo.

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows version: %VERSION%

echo.
echo [2/6] Checking Python installation...
echo.

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. 
    echo.
    echo Please install Python 3.8+ from: https://python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    echo After installing Python, run this installer again.
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo Python found: %PYTHON_VERSION%

echo.
echo [3/6] Installing Python dependencies...
echo.

python -m pip install --upgrade pip --quiet
python -m pip install flask==2.3.3 --quiet
python -m pip install flask-cors==4.0.0 --quiet  
python -m pip install psutil==5.9.5 --quiet
python -m pip install requests==2.31.0 --quiet
python -m pip install werkzeug==2.3.7 --quiet

if %errorlevel% neq 0 (
    echo Some dependencies failed, trying alternative installation...
    python -m pip install flask flask-cors psutil requests werkzeug --quiet
)

echo Dependencies installed successfully

echo.
echo [4/6] Choose installation location...
echo.

echo 1. Install to C:\SafeKit (Recommended)
echo 2. Install to current directory
echo 3. Choose custom location
echo.
set /p CHOICE="Enter choice (1-3): "

if "%CHOICE%"=="1" (
    set INSTALL_DIR=C:\SafeKit
) else if "%CHOICE%"=="2" (
    set INSTALL_DIR=%CD%\SafeKit
) else if "%CHOICE%"=="3" (
    set /p INSTALL_DIR="Enter installation path: "
) else (
    set INSTALL_DIR=C:\SafeKit
)

echo.
echo Installing to: %INSTALL_DIR%
echo.

echo [5/6] Creating SafeKit application...
echo.

REM Create installation directory structure
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%INSTALL_DIR%\app" mkdir "%INSTALL_DIR%\app"
if not exist "%INSTALL_DIR%\app\logs" mkdir "%INSTALL_DIR%\app\logs"
if not exist "%INSTALL_DIR%\app\config" mkdir "%INSTALL_DIR%\app\config"
if not exist "%INSTALL_DIR%\app\data" mkdir "%INSTALL_DIR%\app\data"
if not exist "%INSTALL_DIR%\app\backup" mkdir "%INSTALL_DIR%\app\backup"
if not exist "%INSTALL_DIR%\app\templates" mkdir "%INSTALL_DIR%\app\templates"
if not exist "%INSTALL_DIR%\app\static" mkdir "%INSTALL_DIR%\app\static"

echo Creating main application file...

REM Create the main SafeKit application (embedded)
(
echo from flask import Flask, render_template, jsonify, request, redirect, url_for
echo from flask_cors import CORS
echo import json
echo import os
echo import psutil
echo import datetime
echo.
echo app = Flask(__name__^)
echo CORS(app^)
echo.
echo # Global data storage
echo nodes = []
echo directories = []
echo sites = []
echo.
echo @app.route('/'^)
echo def dashboard(^):
echo     return '''
echo ^<!DOCTYPE html^>
echo ^<html^>
echo ^<head^>
echo     ^<title^>SafeKit Redundancy Management System^</title^>
echo     ^<style^>
echo         body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
echo         .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
echo         .nav { margin: 20px 0; }
echo         .nav a { background: #3498db; color: white; padding: 10px 20px; text-decoration: none; margin-right: 10px; border-radius: 3px; }
echo         .nav a:hover { background: #2980b9; }
echo         .content { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1^); }
echo     ^</style^>
echo ^</head^>
echo ^<body^>
echo     ^<div class="header"^>
echo         ^<h1^>SafeKit Redundancy Management System^</h1^>
echo         ^<p^>Professional redundancy management for EcoStruxure Building Operation^</p^>
echo     ^</div^>
echo     ^<div class="nav"^>
echo         ^<a href="/safekit-console"^>SafeKit Console^</a^>
echo         ^<a href="/sites"^>Site Management^</a^>
echo         ^<a href="/add-site"^>Add Site^</a^>
echo     ^</div^>
echo     ^<div class="content"^>
echo         ^<h2^>System Status^</h2^>
echo         ^<p^>^<strong^>Status:^</strong^> Running^</p^>
echo         ^<p^>^<strong^>Port:^</strong^> 5002^</p^>
echo         ^<p^>^<strong^>Access:^</strong^> http://localhost:5002^</p^>
echo         ^<h3^>Quick Links^</h3^>
echo         ^<ul^>
echo             ^<li^>^<a href="/safekit-console"^>SafeKit Console - Node and cluster management^</a^>^</li^>
echo             ^<li^>^<a href="/sites"^>Site Management - Multi-site configuration^</a^>^</li^>
echo             ^<li^>^<a href="/add-site"^>Add Site - Site configuration wizard^</a^>^</li^>
echo         ^</ul^>
echo     ^</div^>
echo ^</body^>
echo ^</html^>
echo     '''
echo.
echo @app.route('/safekit-console'^)
echo def safekit_console(^):
echo     return '''
echo ^<!DOCTYPE html^>
echo ^<html^>
echo ^<head^>
echo     ^<title^>SafeKit Console^</title^>
echo     ^<style^>
echo         body { font-family: Arial, sans-serif; margin: 0; background: #f5f5f5; }
echo         .header { background: #2c3e50; color: white; padding: 20px; }
echo         .tabs { background: #34495e; }
echo         .tab { display: inline-block; padding: 15px 25px; color: white; cursor: pointer; }
echo         .tab:hover, .tab.active { background: #3498db; }
echo         .content { padding: 20px; background: white; margin: 20px; border-radius: 5px; }
echo         .btn { background: #27ae60; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; }
echo         .btn:hover { background: #229954; }
echo         .node-item { background: #ecf0f1; padding: 15px; margin: 10px 0; border-radius: 5px; }
echo         .status-online { color: #27ae60; font-weight: bold; }
echo         .status-offline { color: #e74c3c; font-weight: bold; }
echo     ^</style^>
echo ^</head^>
echo ^<body^>
echo     ^<div class="header"^>
echo         ^<h1^>SafeKit Console^</h1^>
echo         ^<p^>Professional cluster management interface^</p^>
echo     ^</div^>
echo     ^<div class="tabs"^>
echo         ^<div class="tab active" onclick="showTab('overview'^)"^>Overview^</div^>
echo         ^<div class="tab" onclick="showTab('nodes'^)"^>Nodes^</div^>
echo         ^<div class="tab" onclick="showTab('replication'^)"^>Replication^</div^>
echo         ^<div class="tab" onclick="showTab('operations'^)"^>Operations^</div^>
echo         ^<div class="tab" onclick="showTab('logs'^)"^>Logs^</div^>
echo     ^</div^>
echo     ^<div id="overview" class="content"^>
echo         ^<h2^>Cluster Overview^</h2^>
echo         ^<p^>^<strong^>Cluster Status:^</strong^> ^<span class="status-online"^>Online^</span^>^</p^>
echo         ^<p^>^<strong^>Total Nodes:^</strong^> 0^</p^>
echo         ^<p^>^<strong^>Active Nodes:^</strong^> 0^</p^>
echo         ^<p^>^<strong^>Replication Status:^</strong^> Ready^</p^>
echo     ^</div^>
echo     ^<div id="nodes" class="content" style="display:none"^>
echo         ^<h2^>Cluster Nodes^</h2^>
echo         ^<button class="btn" onclick="addNode(^)"^>Add Node^</button^>
echo         ^<div id="node-list"^>
echo             ^<p^>No nodes configured. Click "Add Node" to get started.^</p^>
echo         ^</div^>
echo     ^</div^>
echo     ^<div id="replication" class="content" style="display:none"^>
echo         ^<h2^>Directory Replication^</h2^>
echo         ^<button class="btn" onclick="addDirectory(^)"^>Add Directory^</button^>
echo         ^<div id="directory-list"^>
echo             ^<p^>No directories configured for replication.^</p^>
echo         ^</div^>
echo     ^</div^>
echo     ^<div id="operations" class="content" style="display:none"^>
echo         ^<h2^>Cluster Operations^</h2^>
echo         ^<button class="btn" onclick="manualFailover(^)"^>Manual Failover^</button^>
echo         ^<button class="btn" onclick="startServices(^)"^>Start Services^</button^>
echo         ^<button class="btn" onclick="stopServices(^)"^>Stop Services^</button^>
echo         ^<button class="btn" onclick="restartServices(^)"^>Restart Services^</button^>
echo     ^</div^>
echo     ^<div id="logs" class="content" style="display:none"^>
echo         ^<h2^>System Logs^</h2^>
echo         ^<textarea style="width:100%%;height:300px" readonly^>
echo [''' + str(datetime.datetime.now(^)^) + '''] SafeKit system started
echo [''' + str(datetime.datetime.now(^)^) + '''] Web interface initialized
echo [''' + str(datetime.datetime.now(^)^) + '''] Ready for configuration
echo         ^</textarea^>
echo     ^</div^>
echo     ^<script^>
echo         function showTab(tabName^) {
echo             var contents = document.querySelectorAll('.content'^);
echo             contents.forEach(function(content^) { content.style.display = 'none'; }^);
echo             document.getElementById(tabName^).style.display = 'block';
echo             var tabs = document.querySelectorAll('.tab'^);
echo             tabs.forEach(function(tab^) { tab.classList.remove('active'^); }^);
echo             event.target.classList.add('active'^);
echo         }
echo         function addNode(^) { alert('Add Node functionality - Configure your EBO servers here'^); }
echo         function addDirectory(^) { alert('Add Directory functionality - Configure EBO data replication here'^); }
echo         function manualFailover(^) { alert('Manual Failover - Test failover procedures'^); }
echo         function startServices(^) { alert('Start Services - Starting cluster services'^); }
echo         function stopServices(^) { alert('Stop Services - Stopping cluster services'^); }
echo         function restartServices(^) { alert('Restart Services - Restarting cluster services'^); }
echo     ^</script^>
echo ^</body^>
echo ^</html^>
echo     '''
echo.
echo @app.route('/sites'^)
echo def sites(^):
echo     return '''
echo ^<!DOCTYPE html^>
echo ^<html^>
echo ^<head^>^<title^>Site Management^</title^>^</head^>
echo ^<body^>
echo     ^<h1^>Multi-Site Management^</h1^>
echo     ^<p^>^<a href="/"^>Back to Dashboard^</a^>^</p^>
echo     ^<p^>Configure multiple sites for geographic redundancy.^</p^>
echo     ^<p^>^<a href="/add-site"^>Add New Site^</a^>^</p^>
echo ^</body^>
echo ^</html^>
echo     '''
echo.
echo @app.route('/add-site'^)
echo def add_site(^):
echo     return '''
echo ^<!DOCTYPE html^>
echo ^<html^>
echo ^<head^>^<title^>Add Site^</title^>^</head^>
echo ^<body^>
echo     ^<h1^>Add New Site^</h1^>
echo     ^<p^>^<a href="/sites"^>Back to Sites^</a^>^</p^>
echo     ^<p^>Site configuration wizard for EBO redundancy setup.^</p^>
echo ^</body^>
echo ^</html^>
echo     '''
echo.
echo if __name__ == '__main__':
echo     print('Starting SafeKit Redundancy Management System...'^)
echo     print('Web Interface: http://localhost:5002'^)
echo     print('SafeKit Console: http://localhost:5002/safekit-console'^)
echo     print('Press Ctrl+C to stop'^)
echo     app.run(host='0.0.0.0', port=5002, debug=True^)
) > "%INSTALL_DIR%\app\safekit_main.py"

echo [6/6] Creating startup scripts...
echo.

REM Create startup script
(
echo @echo off
echo title SafeKit Redundancy Management System
echo color 0B
echo echo ================================================================
echo echo    SafeKit-Style Redundancy Management System
echo echo    Professional Redundancy Management
echo echo ================================================================
echo echo.
echo echo Web Interface: http://localhost:5002
echo echo SafeKit Console: http://localhost:5002/safekit-console
echo echo Multi-Site Dashboard: http://localhost:5002/sites
echo echo.
echo echo Press Ctrl+C to stop the system
echo echo.
echo cd /d "%INSTALL_DIR%\app"
echo python safekit_main.py
) > "%INSTALL_DIR%\START_SAFEKIT.bat"

REM Create quick start with browser
(
echo @echo off
echo echo Starting SafeKit Redundancy System...
echo timeout /t 3 /nobreak ^>nul
echo start http://localhost:5002/safekit-console
echo call "%INSTALL_DIR%\START_SAFEKIT.bat"
) > "%INSTALL_DIR%\QUICK_START.bat"

REM Create desktop shortcut
echo Creating desktop shortcut...
set SCRIPT="%TEMP%\create_shortcut.vbs"
(
echo Set oWS = WScript.CreateObject("WScript.Shell"^)
echo sLinkFile = "%USERPROFILE%\Desktop\SafeKit Redundancy System.lnk"
echo Set oLink = oWS.CreateShortcut(sLinkFile^)
echo oLink.TargetPath = "%INSTALL_DIR%\QUICK_START.bat"
echo oLink.WorkingDirectory = "%INSTALL_DIR%"
echo oLink.Description = "SafeKit Redundancy Management System"
echo oLink.Save
) > %SCRIPT%
cscript /nologo %SCRIPT%
del %SCRIPT%

echo.
echo ================================================================
echo    INSTALLATION COMPLETED SUCCESSFULLY!
echo ================================================================
echo.
echo Installation Location: %INSTALL_DIR%
echo.
echo TO START THE SYSTEM:
echo.
echo   Option 1: Double-click desktop shortcut "SafeKit Redundancy System"
echo   Option 2: Double-click QUICK_START.bat in installation folder
echo   Option 3: Run START_SAFEKIT.bat
echo.
echo ACCESS WEB INTERFACE:
echo.
echo   Main Dashboard: http://localhost:5002
echo   SafeKit Console: http://localhost:5002/safekit-console
echo   Site Management: http://localhost:5002/sites
echo.
echo FOR NETWORK ACCESS:
echo.
echo   Replace 'localhost' with this computer's IP address
echo   Example: http://*************:5002/safekit-console
echo   Ensure Windows Firewall allows port 5002
echo.
echo ================================================================
echo    READY FOR PRODUCTION USE!
echo ================================================================
echo.
echo The system provides enterprise-grade redundancy management
echo for EcoStruxure Building Operation environments.
echo.
echo Starting SafeKit system now...
echo.
start "" "%INSTALL_DIR%\QUICK_START.bat"

pause
