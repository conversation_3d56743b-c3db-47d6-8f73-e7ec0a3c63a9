@echo off
title SafeKit Installation Test
color 0A

echo ================================================================
echo    SafeKit Redundancy System - Installation Test
echo ================================================================
echo.

echo Testing Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found
    echo.
    echo SOLUTION: Install Python from https://python.org
    echo Make sure to check "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ Python found: %PYTHON_VERSION%

echo.
echo Testing required packages...
python -c "import flask; print('✅ Flask available')" 2>nul || echo ❌ Flask missing - run: python -m pip install flask
python -c "import flask_cors; print('✅ Flask-CORS available')" 2>nul || echo ❌ Flask-CORS missing - run: python -m pip install flask-cors
python -c "import psutil; print('✅ PSUtil available')" 2>nul || echo ❌ PSUtil missing - run: python -m pip install psutil
python -c "import requests; print('✅ Requests available')" 2>nul || echo ❌ Requests missing - run: python -m pip install requests

echo.
echo Testing application startup...
echo Starting SafeKit system for 10 seconds...
echo.

timeout /t 2 /nobreak >nul
start /min python multisite_web_interface.py
timeout /t 8 /nobreak >nul

echo.
echo Testing web interface...
curl -s http://localhost:5002 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Web interface is responding
    echo.
    echo SUCCESS! SafeKit system is working correctly.
    echo.
    echo Access your system at:
    echo   Main Dashboard: http://localhost:5002
    echo   SafeKit Console: http://localhost:5002/safekit-console
    echo.
) else (
    echo ❌ Web interface not responding
    echo.
    echo Try manual startup: python multisite_web_interface.py
    echo.
)

echo Stopping test server...
taskkill /f /im python.exe >nul 2>&1

echo.
echo Test completed.
pause
