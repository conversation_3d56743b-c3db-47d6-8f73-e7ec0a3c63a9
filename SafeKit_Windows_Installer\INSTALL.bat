@echo off
title SafeKit Redundancy Management System - Professional Installer
color 0A

echo ================================================================
echo    SafeKit-Style Redundancy Management System
echo    Professional Windows Installer v2.0
echo ================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: This installer must be run as Administrator
    echo.
    echo Right-click on INSTALL.bat and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [1/5] Checking system requirements...
echo.

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows version: %VERSION%

echo.
echo [2/5] Checking Python installation...
echo.

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. Installing Python...
    echo.
    echo Please install Python 3.8+ from: https://python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo Then run this installer again.
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo Python found: %PYTHON_VERSION%

echo.
echo [3/5] Installing Python dependencies...
echo.

python -m pip install --upgrade pip --quiet
python -m pip install flask==2.3.3 --quiet
python -m pip install flask-cors==4.0.0 --quiet  
python -m pip install psutil==5.9.5 --quiet
python -m pip install requests==2.31.0 --quiet
python -m pip install werkzeug==2.3.7 --quiet

if %errorlevel% neq 0 (
    echo Some dependencies failed to install, trying alternative method...
    python -m pip install flask flask-cors psutil requests werkzeug --quiet
)

echo Dependencies installed successfully

echo.
echo [4/5] Creating application directories...
echo.

if not exist "app\logs" mkdir app\logs
if not exist "app\config" mkdir app\config  
if not exist "app\data" mkdir app\data
if not exist "app\backup" mkdir app\backup

echo Application directories created

echo.
echo [5/5] Creating startup scripts...
echo.

REM Create startup script
echo @echo off> START_SAFEKIT.bat
echo title SafeKit Redundancy Management System>> START_SAFEKIT.bat
echo color 0B>> START_SAFEKIT.bat
echo echo ================================================================>> START_SAFEKIT.bat
echo echo    SafeKit-Style Redundancy Management System>> START_SAFEKIT.bat
echo echo    Professional Redundancy Management>> START_SAFEKIT.bat
echo echo ================================================================>> START_SAFEKIT.bat
echo echo.>> START_SAFEKIT.bat
echo echo Web Interface: http://localhost:5002>> START_SAFEKIT.bat
echo echo SafeKit Console: http://localhost:5002/safekit-console>> START_SAFEKIT.bat
echo echo Multi-Site Dashboard: http://localhost:5002/sites>> START_SAFEKIT.bat
echo echo.>> START_SAFEKIT.bat
echo echo Press Ctrl+C to stop the system>> START_SAFEKIT.bat
echo echo.>> START_SAFEKIT.bat
echo cd app>> START_SAFEKIT.bat
echo python multisite_web_interface.py>> START_SAFEKIT.bat

REM Create quick start with browser
echo @echo off> QUICK_START.bat
echo echo Starting SafeKit Redundancy System...>> QUICK_START.bat
echo timeout /t 3 /nobreak ^>nul>> QUICK_START.bat
echo start http://localhost:5002/safekit-console>> QUICK_START.bat
echo call START_SAFEKIT.bat>> QUICK_START.bat

echo Startup scripts created

echo.
echo Testing installation...
echo.

python -c "import flask, flask_cors, psutil, requests; print('All dependencies working')" 2>nul
if %errorlevel% neq 0 (
    echo Some dependencies may be missing, but installation completed
) else (
    echo All dependencies verified successfully
)

echo.
echo ================================================================
echo    INSTALLATION COMPLETED SUCCESSFULLY!
echo ================================================================
echo.
echo TO START THE SYSTEM:
echo.
echo   Option 1: Double-click QUICK_START.bat (Recommended)
echo   Option 2: Double-click START_SAFEKIT.bat  
echo.
echo ACCESS WEB INTERFACE:
echo.
echo   Main Dashboard: http://localhost:5002
echo   SafeKit Console: http://localhost:5002/safekit-console
echo   Site Management: http://localhost:5002/sites
echo.
echo FOR NETWORK ACCESS:
echo.
echo   Replace 'localhost' with this computer's IP address
echo   Example: http://*************:5002/safekit-console
echo   Ensure Windows Firewall allows port 5002
echo.
echo ================================================================
echo    READY FOR PRODUCTION USE!
echo ================================================================
echo.
echo The system provides enterprise-grade redundancy management
echo similar to Microsoft Failover Cluster Manager, specifically
echo designed for EcoStruxure Building Operation environments.
echo.
pause
