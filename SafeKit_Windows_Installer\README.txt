# SafeKit Redundancy Management System

## Professional Windows Installation Package

This package provides a complete installation of the SafeKit-style 
redundancy management system for EcoStruxure Building Operation environments.

## Installation

1. Right-click INSTALL.bat
2. Select "Run as administrator"
3. Follow the installation prompts
4. Start with QUICK_START.bat

## Features

- Microsoft Failover Cluster-style web interface
- Real-time redundancy monitoring
- Multi-site management
- EBO-specific configuration
- Professional enterprise features

## System Requirements

- Windows 10/11 or Windows Server 2016+
- Python 3.8+ (will be installed if missing)
- 4 GB RAM minimum
- 2 GB free disk space
- Administrator privileges for installation

## Ready for Production Use

This system provides enterprise-grade high availability management
specifically designed for building automation environments.
