#!/usr/bin/env python3
"""
Professional Web Interface for Hardware Redundancy Management System
Production-ready web interface for managing application redundancy
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import json
import os
import sys
from datetime import datetime
import threading

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from redundancy_manager import RedundancyManager, ApplicationConfig, HealthStatus, RedundancyState
from database_cluster_manager import DatabaseClusterManager
from storage_redundancy_manager import StorageRedundancyManager
from ebo_redundancy_manager import EBORedundancyManager

app = Flask(__name__)
app.secret_key = 'redundancy-manager-secret-key-change-in-production'

# Global manager instances
redundancy_manager = None
database_cluster_manager = None
storage_redundancy_manager = None
ebo_redundancy_manager = None

def initialize_manager():
    """Initialize the redundancy manager"""
    global redundancy_manager, database_cluster_manager, storage_redundancy_manager, ebo_redundancy_manager
    redundancy_manager = RedundancyManager('redundancy_config.yaml')
    database_cluster_manager = DatabaseClusterManager('database_cluster_config.yaml')
    storage_redundancy_manager = StorageRedundancyManager('storage_redundancy_config.yaml')
    ebo_redundancy_manager = EBORedundancyManager('ebo_redundancy_config.yaml')
    return redundancy_manager

@app.route('/')
def dashboard():
    """Main dashboard showing system overview"""
    if not redundancy_manager:
        initialize_manager()

    status = redundancy_manager.get_system_status()
    return render_template('redundancy_dashboard.html', status=status)

@app.route('/applications')
def applications():
    """Application management page"""
    if not redundancy_manager:
        initialize_manager()

    status = redundancy_manager.get_system_status()
    return render_template('redundancy_applications.html', status=status)

@app.route('/monitors')
def monitors():
    """Monitor management page"""
    if not redundancy_manager:
        initialize_manager()

    status = redundancy_manager.get_system_status()
    return render_template('redundancy_monitors.html', status=status)

@app.route('/events')
def events():
    """Events and logs page"""
    if not redundancy_manager:
        initialize_manager()

    status = redundancy_manager.get_system_status()
    return render_template('redundancy_events.html', status=status)

@app.route('/database-clusters')
def database_clusters():
    """Database clusters management page"""
    if not database_cluster_manager:
        initialize_manager()

    cluster_status = database_cluster_manager.get_cluster_status()
    return render_template('database_clusters.html', status=cluster_status)

@app.route('/storage-redundancy')
def storage_redundancy():
    """Storage redundancy management page"""
    if not storage_redundancy_manager:
        initialize_manager()

    storage_status = storage_redundancy_manager.get_storage_status()
    return render_template('storage_redundancy.html', status=storage_status)

@app.route('/ebo-redundancy')
def ebo_redundancy():
    """EBO redundancy management page"""
    if not ebo_redundancy_manager:
        initialize_manager()

    ebo_status = ebo_redundancy_manager.get_ebo_status_summary()
    return render_template('ebo_redundancy.html', status=ebo_status)

# API Endpoints

@app.route('/api/status')
def api_status():
    """Get system status"""
    if not redundancy_manager:
        initialize_manager()

    try:
        status = redundancy_manager.get_system_status()
        return jsonify({'success': True, 'data': status})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/monitoring/start', methods=['POST'])
def api_start_monitoring():
    """Start monitoring"""
    if not redundancy_manager:
        initialize_manager()

    try:
        success = redundancy_manager.start_monitoring()
        return jsonify({'success': success, 'message': 'Monitoring started' if success else 'Already running'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/monitoring/stop', methods=['POST'])
def api_stop_monitoring():
    """Stop monitoring"""
    if not redundancy_manager:
        initialize_manager()

    try:
        success = redundancy_manager.stop_monitoring()
        return jsonify({'success': success, 'message': 'Monitoring stopped' if success else 'Not running'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/applications', methods=['GET'])
def api_get_applications():
    """Get all applications"""
    if not redundancy_manager:
        initialize_manager()

    try:
        status = redundancy_manager.get_system_status()
        return jsonify({'success': True, 'data': status['applications']})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/applications', methods=['POST'])
def api_add_application():
    """Add new application"""
    if not redundancy_manager:
        initialize_manager()

    try:
        data = request.json
        app_config = ApplicationConfig(
            name=data['name'],
            description=data.get('description', ''),
            priority=data.get('priority', 5),
            is_primary=data.get('is_primary', False),
            monitors=data.get('monitors', []),
            failover_target=data.get('failover_target'),
            auto_failover=data.get('auto_failover', True),
            failover_threshold=data.get('failover_threshold', 3),
            recovery_threshold=data.get('recovery_threshold', 2)
        )

        success = redundancy_manager.add_application(app_config)
        return jsonify({'success': success, 'message': 'Application added successfully' if success else 'Failed to add application'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/applications/<app_name>', methods=['DELETE'])
def api_remove_application(app_name):
    """Remove application"""
    if not redundancy_manager:
        initialize_manager()

    try:
        success = redundancy_manager.remove_application(app_name)
        return jsonify({'success': success, 'message': 'Application removed successfully' if success else 'Failed to remove application'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/applications/<app_name>/monitors', methods=['POST'])
def api_add_monitor(app_name):
    """Add monitor to application"""
    if not redundancy_manager:
        initialize_manager()

    try:
        monitor_config = request.json
        success = redundancy_manager.add_monitor_to_application(app_name, monitor_config)
        return jsonify({'success': success, 'message': 'Monitor added successfully' if success else 'Failed to add monitor'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/applications/<app_name>/monitors/<monitor_name>/test', methods=['POST'])
def api_test_monitor(app_name, monitor_name):
    """Test a specific monitor"""
    if not redundancy_manager:
        initialize_manager()

    try:
        result = redundancy_manager.test_monitor(app_name, monitor_name)
        if result:
            return jsonify({'success': True, 'data': {
                'status': result.status.value,
                'message': result.message,
                'response_time': result.response_time,
                'timestamp': result.timestamp.isoformat()
            }})
        else:
            return jsonify({'success': False, 'error': 'Monitor not found or test failed'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/failover', methods=['POST'])
def api_manual_failover():
    """Manually trigger failover"""
    if not redundancy_manager:
        initialize_manager()

    try:
        data = request.json
        app_name = data['app_name']
        target_app = data['target_app']

        success = redundancy_manager.manual_failover(app_name, target_app)
        return jsonify({'success': success, 'message': 'Failover initiated' if success else 'Failover failed'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/system/info')
def api_system_info():
    """Get system information"""
    try:
        import psutil

        # Get available network interfaces
        interfaces = list(psutil.net_if_addrs().keys())

        # Get disk information
        disk_partitions = []
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_partitions.append({
                    'device': partition.device,
                    'mountpoint': partition.mountpoint,
                    'fstype': partition.fstype,
                    'total': usage.total,
                    'used': usage.used,
                    'free': usage.free,
                    'percent': usage.percent
                })
            except:
                pass

        return jsonify({
            'success': True,
            'data': {
                'network_interfaces': interfaces,
                'disk_partitions': disk_partitions,
                'cpu_count': psutil.cpu_count(),
                'memory': dict(psutil.virtual_memory()._asdict())
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Database Cluster API Endpoints

@app.route('/api/database-clusters')
def get_database_clusters():
    """Get all database clusters status"""
    if not database_cluster_manager:
        initialize_manager()

    try:
        status = database_cluster_manager.get_cluster_status()
        return jsonify({'success': True, 'data': status})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/database-clusters', methods=['POST'])
def create_database_cluster():
    """Create a new database cluster"""
    if not database_cluster_manager:
        initialize_manager()

    try:
        # For now, return success - actual implementation would create the cluster
        return jsonify({'success': True, 'message': 'Database cluster creation not yet implemented'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/database-clusters/<cluster_name>/start', methods=['POST'])
def start_database_cluster_monitoring(cluster_name):
    """Start monitoring for a specific database cluster"""
    if not database_cluster_manager:
        initialize_manager()

    try:
        success = database_cluster_manager.start_cluster_monitoring(cluster_name)
        return jsonify({'success': success, 'message': 'Cluster monitoring started' if success else 'Failed to start monitoring'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/database-clusters/<cluster_name>/stop', methods=['POST'])
def stop_database_cluster_monitoring(cluster_name):
    """Stop monitoring for a specific database cluster"""
    if not database_cluster_manager:
        initialize_manager()

    try:
        success = database_cluster_manager.stop_cluster_monitoring(cluster_name)
        return jsonify({'success': success, 'message': 'Cluster monitoring stopped' if success else 'Failed to stop monitoring'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/database-clusters/start-all', methods=['POST'])
def start_all_database_clusters():
    """Start monitoring for all database clusters"""
    if not database_cluster_manager:
        initialize_manager()

    try:
        success_count = 0
        for cluster_name in database_cluster_manager.clusters.keys():
            if database_cluster_manager.start_cluster_monitoring(cluster_name):
                success_count += 1

        return jsonify({'success': True, 'message': f'Started monitoring for {success_count} clusters'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/database-clusters/<cluster_name>/failover', methods=['POST'])
def manual_database_failover(cluster_name):
    """Manually trigger database cluster failover"""
    if not database_cluster_manager:
        initialize_manager()

    try:
        data = request.json
        target_node = data['target_node']

        success = database_cluster_manager.manual_failover(cluster_name, target_node)
        return jsonify({'success': success, 'message': 'Database failover initiated' if success else 'Database failover failed'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Storage Redundancy API Endpoints

@app.route('/api/storage-redundancy')
def get_storage_redundancy():
    """Get all storage groups status"""
    if not storage_redundancy_manager:
        initialize_manager()

    try:
        status = storage_redundancy_manager.get_storage_status()
        return jsonify({'success': True, 'data': status})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/storage-redundancy', methods=['POST'])
def create_storage_group():
    """Create a new storage group"""
    if not storage_redundancy_manager:
        initialize_manager()

    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'})

        # Create storage group configuration
        success = storage_redundancy_manager.add_storage_group(data)

        if success:
            return jsonify({'success': True, 'message': 'Storage group created successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to create storage group'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/storage-redundancy/<group_name>/start', methods=['POST'])
def start_storage_monitoring(group_name):
    """Start monitoring for a specific storage group"""
    if not storage_redundancy_manager:
        initialize_manager()

    try:
        success = storage_redundancy_manager.start_storage_monitoring(group_name)
        return jsonify({'success': success, 'message': 'Storage monitoring started' if success else 'Failed to start monitoring'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/storage-redundancy/<group_name>/stop', methods=['POST'])
def stop_storage_monitoring(group_name):
    """Stop monitoring for a specific storage group"""
    if not storage_redundancy_manager:
        initialize_manager()

    try:
        success = storage_redundancy_manager.stop_storage_monitoring(group_name)
        return jsonify({'success': success, 'message': 'Storage monitoring stopped' if success else 'Failed to stop monitoring'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/storage-redundancy/start-all', methods=['POST'])
def start_all_storage_groups():
    """Start monitoring for all storage groups"""
    if not storage_redundancy_manager:
        initialize_manager()

    try:
        success_count = 0
        for group_name in storage_redundancy_manager.storage_groups.keys():
            if storage_redundancy_manager.start_storage_monitoring(group_name):
                success_count += 1

        return jsonify({'success': True, 'message': f'Started monitoring for {success_count} storage groups'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/storage-redundancy/<group_name>/sync', methods=['POST'])
def manual_storage_sync(group_name):
    """Manually trigger storage synchronization"""
    if not storage_redundancy_manager:
        initialize_manager()

    try:
        data = request.json
        source_location = data['source_location']
        target_location = data['target_location']

        success = storage_redundancy_manager.manual_sync(group_name, source_location, target_location)
        return jsonify({'success': success, 'message': 'Storage sync initiated' if success else 'Storage sync failed'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/storage-redundancy/<group_name>/failover', methods=['POST'])
def storage_failover(group_name):
    """Perform storage failover"""
    if not storage_redundancy_manager:
        initialize_manager()

    try:
        data = request.json
        failed_location = data['failed_location']
        target_location = data['target_location']

        success = storage_redundancy_manager.storage_failover(group_name, failed_location, target_location)
        return jsonify({'success': success, 'message': 'Storage failover initiated' if success else 'Storage failover failed'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# EBO API Endpoints

@app.route('/api/ebo-status')
def get_ebo_status():
    """Get EBO system status"""
    if not ebo_redundancy_manager:
        initialize_manager()

    try:
        status = ebo_redundancy_manager.get_ebo_status_summary()
        return jsonify({'success': True, 'data': status})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ebo-monitoring/start', methods=['POST'])
def start_ebo_monitoring():
    """Start EBO monitoring"""
    if not ebo_redundancy_manager:
        initialize_manager()

    try:
        success = ebo_redundancy_manager.start_ebo_monitoring()
        return jsonify({'success': success, 'message': 'EBO monitoring started' if success else 'Already running'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ebo-monitoring/stop', methods=['POST'])
def stop_ebo_monitoring():
    """Stop EBO monitoring"""
    if not ebo_redundancy_manager:
        initialize_manager()

    try:
        success = ebo_redundancy_manager.stop_ebo_monitoring()
        return jsonify({'success': success, 'message': 'EBO monitoring stopped' if success else 'Not running'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/ebo-failover/manual', methods=['POST'])
def manual_ebo_failover():
    """Manually trigger EBO failover"""
    if not ebo_redundancy_manager:
        initialize_manager()

    try:
        success = ebo_redundancy_manager.manual_ebo_failover()
        return jsonify({'success': success, 'message': 'EBO failover initiated' if success else 'EBO failover failed'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.errorhandler(404)
def not_found(error):
    return render_template('error.html', error='Page not found'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', error='Internal server error'), 500

if __name__ == '__main__':
    # Ensure required directories exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    os.makedirs('logs', exist_ok=True)

    # Initialize the redundancy manager
    initialize_manager()

    print("🚀 Starting Professional Redundancy Management System")
    print("=" * 60)
    print("📊 Dashboard: http://localhost:5001")
    print("🔧 Applications: http://localhost:5001/applications")
    print("📡 Monitors: http://localhost:5001/monitors")
    print("📋 Events: http://localhost:5001/events")
    print("=" * 60)
    print("Press Ctrl+C to stop the server")

    app.run(debug=True, host='0.0.0.0', port=5001)
