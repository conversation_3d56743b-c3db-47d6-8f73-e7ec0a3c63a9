<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Failover Cluster Configuration - EBO Redundancy Manager</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1e40af;
            --secondary-color: #10b981;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            box-shadow: var(--shadow-lg);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .header h1 i {
            color: var(--secondary-color);
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.875rem 1.75rem;
            background: var(--secondary-color);
            color: white;
            text-decoration: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-md);
        }

        .nav-btn:hover {
            background: #059669;
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .cluster-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card h3 i {
            color: var(--primary-color);
        }

        .cluster-overview {
            grid-column: 1 / -1;
        }

        .cluster-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .status-item {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;
        }

        .status-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .status-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
            margin-bottom: 0.5rem;
        }

        .status-label {
            color: var(--text-secondary);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-size: 0.875rem;
        }

        .nodes-list, .resources-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .node-item, .resource-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.75rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: var(--radius-lg);
            border-left: 4px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .node-item:hover, .resource-item:hover {
            transform: translateX(8px);
            box-shadow: var(--shadow-md);
            background: rgba(255, 255, 255, 0.95);
        }

        .node-item.online {
            border-left-color: var(--success-color);
        }

        .node-item.offline {
            border-left-color: var(--danger-color);
        }

        .node-item.failed {
            border-left-color: var(--danger-color);
            background: rgba(239, 68, 68, 0.1);
        }

        .resource-item.online {
            border-left-color: var(--success-color);
        }

        .resource-item.offline {
            border-left-color: var(--warning-color);
        }

        .resource-item.failed {
            border-left-color: var(--danger-color);
        }

        .node-info, .resource-info {
            flex: 1;
        }

        .node-name, .resource-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
        }

        .node-details, .resource-details {
            color: var(--text-secondary);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-badge.online {
            background: var(--success-color);
            color: white;
        }

        .status-badge.offline {
            background: var(--warning-color);
            color: white;
        }

        .status-badge.failed {
            background: var(--danger-color);
            color: white;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.875rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }

        .actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }

        .config-section {
            margin-bottom: 2rem;
        }

        .config-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid var(--border-color);
        }

        .tab-btn {
            padding: 1rem 2rem;
            background: transparent;
            border: none;
            border-bottom: 3px solid transparent;
            color: var(--text-secondary);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @media (max-width: 768px) {
            .cluster-grid {
                grid-template-columns: 1fr;
            }
            
            .cluster-status {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div>
                <h1><i class="fas fa-server"></i>Failover Cluster Configuration</h1>
                <div class="subtitle">Enterprise-Grade Microsoft Failover Cluster Style Configuration</div>
            </div>
            <div>
                <a href="/" class="nav-btn"><i class="fas fa-arrow-left"></i>Back to Dashboard</a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Cluster Overview -->
        <div class="card cluster-overview">
            <h3><i class="fas fa-chart-line"></i>Cluster Status Overview</h3>
            <div class="cluster-status">
                <div class="status-item">
                    <span class="status-number" id="cluster-nodes">3</span>
                    <div class="status-label">Cluster Nodes</div>
                </div>
                <div class="status-item">
                    <span class="status-number" id="online-nodes">2</span>
                    <div class="status-label">Online Nodes</div>
                </div>
                <div class="status-item">
                    <span class="status-number" id="cluster-resources">5</span>
                    <div class="status-label">Resources</div>
                </div>
                <div class="status-item">
                    <span class="status-number" id="online-resources">4</span>
                    <div class="status-label">Online Resources</div>
                </div>
            </div>
        </div>

        <!-- Configuration Tabs -->
        <div class="config-section">
            <div class="config-tabs">
                <button class="tab-btn active" onclick="showTab('nodes')">
                    <i class="fas fa-server"></i> Cluster Nodes
                </button>
                <button class="tab-btn" onclick="showTab('resources')">
                    <i class="fas fa-cogs"></i> Resources
                </button>
                <button class="tab-btn" onclick="showTab('groups')">
                    <i class="fas fa-layer-group"></i> Resource Groups
                </button>
                <button class="tab-btn" onclick="showTab('settings')">
                    <i class="fas fa-sliders-h"></i> Cluster Settings
                </button>
            </div>

            <!-- Nodes Tab -->
            <div id="nodes-tab" class="tab-content active">
                <div class="cluster-grid">
                    <div class="card">
                        <h3><i class="fas fa-server"></i>Cluster Nodes</h3>
                        <div class="nodes-list" id="nodes-list">
                            <!-- Nodes will be populated here -->
                        </div>
                        <div class="actions">
                            <button class="btn btn-primary" onclick="addNode()">
                                <i class="fas fa-plus"></i>Add Node
                            </button>
                            <button class="btn btn-outline" onclick="refreshNodes()">
                                <i class="fas fa-sync-alt"></i>Refresh
                            </button>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h3><i class="fas fa-heartbeat"></i>Node Health</h3>
                        <div id="node-health-chart" style="height: 300px; display: flex; align-items: center; justify-content: center; color: var(--text-secondary);">
                            <div>
                                <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                <div>Real-time node health monitoring</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resources Tab -->
            <div id="resources-tab" class="tab-content">
                <div class="cluster-grid">
                    <div class="card">
                        <h3><i class="fas fa-cogs"></i>Cluster Resources</h3>
                        <div class="resources-list" id="resources-list">
                            <!-- Resources will be populated here -->
                        </div>
                        <div class="actions">
                            <button class="btn btn-primary" onclick="addResource()">
                                <i class="fas fa-plus"></i>Add Resource
                            </button>
                            <button class="btn btn-outline" onclick="refreshResources()">
                                <i class="fas fa-sync-alt"></i>Refresh
                            </button>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h3><i class="fas fa-exchange-alt"></i>Failover Actions</h3>
                        <div class="actions" style="margin-top: 0; flex-direction: column;">
                            <button class="btn btn-secondary" onclick="testFailover()">
                                <i class="fas fa-play"></i>Test Failover
                            </button>
                            <button class="btn btn-outline" onclick="manualFailover()">
                                <i class="fas fa-hand-paper"></i>Manual Failover
                            </button>
                            <button class="btn btn-outline" onclick="failbackResources()">
                                <i class="fas fa-undo"></i>Failback Resources
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Groups Tab -->
            <div id="groups-tab" class="tab-content">
                <div class="card">
                    <h3><i class="fas fa-layer-group"></i>Resource Groups</h3>
                    <div id="groups-list">
                        <!-- Groups will be populated here -->
                    </div>
                    <div class="actions">
                        <button class="btn btn-primary" onclick="addGroup()">
                            <i class="fas fa-plus"></i>Add Group
                        </button>
                        <button class="btn btn-outline" onclick="refreshGroups()">
                            <i class="fas fa-sync-alt"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings-tab" class="tab-content">
                <div class="card">
                    <h3><i class="fas fa-sliders-h"></i>Cluster Settings</h3>
                    <div id="cluster-settings">
                        <!-- Settings will be populated here -->
                    </div>
                    <div class="actions">
                        <button class="btn btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save"></i>Save Settings
                        </button>
                        <button class="btn btn-outline" onclick="resetSettings()">
                            <i class="fas fa-undo"></i>Reset to Default
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
            
            // Load tab-specific data
            loadTabData(tabName);
        }

        function loadTabData(tabName) {
            switch(tabName) {
                case 'nodes':
                    loadNodes();
                    break;
                case 'resources':
                    loadResources();
                    break;
                case 'groups':
                    loadGroups();
                    break;
                case 'settings':
                    loadSettings();
                    break;
            }
        }

        // Load cluster data
        function loadNodes() {
            // Simulate loading nodes
            const nodesList = document.getElementById('nodes-list');
            nodesList.innerHTML = `
                <div class="node-item online">
                    <div class="node-info">
                        <div class="node-name">EBO-NODE-01</div>
                        <div class="node-details">
                            <span><i class="fas fa-network-wired"></i> *********</span>
                            <span><i class="fas fa-microchip"></i> CPU: 45%</span>
                            <span><i class="fas fa-memory"></i> RAM: 62%</span>
                        </div>
                    </div>
                    <div class="status-badge online">Online</div>
                </div>
                <div class="node-item online">
                    <div class="node-info">
                        <div class="node-name">EBO-NODE-02</div>
                        <div class="node-details">
                            <span><i class="fas fa-network-wired"></i> *********</span>
                            <span><i class="fas fa-microchip"></i> CPU: 38%</span>
                            <span><i class="fas fa-memory"></i> RAM: 55%</span>
                        </div>
                    </div>
                    <div class="status-badge online">Online</div>
                </div>
                <div class="node-item offline">
                    <div class="node-info">
                        <div class="node-name">EBO-NODE-03</div>
                        <div class="node-details">
                            <span><i class="fas fa-network-wired"></i> *********</span>
                            <span><i class="fas fa-exclamation-triangle"></i> Connection timeout</span>
                        </div>
                    </div>
                    <div class="status-badge offline">Offline</div>
                </div>
            `;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadNodes();
        });

        // Placeholder functions
        function addNode() { alert('Add Node functionality would be implemented here'); }
        function refreshNodes() { loadNodes(); }
        function loadResources() { alert('Load Resources functionality would be implemented here'); }
        function loadGroups() { alert('Load Groups functionality would be implemented here'); }
        function loadSettings() { alert('Load Settings functionality would be implemented here'); }
        function addResource() { alert('Add Resource functionality would be implemented here'); }
        function refreshResources() { alert('Refresh Resources functionality would be implemented here'); }
        function testFailover() { alert('Test Failover functionality would be implemented here'); }
        function manualFailover() { alert('Manual Failover functionality would be implemented here'); }
        function failbackResources() { alert('Failback Resources functionality would be implemented here'); }
        function addGroup() { alert('Add Group functionality would be implemented here'); }
        function refreshGroups() { alert('Refresh Groups functionality would be implemented here'); }
        function saveSettings() { alert('Save Settings functionality would be implemented here'); }
        function resetSettings() { alert('Reset Settings functionality would be implemented here'); }
    </script>
</body>
</html>
