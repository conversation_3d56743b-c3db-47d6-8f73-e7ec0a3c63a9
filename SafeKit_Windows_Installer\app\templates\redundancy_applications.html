{% extends "redundancy_base.html" %}

{% block title %}Applications - Professional Redundancy Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="fas fa-server me-2 text-primary"></i>
                Application Management
            </h1>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addApplicationModal">
                <i class="fas fa-plus me-1"></i>Add Application
            </button>
        </div>
    </div>
</div>

<!-- Applications List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Configured Applications
                </h5>
            </div>
            <div class="card-body">
                <div id="applicationsList">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">Loading applications...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Application Modal -->
<div class="modal fade" id="addApplicationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Application</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addApplicationForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appName" class="form-label">Application Name *</label>
                                <input type="text" class="form-control" id="appName" required>
                                <div class="form-text">Unique identifier for the application</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appPriority" class="form-label">Priority</label>
                                <select class="form-select" id="appPriority">
                                    <option value="1">1 - Critical</option>
                                    <option value="2">2 - High</option>
                                    <option value="3">3 - Medium</option>
                                    <option value="4">4 - Low</option>
                                    <option value="5" selected>5 - Normal</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="appDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="appDescription" rows="2" placeholder="Brief description of the application"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isPrimary">
                                    <label class="form-check-label" for="isPrimary">
                                        Primary Application
                                    </label>
                                    <div class="form-text">Mark as primary for failover priority</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoFailover" checked>
                                    <label class="form-check-label" for="autoFailover">
                                        Enable Auto-Failover
                                    </label>
                                    <div class="form-text">Automatically failover on failure</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="failoverTarget" class="form-label">Failover Target</label>
                                <select class="form-select" id="failoverTarget">
                                    <option value="">No failover target</option>
                                </select>
                                <div class="form-text">Application to failover to</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="failoverThreshold" class="form-label">Failover Threshold</label>
                                <input type="number" class="form-control" id="failoverThreshold" value="3" min="1" max="10">
                                <div class="form-text">Consecutive failures before failover</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="recoveryThreshold" class="form-label">Recovery Threshold</label>
                                <input type="number" class="form-control" id="recoveryThreshold" value="2" min="1" max="10">
                                <div class="form-text">Consecutive successes for recovery</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveApplication">Save Application</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Application Modal -->
<div class="modal fade" id="editApplicationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Application</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editApplicationForm">
                    <input type="hidden" id="editAppOriginalName">
                    <!-- Same form fields as add modal -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editAppName" class="form-label">Application Name *</label>
                                <input type="text" class="form-control" id="editAppName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editAppPriority" class="form-label">Priority</label>
                                <select class="form-select" id="editAppPriority">
                                    <option value="1">1 - Critical</option>
                                    <option value="2">2 - High</option>
                                    <option value="3">3 - Medium</option>
                                    <option value="4">4 - Low</option>
                                    <option value="5">5 - Normal</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editAppDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editAppDescription" rows="2"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editIsPrimary">
                                    <label class="form-check-label" for="editIsPrimary">
                                        Primary Application
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editAutoFailover">
                                    <label class="form-check-label" for="editAutoFailover">
                                        Enable Auto-Failover
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="editFailoverTarget" class="form-label">Failover Target</label>
                                <select class="form-select" id="editFailoverTarget">
                                    <option value="">No failover target</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="editFailoverThreshold" class="form-label">Failover Threshold</label>
                                <input type="number" class="form-control" id="editFailoverThreshold" min="1" max="10">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="editRecoveryThreshold" class="form-label">Recovery Threshold</label>
                                <input type="number" class="form-control" id="editRecoveryThreshold" min="1" max="10">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="updateApplication">Update Application</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentApplications = {};

$(document).ready(function() {
    loadApplications();
    
    $('#saveApplication').click(saveApplication);
    $('#updateApplication').click(updateApplication);
    
    // Populate failover targets when modal opens
    $('#addApplicationModal').on('show.bs.modal', function() {
        populateFailoverTargets('#failoverTarget');
    });
    
    $('#editApplicationModal').on('show.bs.modal', function() {
        populateFailoverTargets('#editFailoverTarget');
    });
});

function loadApplications() {
    $.get('/api/applications')
        .done(function(response) {
            if (response.success) {
                currentApplications = response.data;
                displayApplications(response.data);
            } else {
                showAlert('danger', 'Failed to load applications: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to connect to server');
        });
}

function displayApplications(applications) {
    let html = '';
    
    if (Object.keys(applications).length === 0) {
        html = '<div class="text-center py-5"><p class="text-muted">No applications configured. Click "Add Application" to get started.</p></div>';
    } else {
        Object.entries(applications).forEach(([appName, app]) => {
            const config = app.config;
            const statusClass = getStatusClass(app.overall_health);
            const stateClass = getRedundancyStateClass(app.current_state);
            
            html += `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center mb-2">
                                    <h5 class="mb-0 me-3">${appName}</h5>
                                    ${config.is_primary ? '<span class="badge bg-primary me-2">Primary</span>' : '<span class="badge bg-secondary me-2">Secondary</span>'}
                                    <span class="badge bg-info me-2">Priority ${config.priority}</span>
                                    <span class="status-indicator ${statusClass}"></span>
                                    <span class="text-capitalize">${app.overall_health}</span>
                                </div>
                                <p class="text-muted mb-2">${config.description || 'No description'}</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <strong>Monitors:</strong> ${config.monitors.length}<br>
                                            <strong>Auto-Failover:</strong> ${config.auto_failover ? 'Enabled' : 'Disabled'}
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <strong>Failover Target:</strong> ${config.failover_target || 'None'}<br>
                                            <strong>Thresholds:</strong> ${config.failover_threshold}/${config.recovery_threshold}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="mb-2">
                                    <span class="redundancy-state ${stateClass}">${app.current_state.replace(/_/g, ' ')}</span>
                                </div>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="editApplication('${appName}')">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="viewMonitors('${appName}')">
                                        <i class="fas fa-heartbeat"></i> Monitors
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="removeApplication('${appName}')">
                                        <i class="fas fa-trash"></i> Remove
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    $('#applicationsList').html(html);
}

function populateFailoverTargets(selectId) {
    const select = $(selectId);
    const currentValue = select.val();
    
    select.empty().append('<option value="">No failover target</option>');
    
    Object.keys(currentApplications).forEach(appName => {
        select.append(`<option value="${appName}">${appName}</option>`);
    });
    
    if (currentValue) {
        select.val(currentValue);
    }
}

function saveApplication() {
    const appData = {
        name: $('#appName').val(),
        description: $('#appDescription').val(),
        priority: parseInt($('#appPriority').val()),
        is_primary: $('#isPrimary').is(':checked'),
        auto_failover: $('#autoFailover').is(':checked'),
        failover_target: $('#failoverTarget').val() || null,
        failover_threshold: parseInt($('#failoverThreshold').val()),
        recovery_threshold: parseInt($('#recoveryThreshold').val()),
        monitors: []
    };
    
    if (!appData.name) {
        showAlert('warning', 'Application name is required');
        return;
    }
    
    if (currentApplications[appData.name]) {
        showAlert('warning', 'Application name already exists');
        return;
    }
    
    $.ajax({
        url: '/api/applications',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(appData),
        success: function(response) {
            if (response.success) {
                showAlert('success', 'Application added successfully');
                $('#addApplicationModal').modal('hide');
                $('#addApplicationForm')[0].reset();
                loadApplications();
            } else {
                showAlert('danger', 'Failed to add application: ' + response.message);
            }
        },
        error: function() {
            showAlert('danger', 'Failed to add application');
        }
    });
}

function editApplication(appName) {
    const app = currentApplications[appName];
    if (!app) return;
    
    const config = app.config;
    
    $('#editAppOriginalName').val(appName);
    $('#editAppName').val(config.name);
    $('#editAppDescription').val(config.description);
    $('#editAppPriority').val(config.priority);
    $('#editIsPrimary').prop('checked', config.is_primary);
    $('#editAutoFailover').prop('checked', config.auto_failover);
    $('#editFailoverTarget').val(config.failover_target || '');
    $('#editFailoverThreshold').val(config.failover_threshold);
    $('#editRecoveryThreshold').val(config.recovery_threshold);
    
    $('#editApplicationModal').modal('show');
}

function updateApplication() {
    const originalName = $('#editAppOriginalName').val();
    const appData = {
        name: $('#editAppName').val(),
        description: $('#editAppDescription').val(),
        priority: parseInt($('#editAppPriority').val()),
        is_primary: $('#editIsPrimary').is(':checked'),
        auto_failover: $('#editAutoFailover').is(':checked'),
        failover_target: $('#editFailoverTarget').val() || null,
        failover_threshold: parseInt($('#editFailoverThreshold').val()),
        recovery_threshold: parseInt($('#editRecoveryThreshold').val()),
        monitors: currentApplications[originalName].config.monitors
    };
    
    if (!appData.name) {
        showAlert('warning', 'Application name is required');
        return;
    }
    
    // For now, we'll remove and re-add (in a real implementation, you'd have an update endpoint)
    $.ajax({
        url: `/api/applications/${originalName}`,
        method: 'DELETE',
        success: function() {
            $.ajax({
                url: '/api/applications',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(appData),
                success: function(response) {
                    if (response.success) {
                        showAlert('success', 'Application updated successfully');
                        $('#editApplicationModal').modal('hide');
                        loadApplications();
                    } else {
                        showAlert('danger', 'Failed to update application: ' + response.message);
                    }
                },
                error: function() {
                    showAlert('danger', 'Failed to update application');
                }
            });
        },
        error: function() {
            showAlert('danger', 'Failed to update application');
        }
    });
}

function removeApplication(appName) {
    if (!confirm(`Are you sure you want to remove application "${appName}"? This action cannot be undone.`)) {
        return;
    }
    
    $.ajax({
        url: `/api/applications/${appName}`,
        method: 'DELETE',
        success: function(response) {
            if (response.success) {
                showAlert('success', 'Application removed successfully');
                loadApplications();
            } else {
                showAlert('danger', 'Failed to remove application: ' + response.message);
            }
        },
        error: function() {
            showAlert('danger', 'Failed to remove application');
        }
    });
}

function viewMonitors(appName) {
    window.location.href = `/monitors?app=${appName}`;
}
</script>
{% endblock %}
