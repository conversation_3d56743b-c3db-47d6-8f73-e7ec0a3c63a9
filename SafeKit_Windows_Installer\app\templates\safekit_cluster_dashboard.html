<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKit-Style EBO Cluster - Simple High Availability</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --success-color: #16a34a;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --radius: 0.75rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON>goe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 1.5rem 0;
            box-shadow: var(--shadow);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-top: 0.25rem;
            font-weight: 500;
        }

        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--success-color);
            color: white;
            text-decoration: none;
            border-radius: var(--radius);
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        .nav-btn:hover {
            background: #15803d;
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .cluster-overview {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .cluster-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .cluster-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .status-card {
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(16, 163, 74, 0.1) 100%);
            border-radius: var(--radius);
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(37, 99, 235, 0.2);
            transition: all 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .status-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
            margin-bottom: 0.5rem;
        }

        .status-label {
            color: var(--text-secondary);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-size: 0.875rem;
        }

        .cluster-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.15);
        }

        .card h3 {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .node-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            margin-bottom: 1rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: var(--radius);
            border-left: 4px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .node-item:hover {
            transform: translateX(8px);
            box-shadow: var(--shadow);
        }

        .node-item.primary {
            border-left-color: var(--success-color);
            background: rgba(22, 163, 74, 0.1);
        }

        .node-item.secondary {
            border-left-color: var(--info-color);
            background: rgba(8, 145, 178, 0.1);
        }

        .node-item.offline {
            border-left-color: var(--danger-color);
            background: rgba(220, 38, 38, 0.1);
        }

        .node-info {
            flex: 1;
        }

        .node-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
        }

        .node-details {
            color: var(--text-secondary);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-badge.online {
            background: var(--success-color);
            color: white;
        }

        .status-badge.offline {
            background: var(--danger-color);
            color: white;
        }

        .status-badge.synchronizing {
            background: var(--warning-color);
            color: white;
        }

        .status-badge.synchronized {
            background: var(--success-color);
            color: white;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #15803d;
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }

        .actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }

        .replication-status {
            background: linear-gradient(135deg, rgba(22, 163, 74, 0.1), rgba(8, 145, 178, 0.1));
            border-radius: var(--radius);
            padding: 1rem;
            margin-top: 1rem;
        }

        .replication-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .sync-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }

        .sync-indicator.active {
            color: var(--success-color);
        }

        .sync-indicator.inactive {
            color: var(--danger-color);
        }

        .pulse {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        .simple-controls {
            background: rgba(255, 255, 255, 0.9);
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .control-title {
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        @media (max-width: 768px) {
            .cluster-grid {
                grid-template-columns: 1fr;
            }
            
            .cluster-status {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div>
                <h1><i class="fas fa-shield-alt"></i>SafeKit-Style EBO Cluster</h1>
                <div class="subtitle">Software-Only High Availability • Zero Hardware Costs • Zero Special Skills Required</div>
            </div>
            <div>
                <a href="/" class="nav-btn"><i class="fas fa-arrow-left"></i>Back to Dashboard</a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Cluster Overview -->
        <div class="cluster-overview">
            <div class="cluster-title">
                <i class="fas fa-chart-line"></i>
                EBO High Availability Cluster Status
            </div>
            <div class="cluster-status">
                <div class="status-card">
                    <span class="status-number" id="cluster-mode">MIRROR</span>
                    <div class="status-label">Cluster Mode</div>
                </div>
                <div class="status-card">
                    <span class="status-number" id="online-nodes">2</span>
                    <div class="status-label">Online Nodes</div>
                </div>
                <div class="status-card">
                    <span class="status-number" id="replication-status">SYNC</span>
                    <div class="status-label">Replication</div>
                </div>
                <div class="status-card">
                    <span class="status-number" id="virtual-ip">**********</span>
                    <div class="status-label">Virtual IP</div>
                </div>
            </div>
        </div>

        <!-- Main Cluster Grid -->
        <div class="cluster-grid">
            <!-- Cluster Nodes -->
            <div class="card">
                <h3><i class="fas fa-server"></i>Cluster Nodes</h3>
                <div id="nodes-list">
                    <!-- Nodes will be populated here -->
                </div>
                
                <div class="replication-status">
                    <div class="replication-info">
                        <span style="font-weight: 600;">Real-Time Synchronous Replication</span>
                        <div class="sync-indicator active" id="sync-indicator">
                            <div class="pulse"></div>
                            <span>SYNCHRONIZED</span>
                        </div>
                    </div>
                    <div style="font-size: 0.875rem; color: var(--text-secondary);">
                        <i class="fas fa-info-circle"></i>
                        No data loss on failure • Byte-level file replication • Automatic failover enabled
                    </div>
                </div>
            </div>

            <!-- Simple Controls -->
            <div class="card">
                <h3><i class="fas fa-cogs"></i>Simple Controls</h3>
                
                <div class="simple-controls">
                    <div class="control-title">
                        <i class="fas fa-play-circle"></i>
                        Cluster Operations
                    </div>
                    <div class="actions" style="margin-top: 0; flex-direction: column;">
                        <button class="btn btn-success" onclick="startCluster()">
                            <i class="fas fa-play"></i>Start Cluster
                        </button>
                        <button class="btn btn-primary" onclick="testFailover()">
                            <i class="fas fa-exchange-alt"></i>Test Failover
                        </button>
                        <button class="btn btn-outline" onclick="manualFailover()">
                            <i class="fas fa-hand-paper"></i>Manual Failover
                        </button>
                        <button class="btn btn-outline" onclick="syncData()">
                            <i class="fas fa-sync"></i>Force Sync
                        </button>
                    </div>
                </div>

                <div class="simple-controls">
                    <div class="control-title">
                        <i class="fas fa-chart-bar"></i>
                        Monitoring
                    </div>
                    <div style="font-size: 0.875rem; color: var(--text-secondary); margin-bottom: 1rem;">
                        Real-time monitoring with automatic health checks
                    </div>
                    <div class="actions" style="margin-top: 0; flex-direction: column;">
                        <button class="btn btn-outline" onclick="viewLogs()">
                            <i class="fas fa-file-alt"></i>View Logs
                        </button>
                        <button class="btn btn-outline" onclick="refreshStatus()">
                            <i class="fas fa-sync-alt"></i>Refresh Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Load cluster data
        function loadClusterData() {
            // Simulate SafeKit-style cluster data
            const nodesData = [
                {
                    name: 'EBO-PRIMARY',
                    ip: '*********',
                    role: 'primary',
                    status: 'online',
                    replication: 'synchronized',
                    cpu: 45,
                    memory: 62,
                    latency: 1.2
                },
                {
                    name: 'EBO-SECONDARY',
                    ip: '*********',
                    role: 'secondary',
                    status: 'online',
                    replication: 'synchronized',
                    cpu: 38,
                    memory: 55,
                    latency: 1.8
                }
            ];

            const nodesList = document.getElementById('nodes-list');
            nodesList.innerHTML = '';

            nodesData.forEach(node => {
                const nodeItem = document.createElement('div');
                nodeItem.className = `node-item ${node.role} ${node.status}`;
                
                nodeItem.innerHTML = `
                    <div class="node-info">
                        <div class="node-name">${node.name}</div>
                        <div class="node-details">
                            <span><i class="fas fa-network-wired"></i> ${node.ip}</span>
                            <span><i class="fas fa-microchip"></i> CPU: ${node.cpu}%</span>
                            <span><i class="fas fa-memory"></i> RAM: ${node.memory}%</span>
                            <span><i class="fas fa-clock"></i> ${node.latency}ms</span>
                        </div>
                    </div>
                    <div>
                        <div class="status-badge ${node.status}">${node.status.toUpperCase()}</div>
                        <div class="status-badge ${node.replication}" style="margin-top: 0.25rem;">${node.replication.toUpperCase()}</div>
                    </div>
                `;
                
                nodesList.appendChild(nodeItem);
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadClusterData();
            
            // Auto-refresh every 5 seconds (SafeKit-style fast updates)
            setInterval(loadClusterData, 5000);
        });

        // Simple control functions (SafeKit-style simplicity)
        function startCluster() {
            alert('Starting SafeKit cluster...\n\n✓ Initializing nodes\n✓ Starting replication\n✓ Configuring virtual IP\n✓ Cluster ready!');
        }

        function testFailover() {
            if (confirm('This will test automatic failover from primary to secondary node.\n\nContinue?')) {
                alert('Failover test initiated...\n\n✓ Stopping services on primary\n✓ Starting services on secondary\n✓ Switching virtual IP\n✓ Failover completed successfully!');
            }
        }

        function manualFailover() {
            if (confirm('This will manually failover to the secondary node.\n\nContinue?')) {
                alert('Manual failover initiated...\n\n✓ Graceful shutdown on primary\n✓ Activating secondary node\n✓ Virtual IP switched\n✓ Manual failover completed!');
            }
        }

        function syncData() {
            alert('Force synchronization initiated...\n\n✓ Checking file differences\n✓ Synchronizing data\n✓ Verifying integrity\n✓ Synchronization completed!');
        }

        function viewLogs() {
            alert('Opening SafeKit cluster logs...\n\n• Real-time replication logs\n• Heartbeat monitoring\n• Failover events\n• Performance metrics');
        }

        function refreshStatus() {
            loadClusterData();
            alert('Cluster status refreshed!');
        }
    </script>
</body>
</html>
