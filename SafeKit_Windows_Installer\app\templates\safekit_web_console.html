<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKit Web Console - EBO High Availability</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --success-color: #16a34a;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --radius: 0.75rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
        }

        .console-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 1rem 0;
            box-shadow: var(--shadow);
            border-bottom: 3px solid var(--primary-color);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .console-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .console-subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-top: 0.25rem;
            font-weight: 500;
        }

        .console-nav {
            display: flex;
            gap: 1rem;
        }

        .nav-item {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--success-color);
            color: white;
            text-decoration: none;
            border-radius: var(--radius);
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        .nav-item:hover {
            background: #15803d;
            transform: translateY(-2px);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .console-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: var(--radius);
            padding: 0.5rem;
            box-shadow: var(--shadow);
        }

        .tab-btn {
            flex: 1;
            padding: 1rem 2rem;
            background: transparent;
            border: none;
            border-radius: var(--radius);
            color: var(--text-secondary);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-btn.active {
            background: var(--primary-color);
            color: white;
            box-shadow: var(--shadow);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .console-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .console-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .console-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.15);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .status-overview {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(22, 163, 74, 0.1));
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .status-metric {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: var(--radius);
            border-left: 4px solid var(--primary-color);
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: var(--text-secondary);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-size: 0.875rem;
        }

        .node-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .node-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: var(--radius);
            border-left: 4px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .node-card:hover {
            transform: translateX(8px);
            box-shadow: var(--shadow);
        }

        .node-card.primary {
            border-left-color: var(--success-color);
            background: rgba(22, 163, 74, 0.1);
        }

        .node-card.secondary {
            border-left-color: var(--info-color);
            background: rgba(8, 145, 178, 0.1);
        }

        .node-card.offline {
            border-left-color: var(--danger-color);
            background: rgba(220, 38, 38, 0.1);
        }

        .node-info {
            flex: 1;
        }

        .node-name {
            font-weight: 700;
            color: var(--text-primary);
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .node-details {
            color: var(--text-secondary);
            font-size: 0.875rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 0.5rem;
        }

        .node-actions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            text-align: center;
        }

        .status-badge.online {
            background: var(--success-color);
            color: white;
        }

        .status-badge.offline {
            background: var(--danger-color);
            color: white;
        }

        .status-badge.synchronized {
            background: var(--success-color);
            color: white;
        }

        .status-badge.synchronizing {
            background: var(--warning-color);
            color: white;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            min-width: 120px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #15803d;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background: #b45309;
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
            min-width: 100px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .replication-status {
            background: linear-gradient(135deg, rgba(22, 163, 74, 0.1), rgba(8, 145, 178, 0.1));
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .replication-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .sync-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }

        .sync-indicator.active {
            color: var(--success-color);
        }

        .pulse {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        .config-form {
            display: grid;
            gap: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-input {
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .log-viewer {
            background: #1f2937;
            color: #f9fafb;
            border-radius: var(--radius);
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: var(--radius);
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-color);
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: var(--radius);
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .modal-body {
            margin-bottom: 1.5rem;
        }

        .modal-footer {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .form-help {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        @media (max-width: 768px) {
            .console-grid {
                grid-template-columns: 1fr;
            }

            .status-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .console-tabs {
                flex-direction: column;
            }

            .tab-btn {
                justify-content: flex-start;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .modal-content {
                margin: 1rem;
                width: calc(100% - 2rem);
            }
        }
    </style>
</head>
<body>
    <div class="console-header">
        <div class="header-content">
            <div>
                <div class="console-title">
                    <i class="fas fa-shield-alt"></i>
                    SafeKit Web Console
                </div>
                <div class="console-subtitle">EBO High Availability Management • Software-Only Clustering • Zero Hardware Costs</div>
            </div>
            <div class="console-nav">
                <a href="/" class="nav-item">
                    <i class="fas fa-arrow-left"></i>
                    Dashboard
                </a>
                <a href="#" class="nav-item" onclick="refreshConsole()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Console Tabs -->
        <div class="console-tabs">
            <button class="tab-btn active" onclick="showTab('overview')">
                <i class="fas fa-tachometer-alt"></i>
                Overview
            </button>
            <button class="tab-btn" onclick="showTab('nodes')">
                <i class="fas fa-server"></i>
                Nodes
            </button>
            <button class="tab-btn" onclick="showTab('replication')">
                <i class="fas fa-sync"></i>
                Replication
            </button>
            <button class="tab-btn" onclick="showTab('operations')">
                <i class="fas fa-cogs"></i>
                Operations
            </button>
            <button class="tab-btn" onclick="showTab('config')">
                <i class="fas fa-sliders-h"></i>
                Configuration
            </button>
            <button class="tab-btn" onclick="showTab('logs')">
                <i class="fas fa-file-alt"></i>
                Logs
            </button>
        </div>

        <!-- Overview Tab -->
        <div id="overview-tab" class="tab-content active">
            <div class="console-card status-overview">
                <div class="card-title">
                    <i class="fas fa-chart-line"></i>
                    Cluster Status Overview
                </div>
                <div class="status-grid">
                    <div class="status-metric">
                        <span class="metric-value" id="cluster-state">ACTIVE</span>
                        <div class="metric-label">Cluster State</div>
                    </div>
                    <div class="status-metric">
                        <span class="metric-value" id="online-nodes">2/2</span>
                        <div class="metric-label">Online Nodes</div>
                    </div>
                    <div class="status-metric">
                        <span class="metric-value" id="replication-state">SYNC</span>
                        <div class="metric-label">Replication</div>
                    </div>
                    <div class="status-metric">
                        <span class="metric-value" id="virtual-ip">********00</span>
                        <div class="metric-label">Virtual IP</div>
                    </div>
                    <div class="status-metric">
                        <span class="metric-value" id="uptime">99.9%</span>
                        <div class="metric-label">Uptime</div>
                    </div>
                    <div class="status-metric">
                        <span class="metric-value" id="last-failover">Never</span>
                        <div class="metric-label">Last Failover</div>
                    </div>
                </div>
            </div>

            <div class="console-grid">
                <div class="console-card">
                    <div class="card-title">
                        <i class="fas fa-heartbeat"></i>
                        Quick Health Check
                    </div>
                    <div id="health-summary">
                        <div style="margin-bottom: 1rem;">
                            <strong>✅ All systems operational</strong>
                        </div>
                        <div style="font-size: 0.875rem; color: var(--text-secondary);">
                            • Both nodes online and healthy<br>
                            • Real-time replication active<br>
                            • Virtual IP responding<br>
                            • EBO services running<br>
                            • No alerts or warnings
                        </div>
                    </div>
                </div>

                <div class="console-card">
                    <div class="card-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </div>
                    <div class="actions-grid">
                        <button class="btn btn-success btn-sm" onclick="startCluster()">
                            <i class="fas fa-play"></i>
                            Start Cluster
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="testFailover()">
                            <i class="fas fa-vial"></i>
                            Test Failover
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="manualFailover()">
                            <i class="fas fa-exchange-alt"></i>
                            Manual Failover
                        </button>
                        <button class="btn btn-outline btn-sm" onclick="syncData()">
                            <i class="fas fa-sync"></i>
                            Force Sync
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Nodes Tab -->
        <div id="nodes-tab" class="tab-content">
            <div class="console-grid">
                <div class="console-card">
                    <div class="card-title">
                        <i class="fas fa-server"></i>
                        Cluster Nodes
                        <div style="margin-left: auto; display: flex; gap: 0.5rem;">
                            <button class="btn btn-success btn-sm" onclick="addNode()">
                                <i class="fas fa-plus"></i> Add Node
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="refreshNodes()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button class="btn btn-info btn-sm" onclick="testEditFunction()" title="Test Edit Function">
                                <i class="fas fa-bug"></i> Test
                            </button>
                        </div>
                    </div>
                    <div class="node-list" id="nodes-list">
                        <!-- Nodes will be populated here -->
                    </div>
                </div>

                <div class="console-card">
                    <div class="card-title">
                        <i class="fas fa-network-wired"></i>
                        Network Configuration
                    </div>
                    <div class="config-form">
                        <div class="form-group">
                            <label class="form-label">Virtual IP Address</label>
                            <input type="text" class="form-input" id="virtual-ip-input" value="********00">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Subnet Mask</label>
                            <input type="text" class="form-input" id="subnet-mask-input" value="*************">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Split-Brain Checker</label>
                            <input type="text" class="form-input" id="split-brain-input" value="********">
                        </div>
                        <div class="actions-grid" style="margin-top: 1rem;">
                            <button class="btn btn-primary btn-sm" onclick="saveNetworkConfig()">
                                <i class="fas fa-save"></i> Save Network Config
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="testNetworkConfig()">
                                <i class="fas fa-network-wired"></i> Test Configuration
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Replication Tab -->
        <div id="replication-tab" class="tab-content">
            <div class="console-card">
                <div class="card-title">
                    <i class="fas fa-sync"></i>
                    Real-Time Synchronous Replication
                </div>

                <div class="replication-status">
                    <div class="replication-header">
                        <span style="font-weight: 600;">Replication Status</span>
                        <div class="sync-indicator active">
                            <div class="pulse"></div>
                            <span>SYNCHRONIZED</span>
                        </div>
                    </div>
                    <div style="font-size: 0.875rem; color: var(--text-secondary);">
                        <strong>Mode:</strong> Synchronous (No data loss)<br>
                        <strong>Compression:</strong> Enabled<br>
                        <strong>Encryption:</strong> Enabled<br>
                        <strong>Last Sync:</strong> 2 seconds ago
                    </div>
                </div>

                <div class="console-grid" style="margin-top: 2rem;">
                    <div class="console-card">
                        <div class="card-title">
                            <i class="fas fa-folder"></i>
                            Replicated Directories
                            <div style="margin-left: auto; display: flex; gap: 0.5rem;">
                                <button class="btn btn-success btn-sm" onclick="addReplicatedDirectory()">
                                    <i class="fas fa-plus"></i> Add Directory
                                </button>
                                <button class="btn btn-outline btn-sm" onclick="refreshDirectories()">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                            </div>
                        </div>
                        <div id="replicated-dirs">
                            <!-- Directories will be populated here -->
                        </div>
                    </div>

                    <div class="console-card">
                        <div class="card-title">
                            <i class="fas fa-chart-bar"></i>
                            Replication Statistics
                        </div>
                        <div class="status-grid">
                            <div class="status-metric">
                                <span class="metric-value">2.3GB</span>
                                <div class="metric-label">Data Replicated</div>
                            </div>
                            <div class="status-metric">
                                <span class="metric-value">45,678</span>
                                <div class="metric-label">Files Synced</div>
                            </div>
                            <div class="status-metric">
                                <span class="metric-value">0.8s</span>
                                <div class="metric-label">Avg Sync Time</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Operations Tab -->
        <div id="operations-tab" class="tab-content">
            <div class="console-grid">
                <div class="console-card">
                    <div class="card-title">
                        <i class="fas fa-play-circle"></i>
                        Cluster Operations
                    </div>
                    <div class="actions-grid">
                        <button class="btn btn-success" onclick="startCluster()">
                            <i class="fas fa-play"></i>
                            Start Cluster
                        </button>
                        <button class="btn btn-warning" onclick="stopCluster()">
                            <i class="fas fa-stop"></i>
                            Stop Cluster
                        </button>
                        <button class="btn btn-primary" onclick="restartCluster()">
                            <i class="fas fa-redo"></i>
                            Restart Cluster
                        </button>
                        <button class="btn btn-outline" onclick="pauseReplication()">
                            <i class="fas fa-pause"></i>
                            Pause Replication
                        </button>
                    </div>
                </div>

                <div class="console-card">
                    <div class="card-title">
                        <i class="fas fa-exchange-alt"></i>
                        Failover Operations
                    </div>
                    <div class="actions-grid">
                        <button class="btn btn-warning" onclick="testFailover()">
                            <i class="fas fa-vial"></i>
                            Test Failover
                        </button>
                        <button class="btn btn-primary" onclick="manualFailover()">
                            <i class="fas fa-hand-paper"></i>
                            Manual Failover
                        </button>
                        <button class="btn btn-outline" onclick="failbackToPrimary()">
                            <i class="fas fa-undo"></i>
                            Failback to Primary
                        </button>
                        <button class="btn btn-outline" onclick="switchPrimary()">
                            <i class="fas fa-random"></i>
                            Switch Primary
                        </button>
                    </div>
                </div>
            </div>

            <div class="console-card">
                <div class="card-title">
                    <i class="fas fa-history"></i>
                    Recent Operations
                </div>
                <div id="operations-history">
                    <div style="font-size: 0.875rem; line-height: 1.6;">
                        <div style="margin-bottom: 0.5rem;">
                            <strong>2024-01-15 14:30:25</strong> - Cluster started successfully
                        </div>
                        <div style="margin-bottom: 0.5rem;">
                            <strong>2024-01-15 14:25:10</strong> - Replication synchronized
                        </div>
                        <div style="margin-bottom: 0.5rem;">
                            <strong>2024-01-15 14:20:05</strong> - Node EBO-SECONDARY came online
                        </div>
                        <div style="margin-bottom: 0.5rem;">
                            <strong>2024-01-15 14:15:00</strong> - Virtual IP configured
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Configuration Tab -->
        <div id="config-tab" class="tab-content">
            <div class="console-grid">
                <div class="console-card">
                    <div class="card-title">
                        <i class="fas fa-cog"></i>
                        Cluster Settings
                    </div>
                    <div class="config-form">
                        <div class="form-group">
                            <label class="form-label">Cluster Name</label>
                            <input type="text" class="form-input" value="EBO-SafeKit-Cluster">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Heartbeat Interval (seconds)</label>
                            <input type="number" class="form-input" value="1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Heartbeat Timeout (seconds)</label>
                            <input type="number" class="form-input" value="5">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Automatic Failover</label>
                            <select class="form-input">
                                <option value="true" selected>Enabled</option>
                                <option value="false">Disabled</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Automatic Failback</label>
                            <select class="form-input">
                                <option value="true" selected>Enabled</option>
                                <option value="false">Disabled</option>
                            </select>
                        </div>
                    </div>
                    <div class="actions-grid" style="margin-top: 1rem;">
                        <button class="btn btn-primary" onclick="saveConfig()">
                            <i class="fas fa-save"></i>
                            Save Configuration
                        </button>
                        <button class="btn btn-outline" onclick="resetConfig()">
                            <i class="fas fa-undo"></i>
                            Reset to Default
                        </button>
                    </div>
                </div>

                <div class="console-card">
                    <div class="card-title">
                        <i class="fas fa-folder-open"></i>
                        Replication Settings
                    </div>
                    <div class="config-form">
                        <div class="form-group">
                            <label class="form-label">Synchronization Mode</label>
                            <select class="form-input">
                                <option value="synchronous" selected>Synchronous (No data loss)</option>
                                <option value="asynchronous">Asynchronous</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Compression</label>
                            <select class="form-input">
                                <option value="true" selected>Enabled</option>
                                <option value="false">Disabled</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Encryption</label>
                            <select class="form-input">
                                <option value="true" selected>Enabled</option>
                                <option value="false">Disabled</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Max File Size (MB)</label>
                            <input type="number" class="form-input" value="1024">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs Tab -->
        <div id="logs-tab" class="tab-content">
            <div class="console-card">
                <div class="card-title">
                    <i class="fas fa-file-alt"></i>
                    SafeKit Cluster Logs
                </div>
                <div style="margin-bottom: 1rem;">
                    <button class="btn btn-outline btn-sm" onclick="refreshLogs()">
                        <i class="fas fa-sync-alt"></i>
                        Refresh Logs
                    </button>
                    <button class="btn btn-outline btn-sm" onclick="clearLogs()">
                        <i class="fas fa-trash"></i>
                        Clear Logs
                    </button>
                    <button class="btn btn-outline btn-sm" onclick="downloadLogs()">
                        <i class="fas fa-download"></i>
                        Download Logs
                    </button>
                </div>
                <div class="log-viewer" id="log-viewer">
2024-01-15 14:35:42 [INFO] SafeKit cluster monitoring active
2024-01-15 14:35:41 [INFO] Real-time replication started
2024-01-15 14:35:40 [INFO] Virtual IP ********00 configured on EBO-PRIMARY
2024-01-15 14:35:39 [INFO] EBO services started on EBO-PRIMARY
2024-01-15 14:35:38 [INFO] Node EBO-SECONDARY synchronized
2024-01-15 14:35:37 [INFO] Node EBO-PRIMARY promoted to primary
2024-01-15 14:35:36 [INFO] Heartbeat established with EBO-SECONDARY
2024-01-15 14:35:35 [INFO] Heartbeat established with EBO-PRIMARY
2024-01-15 14:35:34 [INFO] Split-brain checker configured: ********
2024-01-15 14:35:33 [INFO] SafeKit cluster initialization started
2024-01-15 14:35:32 [INFO] Configuration loaded: EBO-SafeKit-Cluster
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');

            // Load tab-specific data
            loadTabData(tabName);
        }

        function loadTabData(tabName) {
            switch(tabName) {
                case 'overview':
                    loadOverviewData();
                    break;
                case 'nodes':
                    loadNodesData();
                    break;
                case 'replication':
                    loadReplicationData();
                    break;
                case 'operations':
                    loadOperationsData();
                    break;
                case 'config':
                    loadConfigData();
                    break;
                case 'logs':
                    loadLogsData();
                    break;
            }
        }

        function loadOverviewData() {
            // Update overview metrics
            document.getElementById('cluster-state').textContent = 'ACTIVE';
            document.getElementById('online-nodes').textContent = '2/2';
            document.getElementById('replication-state').textContent = 'SYNC';
            document.getElementById('virtual-ip').textContent = '********00';
            document.getElementById('uptime').textContent = '99.9%';
            document.getElementById('last-failover').textContent = 'Never';
        }

        // Quick action functions
        function startCluster() {
            alert('Starting SafeKit cluster...\n\n✓ Initializing nodes\n✓ Starting replication\n✓ Configuring virtual IP\n✓ Starting EBO services\n✓ Cluster ready!');
        }

        function testFailover() {
            if (confirm('This will test automatic failover.\n\nThe test will:\n• Stop services on primary\n• Start services on secondary\n• Switch virtual IP\n• Verify functionality\n• Restore original state\n\nContinue?')) {
                alert('Failover test completed successfully!\n\n✓ Primary services stopped\n✓ Secondary activated\n✓ Virtual IP switched\n✓ EBO accessible\n✓ Original state restored');
            }
        }

        function manualFailover() {
            if (confirm('This will manually failover to the secondary node.\n\nContinue?')) {
                alert('Manual failover completed!\n\n✓ Services stopped on primary\n✓ Services started on secondary\n✓ Virtual IP switched\n✓ Replication direction updated');
            }
        }

        function syncData() {
            alert('Force synchronization completed!\n\n✓ File differences calculated\n✓ Data synchronized\n✓ Integrity verified\n✓ Replication up to date');
        }

        function refreshConsole() {
            location.reload();
        }

        // Initialize console
        document.addEventListener('DOMContentLoaded', function() {
            loadOverviewData();

            // Auto-refresh every 10 seconds
            setInterval(loadOverviewData, 10000);

            // Add event listeners for modals
            setupModalEventListeners();
        });

        function setupModalEventListeners() {
            // Node modal event listener
            const nodeModal = document.getElementById('nodeModal');
            if (nodeModal) {
                nodeModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeNodeModal();
                    }
                });
            }

            // Directory modal event listener
            const directoryModal = document.getElementById('directoryModal');
            if (directoryModal) {
                directoryModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeDirectoryModal();
                    }
                });
            }

            // ESC key to close modals
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeNodeModal();
                    closeDirectoryModal();
                }
            });
        }

        // Load nodes data
        function loadNodesData() {
            const nodesList = document.getElementById('nodes-list');
            nodesList.innerHTML = `
                <div class="node-card primary">
                    <div class="node-info">
                        <div class="node-name">EBO-PRIMARY</div>
                        <div class="node-details">
                            <span><i class="fas fa-network-wired"></i> ********0</span>
                            <span><i class="fas fa-microchip"></i> CPU: 45%</span>
                            <span><i class="fas fa-memory"></i> RAM: 62%</span>
                            <span><i class="fas fa-hdd"></i> Disk: 78%</span>
                            <span><i class="fas fa-clock"></i> 1.2ms</span>
                        </div>
                    </div>
                    <div class="node-actions">
                        <div class="status-badge online">ONLINE</div>
                        <div class="status-badge synchronized">SYNC</div>
                        <div style="display: flex; gap: 0.25rem; margin-top: 0.5rem;">
                            <button class="btn btn-primary btn-sm" onclick="editNode('EBO-PRIMARY')" title="Edit Node">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="manageNode('EBO-PRIMARY')" title="Manage Node">
                                <i class="fas fa-cog"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="deleteNode('EBO-PRIMARY')" title="Delete Node">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="node-card secondary">
                    <div class="node-info">
                        <div class="node-name">EBO-SECONDARY</div>
                        <div class="node-details">
                            <span><i class="fas fa-network-wired"></i> ********1</span>
                            <span><i class="fas fa-microchip"></i> CPU: 38%</span>
                            <span><i class="fas fa-memory"></i> RAM: 55%</span>
                            <span><i class="fas fa-hdd"></i> Disk: 72%</span>
                            <span><i class="fas fa-clock"></i> 1.8ms</span>
                        </div>
                    </div>
                    <div class="node-actions">
                        <div class="status-badge online">ONLINE</div>
                        <div class="status-badge synchronized">SYNC</div>
                        <div style="display: flex; gap: 0.25rem; margin-top: 0.5rem;">
                            <button class="btn btn-primary btn-sm" onclick="editNode('EBO-SECONDARY')" title="Edit Node">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="manageNode('EBO-SECONDARY')" title="Manage Node">
                                <i class="fas fa-cog"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="deleteNode('EBO-SECONDARY')" title="Delete Node">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadReplicationData() {
            loadReplicatedDirectories();
            console.log('Replication data loaded');
        }

        function loadReplicatedDirectories() {
            const dirsContainer = document.getElementById('replicated-dirs');

            // Sample replicated directories data
            const directories = [
                {
                    path: 'C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation',
                    description: 'EBO Application Data',
                    mode: 'synchronous',
                    priority: 'high',
                    status: 'synchronized',
                    lastSync: '2 seconds ago',
                    fileCount: 15678,
                    size: '1.2 GB'
                },
                {
                    path: 'C:\\EBO_Data',
                    description: 'EBO Database Files',
                    mode: 'synchronous',
                    priority: 'high',
                    status: 'synchronized',
                    lastSync: '1 second ago',
                    fileCount: 8934,
                    size: '856 MB'
                },
                {
                    path: 'C:\\EBO_Config',
                    description: 'EBO Configuration Files',
                    mode: 'synchronous',
                    priority: 'normal',
                    status: 'synchronized',
                    lastSync: '3 seconds ago',
                    fileCount: 234,
                    size: '12 MB'
                },
                {
                    path: 'C:\\EBO_Logs',
                    description: 'EBO Log Files',
                    mode: 'asynchronous',
                    priority: 'low',
                    status: 'synchronized',
                    lastSync: '15 seconds ago',
                    fileCount: 567,
                    size: '89 MB'
                }
            ];

            let html = '';
            directories.forEach((dir, index) => {
                const statusColor = dir.status === 'synchronized' ? 'var(--success-color)' :
                                  dir.status === 'synchronizing' ? 'var(--warning-color)' : 'var(--danger-color)';

                const priorityColor = dir.priority === 'high' ? 'var(--danger-color)' :
                                    dir.priority === 'normal' ? 'var(--info-color)' : 'var(--text-secondary)';

                html += `
                    <div class="directory-item" style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 1rem;
                        margin-bottom: 1rem;
                        background: rgba(255, 255, 255, 0.8);
                        border-radius: var(--radius);
                        border-left: 4px solid ${statusColor};
                        transition: all 0.3s ease;
                    " onmouseover="this.style.transform='translateX(8px)'; this.style.boxShadow='var(--shadow)'"
                       onmouseout="this.style.transform='translateX(0)'; this.style.boxShadow='none'">
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: var(--text-primary); font-size: 1rem; margin-bottom: 0.5rem;">
                                <i class="fas fa-folder" style="margin-right: 0.5rem; color: var(--primary-color);"></i>
                                ${dir.path}
                            </div>
                            <div style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">
                                ${dir.description}
                            </div>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 0.5rem; font-size: 0.75rem;">
                                <span><i class="fas fa-sync" style="color: ${statusColor};"></i> ${dir.mode}</span>
                                <span><i class="fas fa-flag" style="color: ${priorityColor};"></i> ${dir.priority}</span>
                                <span><i class="fas fa-files"></i> ${dir.fileCount} files</span>
                                <span><i class="fas fa-hdd"></i> ${dir.size}</span>
                                <span><i class="fas fa-clock"></i> ${dir.lastSync}</span>
                            </div>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 0.5rem; align-items: center;">
                            <div class="status-badge" style="
                                padding: 0.25rem 0.75rem;
                                border-radius: 9999px;
                                font-size: 0.75rem;
                                font-weight: 600;
                                text-transform: uppercase;
                                background: ${statusColor};
                                color: white;
                            ">${dir.status}</div>
                            <div style="display: flex; gap: 0.25rem;">
                                <button class="btn btn-primary btn-sm" onclick="editDirectory(${index})" title="Edit Directory" style="padding: 0.25rem 0.5rem;">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline btn-sm" onclick="syncDirectory(${index})" title="Force Sync" style="padding: 0.25rem 0.5rem;">
                                    <i class="fas fa-sync"></i>
                                </button>
                                <button class="btn btn-warning btn-sm" onclick="deleteDirectory(${index})" title="Remove Directory" style="padding: 0.25rem 0.5rem;">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });

            // Add exclude patterns info
            html += `
                <div style="margin-top: 1.5rem; padding: 1rem; background: rgba(255, 255, 255, 0.6); border-radius: var(--radius); border-left: 4px solid var(--info-color);">
                    <div style="font-weight: 600; margin-bottom: 0.5rem; color: var(--text-primary);">
                        <i class="fas fa-filter" style="margin-right: 0.5rem; color: var(--info-color);"></i>
                        Global Exclusion Patterns
                    </div>
                    <div style="font-family: monospace; font-size: 0.875rem; color: var(--text-secondary);">
                        <strong>Excluded:</strong> *.tmp, *.log, *.bak, temp\\*, cache\\*, ~$*, Thumbs.db, .DS_Store
                    </div>
                    <button class="btn btn-outline btn-sm" onclick="editExclusionPatterns()" style="margin-top: 0.5rem;">
                        <i class="fas fa-edit"></i> Edit Patterns
                    </button>
                </div>
            `;

            dirsContainer.innerHTML = html;
        }

        function loadOperationsData() {
            // Operations data is already loaded in the HTML
            console.log('Operations data loaded');
        }

        function loadConfigData() {
            // Configuration data is already loaded in the HTML
            console.log('Configuration data loaded');
        }

        function loadLogsData() {
            // Logs data is already loaded in the HTML
            console.log('Logs data loaded');
        }

        // Additional SafeKit functions
        function stopCluster() {
            if (confirm('This will stop the SafeKit cluster.\n\nAll services will be stopped and replication will pause.\n\nContinue?')) {
                alert('Cluster stopped successfully!\n\n✓ Services stopped\n✓ Replication paused\n✓ Virtual IP released\n✓ Cluster offline');
            }
        }

        function restartCluster() {
            if (confirm('This will restart the SafeKit cluster.\n\nContinue?')) {
                alert('Cluster restarted successfully!\n\n✓ Services restarted\n✓ Replication resumed\n✓ Virtual IP reconfigured\n✓ Cluster online');
            }
        }

        function pauseReplication() {
            if (confirm('This will pause real-time replication.\n\nContinue?')) {
                alert('Replication paused!\n\n✓ File synchronization stopped\n✓ Changes will queue\n✓ Resume when ready');
            }
        }

        function failbackToPrimary() {
            if (confirm('This will failback to the original primary node.\n\nContinue?')) {
                alert('Failback completed!\n\n✓ Services moved to primary\n✓ Virtual IP switched\n✓ Replication direction updated');
            }
        }

        function switchPrimary() {
            if (confirm('This will switch the primary node designation.\n\nContinue?')) {
                alert('Primary node switched!\n\n✓ Roles swapped\n✓ Services migrated\n✓ Configuration updated');
            }
        }

        function manageNode(nodeName) {
            alert(`Managing node: ${nodeName}\n\nAvailable actions:\n• View detailed metrics\n• Restart node services\n• Update node configuration\n• Run diagnostics`);
        }

        function saveConfig() {
            alert('Configuration saved successfully!\n\n✓ Cluster settings updated\n✓ Replication settings applied\n✓ Changes will take effect immediately');
        }

        function resetConfig() {
            if (confirm('This will reset all configuration to default values.\n\nContinue?')) {
                alert('Configuration reset to defaults!\n\n✓ All settings restored\n✓ Cluster will restart with new config');
            }
        }

        function refreshLogs() {
            const logViewer = document.getElementById('log-viewer');
            const currentTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
            logViewer.textContent = `${currentTime} [INFO] Logs refreshed by user\n` + logViewer.textContent;
        }

        function clearLogs() {
            if (confirm('This will clear all log entries.\n\nContinue?')) {
                document.getElementById('log-viewer').textContent = 'Logs cleared by user.';
            }
        }

        function downloadLogs() {
            const logs = document.getElementById('log-viewer').textContent;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'safekit-cluster-logs.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Node Management Functions
        let currentEditingNode = null;

        function addNode() {
            currentEditingNode = null;
            document.getElementById('modalTitle').textContent = 'Add Node';

            // Clear form
            document.getElementById('nodeName').value = '';
            document.getElementById('nodeIP').value = '';
            document.getElementById('nodePort').value = '5000';
            document.getElementById('nodeRole').value = 'secondary';
            document.getElementById('nodePriority').value = '100';
            document.getElementById('nodeDescription').value = '';
            document.getElementById('nodeAutoStart').checked = true;

            // Show modal
            document.getElementById('nodeModal').classList.add('show');
        }

        function editNode(nodeName) {
            console.log('editNode called with:', nodeName);

            try {
                currentEditingNode = nodeName;
                document.getElementById('modalTitle').textContent = 'Edit Node';

                // Get current node data (in real implementation, this would fetch from server)
                let currentIP = nodeName === 'EBO-PRIMARY' ? '********0' : '********1';
                let currentRole = nodeName === 'EBO-PRIMARY' ? 'primary' : 'secondary';
                let currentPriority = nodeName === 'EBO-PRIMARY' ? '1000' : '900';

                // Populate form with current data
                const nodeNameInput = document.getElementById('nodeName');
                const nodeIPInput = document.getElementById('nodeIP');
                const nodePortInput = document.getElementById('nodePort');
                const nodeRoleSelect = document.getElementById('nodeRole');
                const nodePriorityInput = document.getElementById('nodePriority');
                const nodeDescriptionInput = document.getElementById('nodeDescription');
                const nodeAutoStartCheckbox = document.getElementById('nodeAutoStart');

                if (!nodeNameInput || !nodeIPInput || !nodePortInput || !nodeRoleSelect || !nodePriorityInput || !nodeDescriptionInput || !nodeAutoStartCheckbox) {
                    console.error('One or more form elements not found');
                    alert('Error: Form elements not found. Please refresh the page and try again.');
                    return;
                }

                nodeNameInput.value = nodeName;
                nodeIPInput.value = currentIP;
                nodePortInput.value = '5000';
                nodeRoleSelect.value = currentRole;
                nodePriorityInput.value = currentPriority;
                nodeDescriptionInput.value = `${nodeName} cluster node`;
                nodeAutoStartCheckbox.checked = true;

                // Show modal
                const modal = document.getElementById('nodeModal');
                if (modal) {
                    modal.classList.add('show');
                    console.log('Modal should now be visible');
                } else {
                    console.error('Modal element not found');
                    alert('Error: Modal not found. Please refresh the page and try again.');
                }
            } catch (error) {
                console.error('Error in editNode:', error);
                alert('Error editing node: ' + error.message);
            }
        }

        function closeNodeModal() {
            document.getElementById('nodeModal').classList.remove('show');
            currentEditingNode = null;
        }

        function saveNode() {
            // Get form data
            const nodeName = document.getElementById('nodeName').value.trim();
            const nodeIP = document.getElementById('nodeIP').value.trim();
            const nodePort = document.getElementById('nodePort').value;
            const nodeRole = document.getElementById('nodeRole').value;
            const nodePriority = document.getElementById('nodePriority').value;
            const nodeDescription = document.getElementById('nodeDescription').value.trim();
            const nodeAutoStart = document.getElementById('nodeAutoStart').checked;

            // Validate required fields
            if (!nodeName || !nodeIP) {
                alert('Please fill in all required fields (Node Name and IP Address).');
                return;
            }

            // Validate IP address format (basic check)
            const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
            if (!ipRegex.test(nodeIP)) {
                alert('Please enter a valid IP address.');
                return;
            }

            const action = currentEditingNode ? 'updated' : 'added';
            const actionPast = currentEditingNode ? 'Updated' : 'Added';

            if (confirm(`${actionPast} node configuration?\n\nName: ${nodeName}\nIP: ${nodeIP}:${nodePort}\nRole: ${nodeRole}\nPriority: ${nodePriority}\nAuto-start: ${nodeAutoStart ? 'Yes' : 'No'}\n\nContinue?`)) {
                alert(`Node ${action} successfully!\n\n✓ Node name: ${nodeName}\n✓ IP address: ${nodeIP}:${nodePort}\n✓ Role: ${nodeRole}\n✓ Priority: ${nodePriority}\n✓ Configuration saved`);

                closeNodeModal();
                loadNodesData(); // Refresh the node list
            }
        }



        function deleteNode(nodeName) {
            if (confirm(`Delete node ${nodeName}?\n\nThis will:\n• Remove node from cluster\n• Stop all services on node\n• Clear node configuration\n\nThis action cannot be undone.\n\nContinue?`)) {
                alert(`Node ${nodeName} deleted successfully!\n\n✓ Node removed from cluster\n✓ Services stopped\n✓ Configuration cleared\n✓ Cluster updated`);
                loadNodesData(); // Refresh the node list
            }
        }

        function refreshNodes() {
            loadNodesData();
            alert('Node list refreshed!\n\n✓ Status updated\n✓ Metrics refreshed\n✓ Health checks completed');
        }

        function testEditFunction() {
            console.log('Test function called');
            alert('JavaScript is working! Now testing edit function...');

            // Test if we can find the modal
            const modal = document.getElementById('nodeModal');
            if (modal) {
                console.log('Modal found:', modal);
                alert('Modal element found! Testing edit function...');
                editNode('EBO-PRIMARY');
            } else {
                console.error('Modal not found!');
                alert('Error: Modal element not found!');
            }
        }

        // Network Configuration Functions
        function saveNetworkConfig() {
            const virtualIP = document.getElementById('virtual-ip-input').value;
            const subnetMask = document.getElementById('subnet-mask-input').value;
            const splitBrainChecker = document.getElementById('split-brain-input').value;

            if (!virtualIP || !subnetMask || !splitBrainChecker) {
                alert('Please fill in all network configuration fields.');
                return;
            }

            if (confirm(`Save network configuration?\n\nVirtual IP: ${virtualIP}\nSubnet Mask: ${subnetMask}\nSplit-Brain Checker: ${splitBrainChecker}\n\nContinue?`)) {
                alert(`Network configuration saved!\n\n✓ Virtual IP: ${virtualIP}\n✓ Subnet Mask: ${subnetMask}\n✓ Split-Brain Checker: ${splitBrainChecker}\n✓ Configuration applied`);
            }
        }

        function testNetworkConfig() {
            const virtualIP = document.getElementById('virtual-ip-input').value;
            const splitBrainChecker = document.getElementById('split-brain-input').value;

            if (confirm(`Test network configuration?\n\nThis will:\n• Test virtual IP connectivity\n• Verify split-brain checker\n• Check network routing\n\nContinue?`)) {
                alert(`Network configuration test completed!\n\n✓ Virtual IP ${virtualIP} - Reachable\n✓ Split-brain checker ${splitBrainChecker} - Responding\n✓ Network routing - OK\n✓ All tests passed`);
            }
        }

        // Directory Management Functions
        let currentEditingDirectory = null;
        let directoriesData = []; // Store directory data

        function addReplicatedDirectory() {
            currentEditingDirectory = null;
            document.getElementById('directoryModalTitle').textContent = 'Add Replicated Directory';

            // Clear form
            document.getElementById('directoryPath').value = '';
            document.getElementById('replicationMode').value = 'synchronous';
            document.getElementById('directoryPriority').value = 'normal';
            document.getElementById('includePatterns').value = '*.*';
            document.getElementById('excludePatterns').value = '';
            document.getElementById('maxFileSize').value = '1024';
            document.getElementById('syncInterval').value = '5';
            document.getElementById('directoryDescription').value = '';
            document.getElementById('enableCompression').checked = true;
            document.getElementById('enableEncryption').checked = true;
            document.getElementById('createIfMissing').checked = true;

            // Show modal
            document.getElementById('directoryModal').classList.add('show');
        }

        function editDirectory(index) {
            console.log('editDirectory called with index:', index);

            try {
                currentEditingDirectory = index;
                document.getElementById('directoryModalTitle').textContent = 'Edit Replicated Directory';

                // Get directory data (in real implementation, this would fetch from server)
                const sampleDirs = [
                    {
                        path: 'C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation',
                        description: 'EBO Application Data',
                        mode: 'synchronous',
                        priority: 'high',
                        includePatterns: '*.*',
                        excludePatterns: '*.tmp, *.log',
                        maxFileSize: 1024,
                        syncInterval: 5
                    },
                    {
                        path: 'C:\\EBO_Data',
                        description: 'EBO Database Files',
                        mode: 'synchronous',
                        priority: 'high',
                        includePatterns: '*.*, *.db',
                        excludePatterns: '*.tmp, *.bak',
                        maxFileSize: 2048,
                        syncInterval: 3
                    },
                    {
                        path: 'C:\\EBO_Config',
                        description: 'EBO Configuration Files',
                        mode: 'synchronous',
                        priority: 'normal',
                        includePatterns: '*.config, *.xml, *.json',
                        excludePatterns: '*.tmp',
                        maxFileSize: 100,
                        syncInterval: 10
                    },
                    {
                        path: 'C:\\EBO_Logs',
                        description: 'EBO Log Files',
                        mode: 'asynchronous',
                        priority: 'low',
                        includePatterns: '*.log, *.txt',
                        excludePatterns: '*.tmp, *.old',
                        maxFileSize: 500,
                        syncInterval: 60
                    }
                ];

                const dir = sampleDirs[index];
                if (!dir) {
                    console.error('Directory not found for index:', index);
                    alert('Error: Directory not found.');
                    return;
                }

                console.log('Editing directory:', dir);

                // Get form elements
                const directoryPathInput = document.getElementById('directoryPath');
                const replicationModeSelect = document.getElementById('replicationMode');
                const directoryPrioritySelect = document.getElementById('directoryPriority');
                const includePatternsInput = document.getElementById('includePatterns');
                const excludePatternsInput = document.getElementById('excludePatterns');
                const maxFileSizeInput = document.getElementById('maxFileSize');
                const syncIntervalInput = document.getElementById('syncInterval');
                const directoryDescriptionInput = document.getElementById('directoryDescription');
                const enableCompressionCheckbox = document.getElementById('enableCompression');
                const enableEncryptionCheckbox = document.getElementById('enableEncryption');
                const createIfMissingCheckbox = document.getElementById('createIfMissing');

                // Check if all elements exist
                if (!directoryPathInput || !replicationModeSelect || !directoryPrioritySelect ||
                    !includePatternsInput || !excludePatternsInput || !maxFileSizeInput ||
                    !syncIntervalInput || !directoryDescriptionInput || !enableCompressionCheckbox ||
                    !enableEncryptionCheckbox || !createIfMissingCheckbox) {
                    console.error('One or more form elements not found');
                    alert('Error: Form elements not found. Please refresh the page and try again.');
                    return;
                }

                // Populate form with current data
                directoryPathInput.value = dir.path;
                replicationModeSelect.value = dir.mode;
                directoryPrioritySelect.value = dir.priority;
                includePatternsInput.value = dir.includePatterns;
                excludePatternsInput.value = dir.excludePatterns;
                maxFileSizeInput.value = dir.maxFileSize;
                syncIntervalInput.value = dir.syncInterval;
                directoryDescriptionInput.value = dir.description;
                enableCompressionCheckbox.checked = true;
                enableEncryptionCheckbox.checked = true;
                createIfMissingCheckbox.checked = true;

                console.log('Form populated successfully');

                // Show modal
                const modal = document.getElementById('directoryModal');
                if (modal) {
                    modal.classList.add('show');
                    console.log('Directory modal should now be visible');
                } else {
                    console.error('Directory modal element not found');
                    alert('Error: Modal not found. Please refresh the page and try again.');
                }
            } catch (error) {
                console.error('Error in editDirectory:', error);
                alert('Error editing directory: ' + error.message);
            }
        }

        function closeDirectoryModal() {
            document.getElementById('directoryModal').classList.remove('show');
            currentEditingDirectory = null;
        }

        function saveDirectory() {
            // Get form data
            const directoryPath = document.getElementById('directoryPath').value.trim();
            const replicationMode = document.getElementById('replicationMode').value;
            const directoryPriority = document.getElementById('directoryPriority').value;
            const includePatterns = document.getElementById('includePatterns').value.trim();
            const excludePatterns = document.getElementById('excludePatterns').value.trim();
            const maxFileSize = document.getElementById('maxFileSize').value;
            const syncInterval = document.getElementById('syncInterval').value;
            const directoryDescription = document.getElementById('directoryDescription').value.trim();
            const enableCompression = document.getElementById('enableCompression').checked;
            const enableEncryption = document.getElementById('enableEncryption').checked;
            const createIfMissing = document.getElementById('createIfMissing').checked;

            // Validate required fields
            if (!directoryPath) {
                alert('Please enter a directory path.');
                return;
            }

            // Validate path format (basic check for Windows paths)
            if (!directoryPath.match(/^[A-Za-z]:\\/) && !directoryPath.match(/^\\\\[^\\]+\\/)) {
                alert('Please enter a valid Windows path (e.g., C:\\MyFolder or \\\\server\\share).');
                return;
            }

            const action = currentEditingDirectory !== null ? 'updated' : 'added';
            const actionPast = currentEditingDirectory !== null ? 'Updated' : 'Added';

            const confirmMessage = `${actionPast} directory replication?\n\n` +
                `Path: ${directoryPath}\n` +
                `Mode: ${replicationMode}\n` +
                `Priority: ${directoryPriority}\n` +
                `Include: ${includePatterns}\n` +
                `Exclude: ${excludePatterns}\n` +
                `Max Size: ${maxFileSize} MB\n` +
                `Sync Interval: ${syncInterval}s\n` +
                `Compression: ${enableCompression ? 'Yes' : 'No'}\n` +
                `Encryption: ${enableEncryption ? 'Yes' : 'No'}\n\n` +
                `Continue?`;

            if (confirm(confirmMessage)) {
                alert(`Directory ${action} successfully!\n\n✓ Path: ${directoryPath}\n✓ Mode: ${replicationMode}\n✓ Priority: ${directoryPriority}\n✓ Configuration saved\n✓ Replication will start automatically`);

                closeDirectoryModal();
                loadReplicatedDirectories(); // Refresh the directory list
            }
        }

        function deleteDirectory(index) {
            const sampleDirs = [
                { path: 'C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation' },
                { path: 'C:\\EBO_Data' },
                { path: 'C:\\EBO_Config' },
                { path: 'C:\\EBO_Logs' }
            ];

            const dir = sampleDirs[index];
            if (!dir) return;

            if (confirm(`Remove directory from replication?\n\nPath: ${dir.path}\n\nThis will:\n• Stop replicating this directory\n• Remove from replication configuration\n• Keep existing files on both nodes\n\nThis action cannot be undone.\n\nContinue?`)) {
                alert(`Directory removed from replication!\n\n✓ Path: ${dir.path}\n✓ Replication stopped\n✓ Configuration removed\n✓ Files preserved on both nodes`);
                loadReplicatedDirectories(); // Refresh the directory list
            }
        }

        function syncDirectory(index) {
            const sampleDirs = [
                { path: 'C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation' },
                { path: 'C:\\EBO_Data' },
                { path: 'C:\\EBO_Config' },
                { path: 'C:\\EBO_Logs' }
            ];

            const dir = sampleDirs[index];
            if (!dir) return;

            if (confirm(`Force synchronization for directory?\n\nPath: ${dir.path}\n\nThis will:\n• Force immediate sync\n• Check all files for changes\n• Update secondary nodes\n\nContinue?`)) {
                alert(`Directory synchronization completed!\n\n✓ Path: ${dir.path}\n✓ All files checked\n✓ Changes synchronized\n✓ Secondary nodes updated`);
                loadReplicatedDirectories(); // Refresh the directory list
            }
        }

        function refreshDirectories() {
            loadReplicatedDirectories();
            alert('Directory list refreshed!\n\n✓ Status updated\n✓ File counts refreshed\n✓ Sync times updated');
        }

        function editExclusionPatterns() {
            const currentPatterns = '*.tmp, *.log, *.bak, temp\\*, cache\\*, ~$*, Thumbs.db, .DS_Store';
            const newPatterns = prompt('Edit global exclusion patterns (comma-separated):', currentPatterns);

            if (newPatterns !== null && newPatterns.trim() !== '') {
                alert(`Global exclusion patterns updated!\n\n✓ New patterns: ${newPatterns}\n✓ Applied to all directories\n✓ Replication filters updated`);
                loadReplicatedDirectories(); // Refresh to show updated patterns
            }
        }


    </script>

    <!-- Node Editor Modal -->
    <div id="nodeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-server"></i>
                    <span id="modalTitle">Add Node</span>
                </div>
                <button class="modal-close" onclick="closeNodeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="nodeForm" class="config-form">
                    <div class="form-group">
                        <label class="form-label">Node Name</label>
                        <input type="text" id="nodeName" class="form-input" placeholder="e.g., EBO-NODE-03" required>
                        <div class="form-help">Unique identifier for this cluster node</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">IP Address</label>
                            <input type="text" id="nodeIP" class="form-input" placeholder="********2" required>
                            <div class="form-help">Node network address</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Port</label>
                            <input type="number" id="nodePort" class="form-input" value="5000" required>
                            <div class="form-help">SafeKit service port</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Role</label>
                            <select id="nodeRole" class="form-input" required>
                                <option value="secondary">Secondary</option>
                                <option value="primary">Primary</option>
                            </select>
                            <div class="form-help">Node role in cluster</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Priority</label>
                            <input type="number" id="nodePriority" class="form-input" value="100" min="1" max="1000" required>
                            <div class="form-help">Failover priority (higher = preferred)</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <input type="text" id="nodeDescription" class="form-input" placeholder="Optional description">
                        <div class="form-help">Optional node description</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="nodeAutoStart" checked style="margin-right: 0.5rem;">
                            Auto-start services on this node
                        </label>
                        <div class="form-help">Automatically start EBO services when node comes online</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" onclick="closeNodeModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveNode()">
                    <i class="fas fa-save"></i> Save Node
                </button>
            </div>
        </div>
    </div>

    <!-- Directory Replication Modal -->
    <div id="directoryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-folder"></i>
                    <span id="directoryModalTitle">Add Replicated Directory</span>
                </div>
                <button class="modal-close" onclick="closeDirectoryModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="directoryForm" class="config-form">
                    <div class="form-group">
                        <label class="form-label">Directory Path</label>
                        <input type="text" id="directoryPath" class="form-input" placeholder="C:\EBO_Data" required>
                        <div class="form-help">Full path to the directory to replicate</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Replication Mode</label>
                            <select id="replicationMode" class="form-input" required>
                                <option value="synchronous">Synchronous (Real-time)</option>
                                <option value="asynchronous">Asynchronous (Batch)</option>
                                <option value="manual">Manual Only</option>
                            </select>
                            <div class="form-help">How files are replicated</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Priority</label>
                            <select id="directoryPriority" class="form-input" required>
                                <option value="high">High</option>
                                <option value="normal" selected>Normal</option>
                                <option value="low">Low</option>
                            </select>
                            <div class="form-help">Replication priority</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Include Patterns</label>
                        <input type="text" id="includePatterns" class="form-input" placeholder="*.*, *.config, *.data" value="*.*">
                        <div class="form-help">File patterns to include (comma-separated)</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Exclude Patterns</label>
                        <input type="text" id="excludePatterns" class="form-input" placeholder="*.tmp, *.log, *.bak, temp\*">
                        <div class="form-help">File patterns to exclude (comma-separated)</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Max File Size (MB)</label>
                            <input type="number" id="maxFileSize" class="form-input" value="1024" min="1" max="10240">
                            <div class="form-help">Maximum file size to replicate</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Sync Interval (seconds)</label>
                            <input type="number" id="syncInterval" class="form-input" value="5" min="1" max="3600">
                            <div class="form-help">How often to check for changes</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <input type="text" id="directoryDescription" class="form-input" placeholder="EBO application data">
                        <div class="form-help">Optional description for this directory</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="enableCompression" checked style="margin-right: 0.5rem;">
                            Enable compression during transfer
                        </label>
                        <div class="form-help">Compress files during replication to save bandwidth</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="enableEncryption" checked style="margin-right: 0.5rem;">
                            Enable encryption during transfer
                        </label>
                        <div class="form-help">Encrypt files during replication for security</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="createIfMissing" checked style="margin-right: 0.5rem;">
                            Create directory if it doesn't exist on target
                        </label>
                        <div class="form-help">Automatically create the directory structure on secondary nodes</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" onclick="closeDirectoryModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveDirectory()">
                    <i class="fas fa-save"></i> Save Directory
                </button>
            </div>
        </div>
    </div>
</body>
</html>
