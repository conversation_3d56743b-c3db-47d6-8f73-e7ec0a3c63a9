{% extends "redundancy_base.html" %}

{% block title %}Storage Redundancy - Professional Redundancy Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="fas fa-hdd me-2 text-primary"></i>
                Storage Redundancy Management
                <small class="text-muted ms-2">Complete Storage Protection</small>
            </h1>
            <div>
                <button class="btn btn-success me-2" id="startAllStorage">
                    <i class="fas fa-play me-1"></i>Start All Sync
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStorageGroupModal">
                    <i class="fas fa-plus me-1"></i>Add Storage Group
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Storage Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-layer-group fa-2x text-primary mb-2"></i>
                <div class="metric-value text-primary" id="totalGroups">-</div>
                <div class="metric-label">Storage Groups</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-sync fa-2x text-success mb-2"></i>
                <div class="metric-value text-success" id="syncingGroups">-</div>
                <div class="metric-label">Syncing</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-database fa-2x text-info mb-2"></i>
                <div class="metric-value text-info" id="totalCapacity">-</div>
                <div class="metric-label">Total Capacity (GB)</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-chart-pie fa-2x text-warning mb-2"></i>
                <div class="metric-value text-warning" id="usedCapacity">-</div>
                <div class="metric-label">Used Capacity (GB)</div>
            </div>
        </div>
    </div>
</div>

<!-- Storage Groups List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Storage Groups & Locations
                </h5>
            </div>
            <div class="card-body">
                <div id="storageGroupsList">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">Loading storage groups...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Storage Group Modal -->
<div class="modal fade" id="addStorageGroupModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Storage Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addStorageGroupForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="groupName" class="form-label">Group Name *</label>
                                <input type="text" class="form-control" id="groupName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="groupDescription" class="form-label">Description</label>
                                <input type="text" class="form-control" id="groupDescription">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="storageType" class="form-label">Storage Type *</label>
                                <select class="form-select" id="storageType" required>
                                    <option value="">Select Type</option>
                                    <option value="application_data">Application Data</option>
                                    <option value="database_files">Database Files</option>
                                    <option value="configuration_files">Configuration Files</option>
                                    <option value="log_files">Log Files</option>
                                    <option value="local_drive">Local Drive</option>
                                    <option value="network_share">Network Share</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="replicationMode" class="form-label">Replication Mode</label>
                                <select class="form-select" id="replicationMode">
                                    <option value="real_time">Real-time</option>
                                    <option value="scheduled">Scheduled</option>
                                    <option value="on_change">On Change</option>
                                    <option value="manual">Manual</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="syncInterval" class="form-label">Sync Interval (seconds)</label>
                                <input type="number" class="form-control" id="syncInterval" value="300" min="30">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoFailover" checked>
                                    <label class="form-check-label" for="autoFailover">
                                        Auto Failover
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="compressionEnabled">
                                    <label class="form-check-label" for="compressionEnabled">
                                        Compression
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="encryptionEnabled">
                                    <label class="form-check-label" for="encryptionEnabled">
                                        Encryption
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="retentionDays" class="form-label">Retention (days)</label>
                                <input type="number" class="form-control" id="retentionDays" value="30" min="1">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="excludePatterns" class="form-label">Exclude Patterns (comma-separated)</label>
                        <input type="text" class="form-control" id="excludePatterns" placeholder="*.tmp, *.log, temp/*">
                        <div class="form-text">Files/folders matching these patterns will be excluded from sync</div>
                    </div>
                    
                    <hr>
                    <h6>Storage Locations</h6>
                    <div id="locationsList">
                        <!-- Locations will be added dynamically -->
                    </div>
                    
                    <button type="button" class="btn btn-outline-primary" id="addLocationBtn">
                        <i class="fas fa-plus me-1"></i>Add Storage Location
                    </button>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveStorageGroup">Create Storage Group</button>
            </div>
        </div>
    </div>
</div>

<!-- Storage Location Template -->
<template id="locationTemplate">
    <div class="card mb-3 location-card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">Storage Location</h6>
            <button type="button" class="btn btn-sm btn-outline-danger remove-location">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Location Name *</label>
                        <input type="text" class="form-control location-name" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">Path *</label>
                        <input type="text" class="form-control location-path" required placeholder="C:\MyApp\Data or \\server\share">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Host</label>
                        <input type="text" class="form-control location-host" value="localhost">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label class="form-label">Capacity (GB)</label>
                        <input type="number" class="form-control location-capacity" min="1">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input location-primary" type="radio" name="primary-location">
                            <label class="form-check-label">Primary Location</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input location-active" type="checkbox" checked>
                            <label class="form-check-label">Active</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block extra_js %}
<script>
let storageData = {};

$(document).ready(function() {
    loadStorageGroups();
    
    $('#addLocationBtn').click(addLocation);
    $('#saveStorageGroup').click(saveStorageGroup);
    $('#startAllStorage').click(startAllStorage);
    
    // Auto-refresh every 30 seconds
    setInterval(loadStorageGroups, 30000);
    
    // Add initial locations
    addLocation(); // Primary
    addLocation(); // Secondary
});

function loadStorageGroups() {
    $.get('/api/storage-redundancy')
        .done(function(response) {
            if (response.success) {
                storageData = response.data;
                displayStorageGroups(response.data);
                updateStorageMetrics(response.data);
            } else {
                showAlert('danger', 'Failed to load storage groups: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to connect to server');
        });
}

function displayStorageGroups(data) {
    let html = '';
    
    if (!data.storage_groups || Object.keys(data.storage_groups).length === 0) {
        html = '<div class="text-center py-5"><p class="text-muted">No storage groups configured.</p></div>';
    } else {
        Object.entries(data.storage_groups).forEach(([groupName, group]) => {
            const syncingCount = group.locations.filter(loc => loc.sync_status === 'syncing').length;
            const healthyCount = group.locations.filter(loc => loc.sync_status === 'healthy').length;
            
            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">
                                    <i class="fas fa-folder me-2 text-primary"></i>
                                    ${groupName}
                                    <span class="badge bg-secondary ms-2">${group.storage_type.toUpperCase()}</span>
                                    <span class="badge bg-info ms-1">${group.replication_mode.toUpperCase()}</span>
                                </h6>
                                <small class="text-muted">${group.description}</small>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="manualSync('${groupName}')">
                                    <i class="fas fa-sync"></i> Sync
                                </button>
                                <button class="btn btn-sm btn-outline-success me-1" onclick="startStorageMonitoring('${groupName}')">
                                    <i class="fas fa-play"></i> Start
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="stopStorageMonitoring('${groupName}')">
                                    <i class="fas fa-stop"></i> Stop
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Configuration:</strong><br>
                                <small class="text-muted">
                                    Sync Interval: ${group.sync_interval}s<br>
                                    Auto Failover: ${group.auto_failover ? 'Enabled' : 'Disabled'}<br>
                                    Compression: ${group.compression_enabled ? 'Enabled' : 'Disabled'}<br>
                                    Encryption: ${group.encryption_enabled ? 'Enabled' : 'Disabled'}
                                </small>
                            </div>
                            <div class="col-md-6">
                                <strong>Locations (${group.locations.length}):</strong><br>
                                <div class="mt-2">
            `;
            
            group.locations.forEach(location => {
                const statusClass = getStorageStatusClass(location.sync_status);
                const statusIcon = getStorageStatusIcon(location.sync_status);
                const roleClass = location.is_primary ? 'bg-primary' : 'bg-secondary';
                
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>
                            <i class="${statusIcon} ${statusClass} me-1"></i>
                            ${location.name}
                            <span class="badge ${roleClass} ms-1">${location.is_primary ? 'PRIMARY' : 'BACKUP'}</span>
                        </span>
                        <small class="text-muted">
                            ${location.path}
                            ${location.capacity_gb ? '• ' + location.capacity_gb.toFixed(1) + 'GB' : ''}
                            ${location.last_sync ? '• Last: ' + new Date(location.last_sync).toLocaleTimeString() : ''}
                        </small>
                    </div>
                `;
            });
            
            html += `
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    $('#storageGroupsList').html(html);
}

function updateStorageMetrics(data) {
    const groups = data.storage_groups || {};
    const totalGroups = Object.keys(groups).length;
    let syncingGroups = 0;
    let totalCapacity = 0;
    let usedCapacity = 0;
    
    Object.values(groups).forEach(group => {
        const hasSyncing = group.locations.some(loc => loc.sync_status === 'syncing');
        if (hasSyncing) syncingGroups++;
        
        group.locations.forEach(location => {
            if (location.capacity_gb) totalCapacity += location.capacity_gb;
            if (location.used_gb) usedCapacity += location.used_gb;
        });
    });
    
    $('#totalGroups').text(totalGroups);
    $('#syncingGroups').text(syncingGroups);
    $('#totalCapacity').text(totalCapacity.toFixed(1));
    $('#usedCapacity').text(usedCapacity.toFixed(1));
}

function addLocation() {
    const template = document.getElementById('locationTemplate');
    const clone = template.content.cloneNode(true);
    
    // Add remove functionality
    clone.querySelector('.remove-location').addEventListener('click', function() {
        this.closest('.location-card').remove();
    });
    
    // Handle primary location radio buttons
    const radioButton = clone.querySelector('.location-primary');
    radioButton.name = 'primary-location-' + Date.now();
    
    document.getElementById('locationsList').appendChild(clone);
}

function saveStorageGroup() {
    const groupName = $('#groupName').val();
    const description = $('#groupDescription').val();
    const storageType = $('#storageType').val();
    const replicationMode = $('#replicationMode').val();
    const syncInterval = parseInt($('#syncInterval').val());
    const autoFailover = $('#autoFailover').is(':checked');
    const compressionEnabled = $('#compressionEnabled').is(':checked');
    const encryptionEnabled = $('#encryptionEnabled').is(':checked');
    const retentionDays = parseInt($('#retentionDays').val());
    const excludePatterns = $('#excludePatterns').val().split(',').map(p => p.trim()).filter(p => p);
    
    if (!groupName || !storageType) {
        showAlert('warning', 'Please fill in required fields');
        return;
    }
    
    // Collect locations
    const locations = [];
    $('.location-card').each(function() {
        const card = $(this);
        const location = {
            name: card.find('.location-name').val(),
            path: card.find('.location-path').val(),
            host: card.find('.location-host').val() || 'localhost',
            storage_type: storageType,
            is_primary: card.find('.location-primary').is(':checked'),
            is_active: card.find('.location-active').is(':checked'),
            capacity_gb: parseFloat(card.find('.location-capacity').val()) || null
        };
        
        if (location.name && location.path) {
            locations.push(location);
        }
    });
    
    if (locations.length < 2) {
        showAlert('warning', 'At least 2 storage locations are required');
        return;
    }
    
    // Ensure exactly one primary location
    const primaryCount = locations.filter(loc => loc.is_primary).length;
    if (primaryCount !== 1) {
        showAlert('warning', 'Exactly one primary location must be selected');
        return;
    }
    
    const storageGroupConfig = {
        name: groupName,
        description: description,
        storage_type: storageType,
        replication_mode: replicationMode,
        sync_interval: syncInterval,
        auto_failover: autoFailover,
        compression_enabled: compressionEnabled,
        encryption_enabled: encryptionEnabled,
        retention_days: retentionDays,
        exclude_patterns: excludePatterns,
        locations: locations
    };
    
    $.ajax({
        url: '/api/storage-redundancy',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(storageGroupConfig),
        success: function(response) {
            if (response.success) {
                showAlert('success', 'Storage group created successfully');
                $('#addStorageGroupModal').modal('hide');
                $('#addStorageGroupForm')[0].reset();
                $('#locationsList').empty();
                loadStorageGroups();
            } else {
                showAlert('danger', 'Failed to create storage group: ' + response.message);
            }
        },
        error: function() {
            showAlert('danger', 'Failed to create storage group');
        }
    });
}

function startStorageMonitoring(groupName) {
    $.post(`/api/storage-redundancy/${groupName}/start`)
        .done(function(response) {
            if (response.success) {
                showAlert('success', `Storage monitoring started for: ${groupName}`);
                loadStorageGroups();
            } else {
                showAlert('danger', 'Failed to start monitoring: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to start monitoring');
        });
}

function stopStorageMonitoring(groupName) {
    $.post(`/api/storage-redundancy/${groupName}/stop`)
        .done(function(response) {
            if (response.success) {
                showAlert('warning', `Storage monitoring stopped for: ${groupName}`);
                loadStorageGroups();
            } else {
                showAlert('danger', 'Failed to stop monitoring: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to stop monitoring');
        });
}

function startAllStorage() {
    $.post('/api/storage-redundancy/start-all')
        .done(function(response) {
            if (response.success) {
                showAlert('success', 'All storage group monitoring started');
                loadStorageGroups();
            } else {
                showAlert('danger', 'Failed to start all storage groups: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to start all storage groups');
        });
}

function manualSync(groupName) {
    const group = storageData.storage_groups[groupName];
    if (!group) return;
    
    const primaryLocation = group.locations.find(loc => loc.is_primary);
    const secondaryLocations = group.locations.filter(loc => !loc.is_primary && loc.is_active);
    
    if (!primaryLocation || secondaryLocations.length === 0) {
        showAlert('warning', 'No valid source/target locations for sync');
        return;
    }
    
    if (confirm(`Manually sync from "${primaryLocation.name}" to all secondary locations?`)) {
        secondaryLocations.forEach(target => {
            $.ajax({
                url: `/api/storage-redundancy/${groupName}/sync`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    source_location: primaryLocation.name,
                    target_location: target.name
                }),
                success: function(response) {
                    if (response.success) {
                        showAlert('success', `Manual sync initiated: ${primaryLocation.name} → ${target.name}`);
                    } else {
                        showAlert('danger', `Sync failed: ${response.message}`);
                    }
                },
                error: function() {
                    showAlert('danger', `Failed to sync to ${target.name}`);
                }
            });
        });
        
        // Refresh after a short delay
        setTimeout(loadStorageGroups, 2000);
    }
}

function getStorageStatusClass(status) {
    switch(status) {
        case 'healthy': return 'text-success';
        case 'syncing': return 'text-info';
        case 'out_of_sync': return 'text-warning';
        case 'failed': return 'text-danger';
        case 'offline': return 'text-secondary';
        default: return 'text-secondary';
    }
}

function getStorageStatusIcon(status) {
    switch(status) {
        case 'healthy': return 'fas fa-check-circle';
        case 'syncing': return 'fas fa-sync fa-spin';
        case 'out_of_sync': return 'fas fa-exclamation-triangle';
        case 'failed': return 'fas fa-times-circle';
        case 'offline': return 'fas fa-power-off';
        default: return 'fas fa-question-circle';
    }
}
</script>
{% endblock %}
