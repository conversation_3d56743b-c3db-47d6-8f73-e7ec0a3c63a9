# SafeKit Redundancy Management System - User Guide

## Quick Start

1. Run INSTALL.bat as Administrator
2. Double-click QUICK_START.bat  
3. Access SafeKit Console: http://localhost:5002/safekit-console

## EBO Configuration

### Add EBO Servers
1. Go to Nodes tab
2. Click Add Node
3. Configure:
   - Node Name: EBO-PRIMARY
   - IP Address: [Your server IP]
   - Role: Primary
   - Priority: 1000

### Setup Data Replication  
1. Go to Replication tab
2. Click Add Directory
3. Configure:
   - Path: C:\Program Files\Schneider Electric\EcoStruxure Building Operation\Data
   - Mode: Synchronous (Real-time)
   - Priority: High

## Network Access

Replace 'localhost' with server IP for remote access:
- Example: http://*************:5002/safekit-console

## Troubleshooting

- Port 5002 in use: Restart computer or change port
- Can't access remotely: Check Windows Firewall
- Python errors: Re-run INSTALL.bat as Administrator

Complete enterprise-grade redundancy management for building automation systems.
