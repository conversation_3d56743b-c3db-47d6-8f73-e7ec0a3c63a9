# Hardware Redundancy System - Web UI

A modern web-based interface for configuring and monitoring your hardware redundancy system. This provides an intuitive dashboard for managing monitors, viewing system status, and ensuring high availability of your critical hardware components.

## 🌟 Features

### 📊 Real-time Dashboard
- **Live Monitor Status**: Real-time health status of all configured monitors
- **System Overview**: Quick stats showing total, healthy, and unhealthy monitors
- **System Information**: Network interfaces and disk partition status
- **Auto-refresh**: Automatic updates every 30 seconds

### ⚙️ Configuration Management
- **Visual Monitor Setup**: Easy-to-use forms for adding monitors
- **Multiple Monitor Types**: Support for Disk, Network, and HTTP monitors
- **Global Settings**: Configure check intervals and logging preferences
- **Real-time Validation**: Instant feedback on configuration changes

### 🔧 Monitor Types Supported

#### 💾 Disk Monitors
- Monitor disk usage on any path/drive
- Configurable threshold percentages
- Real-time usage statistics

#### 🌐 Network Monitors
- Monitor network interface status
- Support for up/down status checking
- Auto-detection of available interfaces

#### 🌍 HTTP Monitors
- Monitor web service health
- Configurable status codes and timeouts
- Support for any HTTP/HTTPS endpoint

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# Install web UI dependencies
pip install -r web_ui/requirements.txt

# Or use the startup script with auto-install
python start_web_ui.py --install-deps
```

### 2. Start the Web Interface
```bash
# Simple start
python start_web_ui.py

# With custom host/port
python start_web_ui.py --host 0.0.0.0 --port 8080

# With debug mode
python start_web_ui.py --debug
```

### 3. Access the Interface
- **Dashboard**: http://localhost:5000
- **Configuration**: http://localhost:5000/config

## 📱 User Interface

### Dashboard Page
The dashboard provides a comprehensive overview of your system:

- **System Stats**: Total monitors, healthy/unhealthy counts, last update time
- **Monitor Status Table**: Detailed status of each monitor with health indicators
- **System Information**: Network interfaces and disk usage
- **Control Buttons**: Start/stop monitoring, refresh status

### Configuration Page
The configuration page allows you to:

- **Add New Monitors**: Use the intuitive form to add disk, network, or HTTP monitors
- **Edit Existing Monitors**: Modify monitor settings with inline editing
- **Global Settings**: Configure check intervals and logging preferences
- **Save Configuration**: Apply changes to the system configuration

## 🔧 Configuration

### Global Settings
- **Check Interval**: How often monitors are checked (5-3600 seconds)
- **Log Level**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Log File Path**: Where to store log files
- **Console/File Logging**: Enable/disable different logging outputs

### Monitor Configuration

#### Disk Monitor
```yaml
- name: SystemDisk
  type: disk
  path: /
  threshold_percentage: 90
```

#### Network Monitor
```yaml
- name: PrimaryNIC
  type: network
  interface: eth0
  expected_status: up
```

#### HTTP Monitor
```yaml
- name: WebService
  type: http
  url: https://example.com/health
  expected_status: 200
  timeout_seconds: 10
```

## 🔌 API Endpoints

The web UI provides RESTful API endpoints:

### Configuration
- `GET /api/config` - Get current configuration
- `POST /api/config` - Save configuration

### Monitoring
- `GET /api/monitors/status` - Get monitor status
- `POST /api/monitors/start` - Start monitoring
- `POST /api/monitors/stop` - Stop monitoring

### System Information
- `GET /api/system/info` - Get system information

## 🛡️ Security Considerations

### Production Deployment
For production use, consider:

1. **Change Secret Key**: Update the Flask secret key in `web_ui/app.py`
2. **Use HTTPS**: Deploy behind a reverse proxy with SSL
3. **Authentication**: Add user authentication for sensitive environments
4. **Firewall**: Restrict access to trusted networks only

### Example Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔧 Customization

### Styling
The interface uses Bootstrap 5 with custom CSS. You can modify the appearance by:
- Editing the CSS variables in `templates/base.html`
- Adding custom styles to the `<style>` section
- Creating a separate CSS file in `web_ui/static/`

### Adding New Monitor Types
To add new monitor types:
1. Create the monitor class in `src/redundancy/monitors/`
2. Update the web UI forms in `templates/config.html`
3. Add handling in `web_ui/app.py`

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Use a different port
python start_web_ui.py --port 8080
```

#### Dependencies Missing
```bash
# Install dependencies
pip install -r web_ui/requirements.txt
```

#### Configuration Not Loading
- Check that `config.yaml` exists in the project root
- Verify YAML syntax is correct
- Check file permissions

#### Monitors Not Starting
- Verify monitor configuration is valid
- Check system permissions for disk/network access
- Review logs for error messages

## 📝 Development

### Project Structure
```
web_ui/
├── app.py              # Flask application
├── templates/          # HTML templates
│   ├── base.html      # Base template
│   ├── dashboard.html # Dashboard page
│   └── config.html    # Configuration page
├── static/            # Static files (CSS, JS, images)
└── requirements.txt   # Python dependencies
```

### Running in Development Mode
```bash
python start_web_ui.py --debug
```

This enables:
- Auto-reload on code changes
- Detailed error messages
- Debug toolbar

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the main project README.md
3. Check the application logs in the `logs/` directory

## 🎯 Future Enhancements

Planned features:
- User authentication and authorization
- Email/SMS alerts for monitor failures
- Historical data and trending
- Mobile-responsive improvements
- Export/import configuration
- Multi-server monitoring
