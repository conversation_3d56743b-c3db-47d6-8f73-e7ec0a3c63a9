"""
Authentication Middleware for Flask Application
Provides session management and route protection
"""

from functools import wraps
from flask import request, jsonify, session, redirect, url_for
from user_management import user_manager, Permission
import logging

logger = logging.getLogger(__name__)

def get_client_ip():
    """Get client IP address"""
    if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
        return request.environ['REMOTE_ADDR']
    else:
        return request.environ['HTTP_X_FORWARDED_FOR']

def get_user_agent():
    """Get user agent string"""
    return request.headers.get('User-Agent', '')

def get_current_user():
    """Get current authenticated user from session"""
    session_token = session.get('session_token')
    if not session_token:
        return None
    
    return user_manager.validate_session(session_token)

def login_required(f):
    """Decorator to require authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if not user:
            if request.is_json:
                return jsonify({'error': 'Authentication required', 'redirect': '/login'}), 401
            return redirect('/login')
        
        # Add user to request context
        request.current_user = user
        return f(*args, **kwargs)
    
    return decorated_function

def permission_required(permission: Permission):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user = get_current_user()
            if not user:
                if request.is_json:
                    return jsonify({'error': 'Authentication required', 'redirect': '/login'}), 401
                return redirect('/login')
            
            if not user_manager.has_permission(user['role'], permission):
                if request.is_json:
                    return jsonify({'error': 'Insufficient permissions'}), 403
                return jsonify({'error': 'Access denied - insufficient permissions'}), 403
            
            # Add user to request context
            request.current_user = user
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def admin_required(f):
    """Decorator to require admin role"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if not user:
            if request.is_json:
                return jsonify({'error': 'Authentication required', 'redirect': '/login'}), 401
            return redirect('/login')
        
        if user['role'] != 'admin':
            if request.is_json:
                return jsonify({'error': 'Admin access required'}), 403
            return jsonify({'error': 'Access denied - admin access required'}), 403
        
        # Add user to request context
        request.current_user = user
        return f(*args, **kwargs)
    
    return decorated_function

def log_user_action(action: str, resource: str, details: str = None):
    """Log user action for audit trail"""
    user = get_current_user()
    if user:
        user_manager.log_audit_event(
            user['user_id'],
            user['username'],
            action,
            resource,
            details,
            get_client_ip(),
            get_user_agent(),
            user['organization']
        )

def create_login_page():
    """Create beautiful login page HTML"""
    return '''
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>EBO Multi-Site Redundancy - Login</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            :root {
                --primary-color: #3b82f6;
                --secondary-color: #10b981;
                --danger-color: #ef4444;
                --text-primary: #1f2937;
                --text-secondary: #6b7280;
                --bg-primary: #ffffff;
                --border-color: #e5e7eb;
                --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                --radius-lg: 0.75rem;
                --radius-xl: 1rem;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
                background-size: 400% 400%;
                animation: gradientShift 15s ease infinite;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                color: var(--text-primary);
                line-height: 1.6;
            }

            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            .login-container {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: var(--radius-xl);
                padding: 3rem;
                box-shadow: var(--shadow-lg);
                border: 1px solid rgba(255, 255, 255, 0.2);
                width: 100%;
                max-width: 400px;
                position: relative;
                overflow: hidden;
            }

            .login-container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
                border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            }

            .login-header {
                text-align: center;
                margin-bottom: 2rem;
            }

            .login-icon {
                width: 4rem;
                height: 4rem;
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1rem;
                color: white;
                font-size: 1.5rem;
            }

            .login-title {
                font-size: 1.75rem;
                font-weight: 700;
                color: var(--text-primary);
                margin-bottom: 0.5rem;
            }

            .login-subtitle {
                color: var(--text-secondary);
                font-size: 0.875rem;
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 600;
                color: var(--text-primary);
                font-size: 0.875rem;
            }

            .form-input {
                width: 100%;
                padding: 0.875rem 1rem;
                border: 2px solid var(--border-color);
                border-radius: var(--radius-lg);
                font-size: 0.875rem;
                transition: all 0.3s ease;
                background: white;
            }

            .form-input:focus {
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .login-btn {
                width: 100%;
                padding: 0.875rem 1.75rem;
                background: linear-gradient(135deg, var(--primary-color), #2563eb);
                color: white;
                border: none;
                border-radius: var(--radius-lg);
                font-weight: 600;
                font-size: 0.875rem;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: var(--shadow-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
            }

            .login-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }

            .login-btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }

            .alert {
                padding: 1rem;
                border-radius: var(--radius-lg);
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.875rem;
            }

            .alert-error {
                background: rgba(239, 68, 68, 0.1);
                border: 1px solid rgba(239, 68, 68, 0.2);
                color: var(--danger-color);
            }

            .loading-spinner {
                width: 1rem;
                height: 1rem;
                border: 2px solid transparent;
                border-top: 2px solid currentColor;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .default-credentials {
                margin-top: 2rem;
                padding: 1rem;
                background: rgba(59, 130, 246, 0.1);
                border: 1px solid rgba(59, 130, 246, 0.2);
                border-radius: var(--radius-lg);
                font-size: 0.75rem;
                color: var(--primary-color);
                text-align: center;
            }

            .fade-in {
                animation: fadeIn 0.6s ease-out;
            }

            @keyframes fadeIn {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        </style>
    </head>
    <body>
        <div class="login-container fade-in">
            <div class="login-header">
                <div class="login-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="login-title">EBO Multi-Site</h1>
                <p class="login-subtitle">Redundancy Management System</p>
            </div>

            <div id="error-message"></div>

            <form id="loginForm">
                <div class="form-group">
                    <label class="form-label">Username</label>
                    <input type="text" class="form-input" id="username" name="username" required autocomplete="username">
                </div>

                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-input" id="password" name="password" required autocomplete="current-password">
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <span id="loginText">Sign In</span>
                    <div id="loginSpinner" class="loading-spinner" style="display: none;"></div>
                </button>
            </form>

            <div class="default-credentials">
                <strong>Default Admin Credentials:</strong><br>
                Username: admin<br>
                Password: admin123
            </div>
        </div>

        <script>
            document.getElementById('loginForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const loginBtn = document.getElementById('loginBtn');
                const loginText = document.getElementById('loginText');
                const loginSpinner = document.getElementById('loginSpinner');
                const errorDiv = document.getElementById('error-message');
                
                // Show loading state
                loginBtn.disabled = true;
                loginText.style.display = 'none';
                loginSpinner.style.display = 'block';
                errorDiv.innerHTML = '';
                
                const formData = new FormData(e.target);
                const credentials = {
                    username: formData.get('username'),
                    password: formData.get('password')
                };
                
                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(credentials)
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        // Successful login
                        window.location.href = '/';
                    } else {
                        // Show error
                        errorDiv.innerHTML = `
                            <div class="alert alert-error">
                                <i class="fas fa-exclamation-triangle"></i>
                                ${data.error || 'Login failed'}
                            </div>
                        `;
                    }
                } catch (error) {
                    errorDiv.innerHTML = `
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-triangle"></i>
                            Network error. Please try again.
                        </div>
                    `;
                }
                
                // Reset button state
                loginBtn.disabled = false;
                loginText.style.display = 'block';
                loginSpinner.style.display = 'none';
            });
            
            // Focus username field on load
            document.getElementById('username').focus();
        </script>
    </body>
    </html>
    '''
