global_settings:
  check_interval_seconds: 30
logging:
  log_file: logs/redundancy_app.log
  log_level: INFO
  log_to_console: true
  log_to_file: true
monitors:
- name: SystemDiskMonitor
  path: /
  threshold_percentage: 90
  type: disk
- expected_status: up
  interface: Ethernet
  name: PrimaryNetworkInterface
  type: network
- expected_status: 200
  name: GoogleHealthCheck
  timeout_seconds: 10
  type: http
  url: https://www.google.com
- expected_status: 200
  name: LocalhostCheck
  timeout_seconds: 5
  type: http
  url: http://localhost:5000
