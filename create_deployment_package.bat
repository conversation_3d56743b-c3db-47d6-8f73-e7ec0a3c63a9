@echo off
title SafeKit Redundancy System - Deployment Package Creator
color 0E

echo ================================================================
echo    SafeKit-Style Redundancy Management System
echo    Deployment Package Creator v1.0
echo ================================================================
echo.

set PACKAGE_NAME=SafeKit_Redundancy_System_v1.0
set PACKAGE_DIR=%PACKAGE_NAME%
set ZIP_FILE=%PACKAGE_NAME%.zip

echo Creating deployment package: %PACKAGE_NAME%
echo.

REM Clean up any existing package
if exist "%PACKAGE_DIR%" (
    echo Removing existing package directory...
    rmdir /s /q "%PACKAGE_DIR%"
)

if exist "%ZIP_FILE%" (
    echo Removing existing ZIP file...
    del "%ZIP_FILE%"
)

echo.
echo [1/5] Creating package directory structure...
echo.

mkdir "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%\logs"
mkdir "%PACKAGE_DIR%\config"
mkdir "%PACKAGE_DIR%\data"
mkdir "%PACKAGE_DIR%\backup"
mkdir "%PACKAGE_DIR%\docs"

echo Created directory structure.

echo.
echo [2/5] Copying application files...
echo.

REM Copy main application files
copy "multisite_web_interface.py" "%PACKAGE_DIR%\"
copy "redundancy_web_ui.py" "%PACKAGE_DIR%\"
copy "redundancy_manager.py" "%PACKAGE_DIR%\"
copy "multisite_manager.py" "%PACKAGE_DIR%\"

REM Copy installation files
copy "install.bat" "%PACKAGE_DIR%\"
copy "README_INSTALLATION.md" "%PACKAGE_DIR%\"

echo Copied application files.

echo.
echo [3/5] Creating startup and service files...
echo.

REM Create startup script
echo @echo off> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo title SafeKit Redundancy Management System>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo color 0B>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo echo ================================================================>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo echo    SafeKit-Style Redundancy Management System>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo echo    Starting Web Interface...>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo echo ================================================================>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo echo.>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo echo Web Interface: http://localhost:5002>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo echo SafeKit Console: http://localhost:5002/safekit-console>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo echo Multi-Site Dashboard: http://localhost:5002/sites>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo echo.>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo echo Press Ctrl+C to stop the system>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo echo.>> "%PACKAGE_DIR%\start_redundancy_system.bat"
echo python multisite_web_interface.py>> "%PACKAGE_DIR%\start_redundancy_system.bat"

REM Create quick start script
echo @echo off> "%PACKAGE_DIR%\quick_start.bat"
echo echo Starting SafeKit Redundancy System...>> "%PACKAGE_DIR%\quick_start.bat"
echo start http://localhost:5002>> "%PACKAGE_DIR%\quick_start.bat"
echo start start_redundancy_system.bat>> "%PACKAGE_DIR%\quick_start.bat"

REM Create stop script
echo @echo off> "%PACKAGE_DIR%\stop_system.bat"
echo echo Stopping SafeKit Redundancy System...>> "%PACKAGE_DIR%\stop_system.bat"
echo taskkill /f /im python.exe /fi "WINDOWTITLE eq SafeKit Redundancy Management System">> "%PACKAGE_DIR%\stop_system.bat"
echo echo System stopped.>> "%PACKAGE_DIR%\stop_system.bat"
echo pause>> "%PACKAGE_DIR%\stop_system.bat"

echo Created startup scripts.

echo.
echo [4/5] Creating documentation...
echo.

REM Create quick start guide
echo # SafeKit Redundancy System - Quick Start> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo.>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo 1. INSTALLATION:>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo    - Right-click install.bat and "Run as administrator">> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo    - Follow the installation prompts>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo.>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo 2. START SYSTEM:>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo    - Double-click: quick_start.bat>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo    - Or manually: start_redundancy_system.bat>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo.>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo 3. ACCESS WEB INTERFACE:>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo    - Main Dashboard: http://localhost:5002>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo    - SafeKit Console: http://localhost:5002/safekit-console>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo    - Site Management: http://localhost:5002/sites>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo.>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo 4. NETWORK ACCESS:>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo    - Replace localhost with server IP for remote access>> "%PACKAGE_DIR%\docs\QUICK_START.txt"
echo    - Example: http://*************:5002>> "%PACKAGE_DIR%\docs\QUICK_START.txt"

REM Create requirements file
echo flask==2.3.3> "%PACKAGE_DIR%\requirements.txt"
echo flask-cors==4.0.0>> "%PACKAGE_DIR%\requirements.txt"
echo psutil==5.9.5>> "%PACKAGE_DIR%\requirements.txt"
echo requests==2.31.0>> "%PACKAGE_DIR%\requirements.txt"
echo werkzeug==2.3.7>> "%PACKAGE_DIR%\requirements.txt"

echo Created documentation.

echo.
echo [5/5] Creating ZIP package...
echo.

REM Check if PowerShell is available for ZIP creation
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%' -DestinationPath '%ZIP_FILE%' -Force" 2>nul

if %errorlevel% equ 0 (
    echo ZIP package created successfully: %ZIP_FILE%
) else (
    echo Warning: Could not create ZIP file automatically.
    echo Please manually ZIP the folder: %PACKAGE_DIR%
)

echo.
echo ================================================================
echo    DEPLOYMENT PACKAGE CREATED SUCCESSFULLY!
echo ================================================================
echo.
echo Package Contents:
echo   - Application files (Python scripts)
echo   - Installation script (install.bat)
echo   - Startup scripts (start_redundancy_system.bat, quick_start.bat)
echo   - Documentation (README_INSTALLATION.md, QUICK_START.txt)
echo   - Requirements file (requirements.txt)
echo   - Directory structure (logs, config, data, backup)
echo.
echo Package Location:
echo   - Folder: %PACKAGE_DIR%\
echo   - ZIP File: %ZIP_FILE%
echo.
echo Ready for Distribution!
echo.
echo To deploy on target system:
echo   1. Copy ZIP file to target server
echo   2. Extract ZIP file
echo   3. Run install.bat as administrator
echo   4. Start system with quick_start.bat
echo.
pause
