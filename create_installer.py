#!/usr/bin/env python3
"""
EBO Redundancy System - Windows Application Installer Creator
Creates a professional Windows installer package with GUI setup
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import zipfile
import json

class EBORedundancyInstaller:
    """Creates a Windows installer for EBO Redundancy System"""
    
    def __init__(self):
        self.app_name = "EBO Redundancy Manager"
        self.app_version = "1.0.0"
        self.publisher = "Professional Redundancy Solutions"
        self.current_dir = Path(__file__).parent
        self.build_dir = self.current_dir / "installer_build"
        self.dist_dir = self.current_dir / "dist"
        
    def create_installer_structure(self):
        """Create installer directory structure"""
        print("📁 Creating installer structure...")
        
        # Clean and create build directory
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        self.build_dir.mkdir()
        
        # Create subdirectories
        subdirs = [
            'app',           # Application files
            'config',        # Configuration templates
            'templates',     # Web templates
            'static',        # Static files
            'scripts',       # Installation scripts
            'service',       # Windows service files
            'docs'           # Documentation
        ]
        
        for subdir in subdirs:
            (self.build_dir / subdir).mkdir()
            print(f"  ✅ Created: {subdir}")
    
    def copy_application_files(self):
        """Copy all application files to installer"""
        print("📋 Copying application files...")
        
        # Core application files
        app_files = [
            'redundancy_manager.py',
            'database_cluster_manager.py',
            'storage_redundancy_manager.py', 
            'ebo_redundancy_manager.py',
            'redundancy_web_ui.py',
            'base_monitor.py',
            'disk_monitor.py',
            'service_monitor.py',
            'http_monitor.py',
            'network_monitor.py',
            'port_monitor.py',
            'performance_monitor.py'
        ]
        
        for file_name in app_files:
            src_file = self.current_dir / file_name
            if src_file.exists():
                dst_file = self.build_dir / 'app' / file_name
                shutil.copy2(src_file, dst_file)
                print(f"  ✅ Copied: {file_name}")
        
        # Copy templates
        templates_dir = self.current_dir / 'templates'
        if templates_dir.exists():
            shutil.copytree(templates_dir, self.build_dir / 'templates', dirs_exist_ok=True)
            print(f"  ✅ Copied: templates")
        
        # Copy static files
        static_dir = self.current_dir / 'static'
        if static_dir.exists():
            shutil.copytree(static_dir, self.build_dir / 'static', dirs_exist_ok=True)
            print(f"  ✅ Copied: static files")
    
    def create_main_application(self):
        """Create main application entry point"""
        print("🚀 Creating main application...")
        
        main_app = '''#!/usr/bin/env python3
"""
EBO Redundancy Manager - Main Application
Professional Windows Application for EBO Redundancy Management
"""

import sys
import os
import threading
import time
import webbrowser
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess

# Add app directory to path
app_dir = Path(__file__).parent / 'app'
sys.path.insert(0, str(app_dir))

class EBORedundancyApp:
    """Main EBO Redundancy Manager Application"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("EBO Redundancy Manager")
        self.root.geometry("800x600")
        self.root.iconbitmap(default="app.ico") if os.path.exists("app.ico") else None
        
        self.service_running = False
        self.web_thread = None
        
        self.create_gui()
        
    def create_gui(self):
        """Create the main GUI interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="EBO Redundancy Manager", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="System Status", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="Service Stopped", 
                                     foreground="red", font=("Arial", 12, "bold"))
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # Control buttons frame
        control_frame = ttk.LabelFrame(main_frame, text="Service Control", padding="10")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_button = ttk.Button(control_frame, text="Start Service", 
                                      command=self.start_service, width=15)
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="Stop Service", 
                                     command=self.stop_service, width=15, state="disabled")
        self.stop_button.grid(row=0, column=1, padx=(0, 10))
        
        self.restart_button = ttk.Button(control_frame, text="Restart Service", 
                                        command=self.restart_service, width=15, state="disabled")
        self.restart_button.grid(row=0, column=2)
        
        # Web interface frame
        web_frame = ttk.LabelFrame(main_frame, text="Web Interface Access", padding="10")
        web_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(web_frame, text="Access the web interface at:").grid(row=0, column=0, sticky=tk.W)
        
        self.url_label = ttk.Label(web_frame, text="http://localhost:5001", 
                                  foreground="blue", cursor="hand2")
        self.url_label.grid(row=1, column=0, sticky=tk.W)
        self.url_label.bind("<Button-1>", self.open_web_interface)
        
        web_buttons_frame = ttk.Frame(web_frame)
        web_buttons_frame.grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        
        ttk.Button(web_buttons_frame, text="Open Dashboard", 
                  command=lambda: self.open_url("http://localhost:5001")).grid(row=0, column=0, padx=(0, 5))
        
        ttk.Button(web_buttons_frame, text="EBO Redundancy", 
                  command=lambda: self.open_url("http://localhost:5001/ebo-redundancy")).grid(row=0, column=1, padx=(0, 5))
        
        ttk.Button(web_buttons_frame, text="Applications", 
                  command=lambda: self.open_url("http://localhost:5001/applications")).grid(row=0, column=2, padx=(0, 5))
        
        ttk.Button(web_buttons_frame, text="Storage", 
                  command=lambda: self.open_url("http://localhost:5001/storage-redundancy")).grid(row=0, column=3)
        
        # Configuration frame
        config_frame = ttk.LabelFrame(main_frame, text="Configuration", padding="10")
        config_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(config_frame, text="Edit Configuration", 
                  command=self.edit_config, width=20).grid(row=0, column=0, padx=(0, 10))
        
        ttk.Button(config_frame, text="View Logs", 
                  command=self.view_logs, width=20).grid(row=0, column=1, padx=(0, 10))
        
        ttk.Button(config_frame, text="System Info", 
                  command=self.show_system_info, width=20).grid(row=0, column=2)
        
        # Log display frame
        log_frame = ttk.LabelFrame(main_frame, text="System Log", padding="10")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Log text widget with scrollbar
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.log_text = tk.Text(log_text_frame, height=10, width=80)
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        log_text_frame.columnconfigure(0, weight=1)
        log_text_frame.rowconfigure(0, weight=1)
        
        # Status bar
        self.status_bar = ttk.Label(self.root, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # Start status update thread
        self.update_status()
        
    def start_service(self):
        """Start the redundancy service"""
        try:
            self.log_message("Starting EBO Redundancy Service...")
            
            # Import and start the web service
            from redundancy_web_ui import app
            from ebo_redundancy_manager import EBORedundancyManager
            
            # Start web service in separate thread
            self.web_thread = threading.Thread(target=self.run_web_service, daemon=True)
            self.web_thread.start()
            
            self.service_running = True
            self.update_button_states()
            self.log_message("✅ EBO Redundancy Service started successfully")
            self.status_bar.config(text="Service Running - Web Interface: http://localhost:5001")
            
        except Exception as e:
            self.log_message(f"❌ Failed to start service: {e}")
            messagebox.showerror("Error", f"Failed to start service: {e}")
    
    def stop_service(self):
        """Stop the redundancy service"""
        try:
            self.log_message("Stopping EBO Redundancy Service...")
            self.service_running = False
            self.update_button_states()
            self.log_message("✅ EBO Redundancy Service stopped")
            self.status_bar.config(text="Service Stopped")
            
        except Exception as e:
            self.log_message(f"❌ Failed to stop service: {e}")
    
    def restart_service(self):
        """Restart the redundancy service"""
        self.stop_service()
        time.sleep(2)
        self.start_service()
    
    def run_web_service(self):
        """Run the web service"""
        try:
            from redundancy_web_ui import app
            app.run(host='0.0.0.0', port=5001, debug=False, use_reloader=False)
        except Exception as e:
            self.log_message(f"❌ Web service error: {e}")
    
    def update_button_states(self):
        """Update button states based on service status"""
        if self.service_running:
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
            self.restart_button.config(state="normal")
            self.status_label.config(text="Service Running", foreground="green")
        else:
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.restart_button.config(state="disabled")
            self.status_label.config(text="Service Stopped", foreground="red")
    
    def open_web_interface(self, event=None):
        """Open the main web interface"""
        self.open_url("http://localhost:5001")
    
    def open_url(self, url):
        """Open URL in default browser"""
        if self.service_running:
            webbrowser.open(url)
        else:
            messagebox.showwarning("Service Not Running", 
                                 "Please start the service first before accessing the web interface.")
    
    def edit_config(self):
        """Open configuration editor"""
        config_file = Path("config/ebo_redundancy_config.yaml")
        if config_file.exists():
            os.startfile(str(config_file))
        else:
            messagebox.showinfo("Configuration", 
                              "Configuration file not found. It will be created when the service starts.")
    
    def view_logs(self):
        """Open log directory"""
        log_dir = Path("logs")
        if log_dir.exists():
            os.startfile(str(log_dir))
        else:
            messagebox.showinfo("Logs", "Log directory not found. Logs will be created when the service starts.")
    
    def show_system_info(self):
        """Show system information"""
        info = f"""EBO Redundancy Manager v{self.app_version}
        
Installation Directory: {Path(__file__).parent}
Configuration: config/ebo_redundancy_config.yaml
Logs: logs/
Web Interface: http://localhost:5001

System Status:
- Service Running: {'Yes' if self.service_running else 'No'}
- Python Version: {sys.version.split()[0]}
- Platform: {sys.platform}
"""
        messagebox.showinfo("System Information", info)
    
    def log_message(self, message):
        """Add message to log display"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Keep only last 100 lines
        lines = self.log_text.get("1.0", tk.END).split("\\n")
        if len(lines) > 100:
            self.log_text.delete("1.0", f"{len(lines)-100}.0")
    
    def update_status(self):
        """Update status periodically"""
        # This could check actual service status
        self.root.after(5000, self.update_status)  # Update every 5 seconds
    
    def run(self):
        """Run the application"""
        self.log_message("EBO Redundancy Manager started")
        self.log_message("Click 'Start Service' to begin monitoring")
        self.root.mainloop()

if __name__ == '__main__':
    app = EBORedundancyApp()
    app.run()
'''
        
        main_file = self.build_dir / 'EBO_Redundancy_Manager.py'
        with open(main_file, 'w') as f:
            f.write(main_app)
        
        print(f"  ✅ Created: {main_file}")
    
    def create_installer_script(self):
        """Create NSIS installer script"""
        print("📦 Creating installer script...")
        
        nsis_script = f'''
; EBO Redundancy Manager Installer
; Professional Windows Installer

!define APPNAME "EBO Redundancy Manager"
!define COMPANYNAME "Professional Redundancy Solutions"
!define DESCRIPTION "Enterprise EBO Redundancy Management System"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0
!define HELPURL "https://github.com/your-repo/ebo-redundancy"
!define UPDATEURL "https://github.com/your-repo/ebo-redundancy/releases"
!define ABOUTURL "https://github.com/your-repo/ebo-redundancy"
!define INSTALLSIZE 50000

RequestExecutionLevel admin
InstallDir "$PROGRAMFILES\\${{APPNAME}}"
LicenseData "license.txt"
Name "${{APPNAME}}"
Icon "app.ico"
outFile "EBO_Redundancy_Manager_Setup.exe"

!include LogicLib.nsh

page license
page directory
page instfiles

!macro VerifyUserIsAdmin
UserInfo::GetAccountType
pop $0
${{If}} $0 != "admin"
    messageBox mb_iconstop "Administrator rights required!"
    setErrorLevel 740
    quit
${{EndIf}}
!macroend

function .onInit
    setShellVarContext all
    !insertmacro VerifyUserIsAdmin
functionEnd

section "install"
    setOutPath $INSTDIR
    
    ; Copy application files
    file /r "app\\*.*"
    file /r "config\\*.*"
    file /r "templates\\*.*"
    file /r "static\\*.*"
    file /r "scripts\\*.*"
    file /r "service\\*.*"
    file /r "docs\\*.*"
    file "EBO_Redundancy_Manager.py"
    file "requirements.txt"
    
    ; Create directories
    createDirectory "$INSTDIR\\logs"
    createDirectory "$INSTDIR\\data"
    createDirectory "$INSTDIR\\backup"
    
    ; Install Python dependencies
    nsExec::ExecToLog 'pip install -r "$INSTDIR\\requirements.txt"'
    
    ; Create shortcuts
    createDirectory "$SMPROGRAMS\\${{APPNAME}}"
    createShortCut "$SMPROGRAMS\\${{APPNAME}}\\${{APPNAME}}.lnk" "python.exe" '"$INSTDIR\\EBO_Redundancy_Manager.py"' "$INSTDIR\\app.ico"
    createShortCut "$SMPROGRAMS\\${{APPNAME}}\\Web Interface.lnk" "http://localhost:5001"
    createShortCut "$SMPROGRAMS\\${{APPNAME}}\\Configuration.lnk" "$INSTDIR\\config"
    createShortCut "$SMPROGRAMS\\${{APPNAME}}\\Logs.lnk" "$INSTDIR\\logs"
    createShortCut "$SMPROGRAMS\\${{APPNAME}}\\Uninstall.lnk" "$INSTDIR\\uninstall.exe"
    
    ; Desktop shortcut
    createShortCut "$DESKTOP\\${{APPNAME}}.lnk" "python.exe" '"$INSTDIR\\EBO_Redundancy_Manager.py"' "$INSTDIR\\app.ico"
    
    ; Registry entries
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "DisplayName" "${{APPNAME}}"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "UninstallString" "$INSTDIR\\uninstall.exe"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "InstallLocation" "$INSTDIR"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "DisplayIcon" "$INSTDIR\\app.ico"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "Publisher" "${{COMPANYNAME}}"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "HelpLink" "${{HELPURL}}"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "URLUpdateInfo" "${{UPDATEURL}}"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "URLInfoAbout" "${{ABOUTURL}}"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "DisplayVersion" "${{VERSIONMAJOR}}.${{VERSIONMINOR}}.${{VERSIONBUILD}}"
    writeRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "VersionMajor" ${{VERSIONMAJOR}}
    writeRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "VersionMinor" ${{VERSIONMINOR}}
    writeRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "NoModify" 1
    writeRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "NoRepair" 1
    writeRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}" "EstimatedSize" ${{INSTALLSIZE}}
    
    ; Create uninstaller
    writeUninstaller "$INSTDIR\\uninstall.exe"
    
    ; Success message
    messageBox MB_OK "EBO Redundancy Manager has been installed successfully!$\\r$\\n$\\r$\\nYou can now start the application from the Start Menu or Desktop shortcut."
sectionEnd

section "uninstall"
    ; Remove files
    rmDir /r "$INSTDIR"
    
    ; Remove shortcuts
    rmDir /r "$SMPROGRAMS\\${{APPNAME}}"
    delete "$DESKTOP\\${{APPNAME}}.lnk"
    
    ; Remove registry entries
    deleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{APPNAME}}"
sectionEnd
'''
        
        nsis_file = self.build_dir / 'installer.nsi'
        with open(nsis_file, 'w') as f:
            f.write(nsis_script)
        
        print(f"  ✅ Created: {nsis_file}")
    
    def create_requirements_file(self):
        """Create requirements.txt for dependencies"""
        requirements = '''flask>=2.0.0
PyYAML>=6.0
psutil>=5.8.0
requests>=2.25.0
'''
        
        req_file = self.build_dir / 'requirements.txt'
        with open(req_file, 'w') as f:
            f.write(requirements)
        
        print(f"  ✅ Created: {req_file}")
    
    def create_license_file(self):
        """Create license file"""
        license_text = '''EBO Redundancy Manager License Agreement

Copyright (c) 2025 Professional Redundancy Solutions

Permission is hereby granted to use this software for managing EcoStruxure Building Operation redundancy systems.

This software is provided "as is", without warranty of any kind, express or implied, including but not limited to the warranties of merchantability, fitness for a particular purpose and noninfringement.

For support and updates, please visit: https://github.com/your-repo/ebo-redundancy
'''
        
        license_file = self.build_dir / 'license.txt'
        with open(license_file, 'w') as f:
            f.write(license_text)
        
        print(f"  ✅ Created: {license_file}")
    
    def create_portable_version(self):
        """Create portable ZIP version"""
        print("📦 Creating portable version...")
        
        if not self.dist_dir.exists():
            self.dist_dir.mkdir()
        
        # Create portable ZIP
        zip_file = self.dist_dir / f"EBO_Redundancy_Manager_v{self.app_version}_Portable.zip"
        
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            for root, dirs, files in os.walk(self.build_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_path = file_path.relative_to(self.build_dir)
                    zf.write(file_path, arc_path)
        
        print(f"  ✅ Created portable version: {zip_file}")
        
        # Create portable launcher
        launcher_script = '''@echo off
echo Starting EBO Redundancy Manager (Portable)...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Install dependencies if needed
echo Installing/updating dependencies...
pip install -r requirements.txt >nul 2>&1

REM Start the application
echo Starting EBO Redundancy Manager...
python EBO_Redundancy_Manager.py

pause
'''
        
        launcher_file = self.dist_dir / "Start_EBO_Redundancy_Manager.bat"
        with open(launcher_file, 'w') as f:
            f.write(launcher_script)
        
        print(f"  ✅ Created portable launcher: {launcher_file}")
    
    def build_installer(self):
        """Build the complete installer package"""
        print("🚀 Building EBO Redundancy Manager Installer")
        print("=" * 60)
        
        try:
            # Create installer structure
            self.create_installer_structure()
            
            # Copy application files
            self.copy_application_files()
            
            # Create main application
            self.create_main_application()
            
            # Create installer script
            self.create_installer_script()
            
            # Create requirements file
            self.create_requirements_file()
            
            # Create license file
            self.create_license_file()
            
            # Create portable version
            self.create_portable_version()
            
            print("\n🎉 Installer package created successfully!")
            print("=" * 60)
            print(f"📁 Build Directory: {self.build_dir}")
            print(f"📦 Distribution: {self.dist_dir}")
            print("\n📋 Available Packages:")
            print(f"  • Portable Version: {self.dist_dir}/EBO_Redundancy_Manager_v{self.app_version}_Portable.zip")
            print(f"  • Portable Launcher: {self.dist_dir}/Start_EBO_Redundancy_Manager.bat")
            print(f"  • NSIS Installer Script: {self.build_dir}/installer.nsi")
            
            print("\n📝 Next Steps:")
            print("1. For Portable Use:")
            print("   - Extract the ZIP file")
            print("   - Run Start_EBO_Redundancy_Manager.bat")
            print("2. For Windows Installer:")
            print("   - Install NSIS (Nullsoft Scriptable Install System)")
            print("   - Compile installer.nsi to create EBO_Redundancy_Manager_Setup.exe")
            print("3. For Direct Use:")
            print("   - Run python EBO_Redundancy_Manager.py from the build directory")
            
            return True
            
        except Exception as e:
            print(f"❌ Build failed: {e}")
            return False

if __name__ == '__main__':
    installer = EBORedundancyInstaller()
    installer.build_installer()
