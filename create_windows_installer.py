#!/usr/bin/env python3
"""
SafeKit Redundancy Management System - Windows Installer Creator
Creates a complete Windows installer package with embedded Python
"""

import os
import shutil
import zipfile
import urllib.request
import subprocess
import sys
from pathlib import Path

class SafeKitInstaller:
    def __init__(self):
        self.installer_dir = "SafeKit_Windows_Installer"
        self.app_dir = os.path.join(self.installer_dir, "app")
        self.python_dir = os.path.join(self.installer_dir, "python")
        self.scripts_dir = os.path.join(self.installer_dir, "scripts")

    def create_directories(self):
        """Create installer directory structure"""
        print("📁 Creating installer directories...")

        # Remove existing installer if it exists
        if os.path.exists(self.installer_dir):
            shutil.rmtree(self.installer_dir)

        # Create directory structure
        os.makedirs(self.app_dir, exist_ok=True)
        os.makedirs(self.python_dir, exist_ok=True)
        os.makedirs(self.scripts_dir, exist_ok=True)
        os.makedirs(os.path.join(self.installer_dir, "docs"), exist_ok=True)

        print("✅ Directories created")

    def copy_application_files(self):
        """Copy all application files"""
        print("📋 Copying application files...")

        app_files = [
            "multisite_web_interface.py",
            "redundancy_manager.py",
            "redundancy_web_ui.py",
            "requirements.txt"
        ]

        for file in app_files:
            if os.path.exists(file):
                shutil.copy2(file, self.app_dir)
                print(f"  ✅ Copied {file}")
            else:
                print(f"  ⚠️  Missing {file}")

        # Copy template and static directories if they exist
        for dir_name in ["templates", "static"]:
            if os.path.exists(dir_name):
                shutil.copytree(dir_name, os.path.join(self.app_dir, dir_name), dirs_exist_ok=True)
                print(f"  ✅ Copied {dir_name}/ directory")

        print("✅ Application files copied")

    def download_python_embeddable(self):
        """Download Python embeddable package"""
        print("🐍 Downloading Python embeddable package...")

        python_url = "https://www.python.org/ftp/python/3.11.7/python-3.11.7-embed-amd64.zip"
        python_zip = os.path.join(self.installer_dir, "python-embed.zip")

        try:
            print("  📥 Downloading Python 3.11.7 embeddable...")
            urllib.request.urlretrieve(python_url, python_zip)

            print("  📦 Extracting Python...")
            with zipfile.ZipFile(python_zip, 'r') as zip_ref:
                zip_ref.extractall(self.python_dir)

            # Remove the zip file
            os.remove(python_zip)

            print("✅ Python embeddable package ready")
            return True

        except Exception as e:
            print(f"❌ Failed to download Python: {e}")
            print("  💡 Installer will include instructions to install Python manually")
            return False

    def create_installer_script(self):
        """Create the main installer script"""
        print("📝 Creating installer script...")

        installer_script = f"""@echo off
title SafeKit Redundancy Management System - Professional Installer
color 0A

echo ================================================================
echo    SafeKit-Style Redundancy Management System
echo    Professional Windows Installer v2.0
echo ================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: This installer must be run as Administrator
    echo.
    echo Right-click on INSTALL.bat and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [1/6] Checking system requirements...
echo.

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo ✅ Windows version: %VERSION%

echo.
echo [2/6] Setting up Python environment...
echo.

REM Check if we have embedded Python
if exist "python\\python.exe" (
    echo ✅ Using embedded Python
    set PYTHON_CMD=python\\python.exe
    set PIP_CMD=python\\python.exe -m pip
) else (
    echo 🔍 Checking system Python...
    python --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Python not found
        echo.
        echo Please install Python 3.8+ from: https://python.org/downloads/
        echo Make sure to check "Add Python to PATH" during installation
        echo Then run this installer again.
        echo.
        pause
        exit /b 1
    )
    set PYTHON_CMD=python
    set PIP_CMD=python -m pip
    echo ✅ Using system Python
)

echo.
echo [3/6] Installing Python dependencies...
echo.

%PIP_CMD% install --upgrade pip --quiet
%PIP_CMD% install flask==2.3.3 --quiet
%PIP_CMD% install flask-cors==4.0.0 --quiet
%PIP_CMD% install psutil==5.9.5 --quiet
%PIP_CMD% install requests==2.31.0 --quiet
%PIP_CMD% install werkzeug==2.3.7 --quiet

if %errorlevel% neq 0 (
    echo ❌ Failed to install some dependencies
    echo Trying alternative installation method...
    %PIP_CMD% install flask flask-cors psutil requests werkzeug --quiet
)

echo ✅ Dependencies installed

echo.
echo [4/6] Creating application directories...
echo.

if not exist "app\\logs" mkdir app\\logs
if not exist "app\\config" mkdir app\\config
if not exist "app\\data" mkdir app\\data
if not exist "app\\backup" mkdir app\\backup

echo ✅ Application directories created

echo.
echo [5/6] Creating startup scripts...
echo.

REM Create startup script
echo @echo off> START_SAFEKIT.bat
echo title SafeKit Redundancy Management System>> START_SAFEKIT.bat
echo color 0B>> START_SAFEKIT.bat
echo echo ================================================================>> START_SAFEKIT.bat
echo echo    SafeKit-Style Redundancy Management System>> START_SAFEKIT.bat
echo echo    Professional Redundancy Management>> START_SAFEKIT.bat
echo echo ================================================================>> START_SAFEKIT.bat
echo echo.>> START_SAFEKIT.bat
echo echo 🌐 Web Interface: http://localhost:5002>> START_SAFEKIT.bat
echo echo 🔧 SafeKit Console: http://localhost:5002/safekit-console>> START_SAFEKIT.bat
echo echo 📊 Multi-Site Dashboard: http://localhost:5002/sites>> START_SAFEKIT.bat
echo echo.>> START_SAFEKIT.bat
echo echo Press Ctrl+C to stop the system>> START_SAFEKIT.bat
echo echo.>> START_SAFEKIT.bat
if exist "python\\python.exe" (
    echo cd app>> START_SAFEKIT.bat
    echo ..\\python\\python.exe multisite_web_interface.py>> START_SAFEKIT.bat
) else (
    echo cd app>> START_SAFEKIT.bat
    echo python multisite_web_interface.py>> START_SAFEKIT.bat
)

REM Create quick start with browser
echo @echo off> QUICK_START.bat
echo echo Starting SafeKit Redundancy System...>> QUICK_START.bat
echo timeout /t 3 /nobreak ^>nul>> QUICK_START.bat
echo start http://localhost:5002/safekit-console>> QUICK_START.bat
echo call START_SAFEKIT.bat>> QUICK_START.bat

REM Create desktop shortcut script
echo @echo off> CREATE_DESKTOP_SHORTCUT.bat
echo echo Creating desktop shortcut...>> CREATE_DESKTOP_SHORTCUT.bat
echo set SCRIPT="%TEMP%\\%RANDOM%-%RANDOM%-%RANDOM%-%RANDOM%.vbs">> CREATE_DESKTOP_SHORTCUT.bat
echo echo Set oWS = WScript.CreateObject("WScript.Shell"^) ^> %SCRIPT%>> CREATE_DESKTOP_SHORTCUT.bat
echo echo sLinkFile = "%USERPROFILE%\\Desktop\\SafeKit Redundancy System.lnk" ^>^> %SCRIPT%>> CREATE_DESKTOP_SHORTCUT.bat
echo echo Set oLink = oWS.CreateShortcut(sLinkFile^) ^>^> %SCRIPT%>> CREATE_DESKTOP_SHORTCUT.bat
echo echo oLink.TargetPath = "%CD%\\QUICK_START.bat" ^>^> %SCRIPT%>> CREATE_DESKTOP_SHORTCUT.bat
echo echo oLink.WorkingDirectory = "%CD%" ^>^> %SCRIPT%>> CREATE_DESKTOP_SHORTCUT.bat
echo echo oLink.Description = "SafeKit Redundancy Management System" ^>^> %SCRIPT%>> CREATE_DESKTOP_SHORTCUT.bat
echo echo oLink.Save ^>^> %SCRIPT%>> CREATE_DESKTOP_SHORTCUT.bat
echo cscript /nologo %SCRIPT%>> CREATE_DESKTOP_SHORTCUT.bat
echo del %SCRIPT%>> CREATE_DESKTOP_SHORTCUT.bat
echo echo ✅ Desktop shortcut created>> CREATE_DESKTOP_SHORTCUT.bat

echo ✅ Startup scripts created

echo.
echo [6/6] Testing installation...
echo.

echo Testing Python and dependencies...
%PYTHON_CMD% -c "import flask, flask_cors, psutil, requests; print('✅ All dependencies working')" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  Some dependencies may be missing, but installation completed
) else (
    echo ✅ All dependencies verified
)

echo.
echo ================================================================
echo    INSTALLATION COMPLETED SUCCESSFULLY!
echo ================================================================
echo.
echo 🚀 TO START THE SYSTEM:
echo.
echo   Option 1: Double-click QUICK_START.bat (Recommended)
echo   Option 2: Double-click START_SAFEKIT.bat
echo   Option 3: Run CREATE_DESKTOP_SHORTCUT.bat for desktop icon
echo.
echo 🌐 ACCESS WEB INTERFACE:
echo.
echo   Main Dashboard: http://localhost:5002
echo   SafeKit Console: http://localhost:5002/safekit-console
echo   Site Management: http://localhost:5002/sites
echo   Add New Site: http://localhost:5002/add-site
echo.
echo 🔧 FOR NETWORK ACCESS:
echo.
echo   Replace 'localhost' with this computer's IP address
echo   Example: http://*************:5002/safekit-console
echo   Ensure Windows Firewall allows port 5002
echo.
echo ================================================================
echo    READY FOR PRODUCTION USE!
echo ================================================================
echo.
echo The system provides enterprise-grade redundancy management
echo similar to Microsoft Failover Cluster Manager, specifically
echo designed for EcoStruxure Building Operation environments.
echo.
pause
"""

        with open(os.path.join(self.installer_dir, "INSTALL.bat"), 'w') as f:
            f.write(installer_script)

        print("✅ Installer script created")

    def create_documentation(self):
        """Create comprehensive documentation"""
        print("📚 Creating documentation...")

        # Create user guide
        user_guide = """# SafeKit Redundancy Management System - User Guide

## 🎯 Quick Start

1. **Run INSTALL.bat as Administrator**
2. **Double-click QUICK_START.bat**
3. **Access SafeKit Console**: http://localhost:5002/safekit-console

## 🔧 EBO Configuration

### Add EBO Servers
1. Go to **Nodes** tab
2. Click **Add Node**
3. Configure:
   - Node Name: EBO-PRIMARY
   - IP Address: [Your server IP]
   - Role: Primary
   - Priority: 1000

### Setup Data Replication
1. Go to **Replication** tab
2. Click **Add Directory**
3. Configure:
   - Path: C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation\\Data
   - Mode: Synchronous (Real-time)
   - Priority: High

## 🌐 Network Access

Replace 'localhost' with server IP for remote access:
- Example: http://*************:5002/safekit-console

## 🛠 Troubleshooting

- **Port 5002 in use**: Restart computer or change port
- **Can't access remotely**: Check Windows Firewall
- **Python errors**: Re-run INSTALL.bat as Administrator

## 📞 Support

Complete enterprise-grade redundancy management for building automation systems.
"""

        with open(os.path.join(self.installer_dir, "docs", "USER_GUIDE.md"), 'w') as f:
            f.write(user_guide)

        # Create README
        readme = """# SafeKit Redundancy Management System

## Professional Windows Installation Package

This package provides a complete, self-contained installation of the SafeKit-style
redundancy management system for EcoStruxure Building Operation environments.

## Installation

1. **Right-click INSTALL.bat**
2. **Select "Run as administrator"**
3. **Follow the installation prompts**
4. **Start with QUICK_START.bat**

## Features

- Microsoft Failover Cluster-style web interface
- Real-time redundancy monitoring
- Multi-site management
- EBO-specific configuration
- Professional enterprise features

## System Requirements

- Windows 10/11 or Windows Server 2016+
- 4 GB RAM minimum
- 2 GB free disk space
- Administrator privileges for installation

## Ready for Production Use

This system provides enterprise-grade high availability management
specifically designed for building automation environments.
"""

        with open(os.path.join(self.installer_dir, "README.txt"), 'w') as f:
            f.write(readme)

        print("✅ Documentation created")

    def create_final_package(self):
        """Create the final installer package"""
        print("📦 Creating final installer package...")

        # Create a ZIP file of the entire installer
        zip_filename = "SafeKit_Professional_Windows_Installer.zip"

        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(self.installer_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, self.installer_dir)
                    zipf.write(file_path, arc_name)

        print(f"✅ Created {zip_filename}")

        # Create a simple extractor script
        extractor_script = f"""@echo off
title SafeKit Professional Installer
color 0A

echo ================================================================
echo    SafeKit Redundancy Management System
echo    Professional Windows Installer Package
echo ================================================================
echo.

echo Extracting installer files...
powershell -command "Expand-Archive -Path 'SafeKit_Professional_Windows_Installer.zip' -DestinationPath '.' -Force"

echo.
echo ✅ Files extracted successfully!
echo.
echo Next steps:
echo 1. Navigate to SafeKit_Windows_Installer folder
echo 2. Right-click INSTALL.bat and "Run as administrator"
echo 3. Follow the installation prompts
echo.
pause

cd SafeKit_Windows_Installer
echo.
echo Opening installer folder...
explorer .
"""

        with open("EXTRACT_AND_INSTALL.bat", 'w') as f:
            f.write(extractor_script)

        print("✅ Extractor script created")

    def build_installer(self):
        """Build the complete installer package"""
        print("🚀 Building SafeKit Professional Windows Installer...")
        print("=" * 60)

        try:
            self.create_directories()
            self.copy_application_files()

            # Try to download Python, but continue if it fails
            python_downloaded = self.download_python_embeddable()

            self.create_installer_script()
            self.create_documentation()
            self.create_final_package()

            print("=" * 60)
            print("🎉 INSTALLER PACKAGE CREATED SUCCESSFULLY!")
            print("=" * 60)
            print()
            print("📦 Package Files Created:")
            print("  ✅ SafeKit_Professional_Windows_Installer.zip")
            print("  ✅ EXTRACT_AND_INSTALL.bat")
            print()
            print("🚀 To Deploy:")
            print("  1. Send both files to target computer")
            print("  2. Run EXTRACT_AND_INSTALL.bat")
            print("  3. Follow the installation prompts")
            print()
            if python_downloaded:
                print("✅ Includes embedded Python - no manual Python installation needed")
            else:
                print("⚠️  Python will need to be installed manually on target system")
            print()
            print("🎯 Result: Complete professional redundancy management system")
            print("   ready for EcoStruxure Building Operation environments!")

        except Exception as e:
            print(f"❌ Error building installer: {e}")
            return False

        return True

if __name__ == "__main__":
    installer = SafeKitInstaller()
    success = installer.build_installer()

    if success:
        print("\n🎉 Ready for distribution!")
    else:
        print("\n❌ Build failed!")
        sys.exit(1)
