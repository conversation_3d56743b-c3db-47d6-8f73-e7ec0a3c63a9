clusters:
  primary_database_cluster:
    auto_failover: true
    database_type: postgresql
    description: Primary Application Database Cluster
    failover_threshold: 3
    heartbeat_interval: 5
    nodes:
    - connection_string: *************************************************/myapp
      database_type: postgresql
      host: *************
      is_active: true
      name: db-primary
      password: your_password
      port: 5432
      role: primary
      username: postgres
    - connection_string: *************************************************/myapp
      database_type: postgresql
      host: *************
      is_active: true
      name: db-secondary
      password: your_password
      port: 5432
      role: secondary
      username: postgres
    - connection_string: *************************************************/witness
      database_type: postgresql
      host: *************
      is_active: true
      name: witness1
      password: your_password
      port: 5432
      role: witness
      username: postgres
    quorum_nodes: 2
    replication_mode: synchronous
    virtual_ip: *************
    witness_node: witness1
  secondary_database_cluster:
    auto_failover: true
    database_type: mysql
    description: Secondary Application Database Cluster
    failover_threshold: 3
    heartbeat_interval: 5
    nodes:
    - connection_string: mysql://username:password@*************:3306/myapp
      database_type: mysql
      host: *************
      is_active: true
      name: mysql-primary
      password: your_password
      port: 3306
      role: primary
      username: root
    - connection_string: mysql://username:password@*************:3306/myapp
      database_type: mysql
      host: *************
      is_active: true
      name: mysql-secondary
      password: your_password
      port: 3306
      role: secondary
      username: root
    quorum_nodes: 2
    replication_mode: asynchronous
    virtual_ip: *************
