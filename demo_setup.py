#!/usr/bin/env python3
"""
Demo Setup Script for Hardware Redundancy System
This script sets up a complete demo configuration for testing the system
"""

import yaml
import os
import psutil
import sys

def get_available_interfaces():
    """Get available network interfaces"""
    interfaces = list(psutil.net_if_addrs().keys())
    return interfaces

def get_available_disks():
    """Get available disk partitions"""
    partitions = []
    for partition in psutil.disk_partitions():
        try:
            usage = psutil.disk_usage(partition.mountpoint)
            partitions.append({
                'mountpoint': partition.mountpoint,
                'device': partition.device,
                'fstype': partition.fstype,
                'usage_percent': usage.percent
            })
        except:
            pass
    return partitions

def create_demo_config():
    """Create a comprehensive demo configuration"""
    
    print("🔧 Creating demo configuration...")
    
    # Get system information
    interfaces = get_available_interfaces()
    partitions = get_available_disks()
    
    # Select best interface (prefer Ethernet, then Wi-Fi, then first available)
    selected_interface = "eth0"  # default
    if "Ethernet" in interfaces:
        selected_interface = "Ethernet"
    elif "Wi-Fi" in interfaces:
        selected_interface = "Wi-Fi"
    elif "en0" in interfaces:
        selected_interface = "en0"
    elif interfaces:
        selected_interface = interfaces[0]
    
    # Select disk with reasonable usage (not too full)
    selected_disk = "/"  # default
    if partitions:
        # Find a disk that's not too full for monitoring
        for partition in partitions:
            if partition['usage_percent'] < 95:  # Not too full
                selected_disk = partition['mountpoint']
                break
    
    # Create comprehensive configuration
    demo_config = {
        'global_settings': {
            'check_interval_seconds': 30
        },
        'logging': {
            'log_file': 'logs/redundancy_app.log',
            'log_level': 'INFO',
            'log_to_console': True,
            'log_to_file': True
        },
        'monitors': [
            {
                'name': 'SystemDiskMonitor',
                'type': 'disk',
                'path': selected_disk,
                'threshold_percentage': 90
            },
            {
                'name': 'PrimaryNetworkInterface',
                'type': 'network',
                'interface': selected_interface,
                'expected_status': 'up'
            },
            {
                'name': 'GoogleHealthCheck',
                'type': 'http',
                'url': 'https://www.google.com',
                'expected_status': 200,
                'timeout_seconds': 10
            },
            {
                'name': 'LocalhostCheck',
                'type': 'http',
                'url': 'http://localhost:5000',
                'expected_status': 200,
                'timeout_seconds': 5
            }
        ]
    }
    
    # Save configuration
    with open('config.yaml', 'w') as f:
        yaml.dump(demo_config, f, default_flow_style=False, indent=2)
    
    print("✅ Demo configuration created successfully!")
    print(f"   📁 Monitoring disk: {selected_disk}")
    print(f"   🌐 Monitoring interface: {selected_interface}")
    print(f"   🔗 Monitoring HTTP endpoints: Google + Localhost")
    
    return demo_config

def display_system_info():
    """Display current system information"""
    print("\n📊 System Information:")
    print("=" * 50)
    
    # Network interfaces
    interfaces = get_available_interfaces()
    print(f"🌐 Network Interfaces ({len(interfaces)}):")
    for i, interface in enumerate(interfaces, 1):
        print(f"   {i}. {interface}")
    
    # Disk partitions
    partitions = get_available_disks()
    print(f"\n💾 Disk Partitions ({len(partitions)}):")
    for i, partition in enumerate(partitions, 1):
        print(f"   {i}. {partition['mountpoint']} ({partition['device']}) - {partition['usage_percent']:.1f}% used")
    
    # System stats
    print(f"\n🖥️  System Stats:")
    print(f"   CPU Cores: {psutil.cpu_count()}")
    memory = psutil.virtual_memory()
    print(f"   Memory: {memory.percent}% used ({memory.used // (1024**3)}GB / {memory.total // (1024**3)}GB)")

def create_sample_monitors():
    """Create additional sample monitor configurations"""
    
    samples = {
        'high_availability_web_service': {
            'name': 'WebServiceHA',
            'type': 'http',
            'url': 'https://httpbin.org/status/200',
            'expected_status': 200,
            'timeout_seconds': 15
        },
        'backup_disk_monitor': {
            'name': 'BackupDiskMonitor',
            'type': 'disk',
            'path': '/' if os.name != 'nt' else 'C:\\',
            'threshold_percentage': 85
        },
        'secondary_network': {
            'name': 'SecondaryNetwork',
            'type': 'network',
            'interface': 'lo' if os.name != 'nt' else 'Loopback Pseudo-Interface 1',
            'expected_status': 'up'
        }
    }
    
    print("\n📋 Sample Monitor Configurations:")
    print("=" * 50)
    
    for name, config in samples.items():
        print(f"\n{config['name']} ({config['type'].upper()}):")
        for key, value in config.items():
            if key != 'name':
                print(f"   {key}: {value}")

def main():
    """Main demo setup function"""
    
    print("🎯 Hardware Redundancy System - Demo Setup")
    print("=" * 60)
    print("This script will set up a complete demo configuration")
    print("for testing your hardware redundancy system.\n")
    
    # Display system information
    display_system_info()
    
    # Ask user if they want to proceed
    print("\n" + "=" * 60)
    response = input("📝 Create demo configuration? (y/N): ").lower().strip()
    
    if response in ['y', 'yes']:
        # Create demo configuration
        config = create_demo_config()
        
        # Show sample configurations
        create_sample_monitors()
        
        print("\n" + "=" * 60)
        print("🚀 Demo Setup Complete!")
        print("\nNext Steps:")
        print("1. Start the web UI: python start_web_ui.py")
        print("2. Open browser: http://localhost:5000")
        print("3. View dashboard to see monitor status")
        print("4. Go to configuration page to add/edit monitors")
        print("5. Start monitoring from the dashboard")
        
        print("\n📁 Files Created:")
        print("   ✅ config.yaml - Main configuration file")
        print("   ✅ logs/ - Directory for log files")
        
        print("\n🔧 Configuration Summary:")
        print(f"   📊 Total Monitors: {len(config['monitors'])}")
        print(f"   ⏱️  Check Interval: {config['global_settings']['check_interval_seconds']} seconds")
        print(f"   📝 Log Level: {config['logging']['log_level']}")
        
        # Test configuration
        print("\n🧪 Testing Configuration...")
        try:
            from src.redundancy.config.config_loader import load_config
            test_config = load_config('config.yaml')
            if test_config:
                print("   ✅ Configuration file is valid")
            else:
                print("   ❌ Configuration file has issues")
        except Exception as e:
            print(f"   ⚠️  Warning: {e}")
        
    else:
        print("❌ Demo setup cancelled.")
        print("\nYou can run this script again anytime to create the demo configuration.")
    
    print("\n📚 For more information, see:")
    print("   - README.md - Main project documentation")
    print("   - WEB_UI_README.md - Web interface guide")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Demo setup interrupted by user.")
    except Exception as e:
        print(f"\n❌ Error during demo setup: {e}")
        sys.exit(1)
