# EcoStruxure Building Operation (EBO) Redundancy Configuration
# PRODUCTION CONFIGURATION - Update with your actual server details
# Replace all IP addresses, server names, and paths with your actual values

applications:
  # EBO Enterprise Server (Primary) - UPDATE THESE VALUES
  ebo_enterprise_primary:
    description: "EBO Enterprise Server - Primary Dell R750xa"
    priority: 1
    is_primary: true
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: "ebo_enterprise_secondary"
    server_ip: "YOUR_PRIMARY_SERVER_IP"      # CHANGE THIS: e.g., "*********"
    server_name: "YOUR_PRIMARY_SERVER_NAME"  # CHANGE THIS: e.g., "EBO-PRIMARY"
    ebo_services:
      - "EcoStruxure Building Operation Enterprise Server"
      - "EcoStruxure Building Operation Database Service"
      - "EcoStruxure Building Operation Web Service"
      - "EcoStruxure Building Operation Automation Server"
    monitors:
      - name: "ebo_enterprise_service"
        type: "service"
        params:
          service_name: "EcoStruxure Building Operation Enterprise Server"
          expected_status: "running"
      - name: "ebo_database_service"
        type: "service"
        params:
          service_name: "EcoStruxure Building Operation Database Service"
          expected_status: "running"
      - name: "ebo_web_service"
        type: "http"
        params:
          url: "http://************:80/EBO"
          expected_status_code: 200
          timeout_seconds: 10
      - name: "ebo_database_disk"
        type: "disk"
        params:
          path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation\\Database"
          threshold_percentage: 85
      - name: "ebo_server_network"
        type: "network"
        params:
          interface_name: "Ethernet"
          expected_status: "up"
      - name: "ebo_server_cpu"
        type: "performance"
        params:
          metric: "cpu_usage"
          threshold_percentage: 90
      - name: "ebo_server_memory"
        type: "performance"
        params:
          metric: "memory_usage"
          threshold_percentage: 85

  # EBO Enterprise Server (Secondary)
  ebo_enterprise_secondary:
    description: "EBO Enterprise Server - Secondary Dell R750xa"
    priority: 2
    is_primary: false
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: null
    server_ip: "************"
    server_name: "DELL-R750XA-02"
    ebo_services:
      - "EcoStruxure Building Operation Enterprise Server"
      - "EcoStruxure Building Operation Database Service"
      - "EcoStruxure Building Operation Web Service"
      - "EcoStruxure Building Operation Automation Server"
    monitors:
      - name: "ebo_enterprise_service_backup"
        type: "service"
        params:
          service_name: "EcoStruxure Building Operation Enterprise Server"
          expected_status: "running"
      - name: "ebo_database_service_backup"
        type: "service"
        params:
          service_name: "EcoStruxure Building Operation Database Service"
          expected_status: "running"
      - name: "ebo_web_service_backup"
        type: "http"
        params:
          url: "http://************:80/EBO"
          expected_status_code: 200
          timeout_seconds: 10
      - name: "ebo_database_disk_backup"
        type: "disk"
        params:
          path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation\\Database"
          threshold_percentage: 85
      - name: "ebo_server_network_backup"
        type: "network"
        params:
          interface_name: "Ethernet"
          expected_status: "up"

  # EBO License Server
  ebo_license_server:
    description: "EBO License Server"
    priority: 1
    is_primary: true
    auto_failover: true
    failover_threshold: 2
    recovery_threshold: 2
    failover_target: "ebo_license_backup"
    server_ip: "************"
    server_name: "DELL-R750XA-01"
    monitors:
      - name: "ebo_license_service"
        type: "service"
        params:
          service_name: "EcoStruxure Building Operation License Server"
          expected_status: "running"
      - name: "ebo_license_port"
        type: "port"
        params:
          host: "************"
          port: 1947
          timeout_seconds: 5
      - name: "ebo_license_disk"
        type: "disk"
        params:
          path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation\\License"
          threshold_percentage: 90

  # EBO License Server Backup
  ebo_license_backup:
    description: "EBO License Server Backup"
    priority: 2
    is_primary: false
    auto_failover: true
    failover_threshold: 2
    recovery_threshold: 2
    failover_target: null
    server_ip: "************"
    server_name: "DELL-R750XA-02"
    monitors:
      - name: "ebo_license_service_backup"
        type: "service"
        params:
          service_name: "EcoStruxure Building Operation License Server"
          expected_status: "running"
      - name: "ebo_license_port_backup"
        type: "port"
        params:
          host: "************"
          port: 1947
          timeout_seconds: 5

# Database Cluster Configuration for EBO
database_clusters:
  ebo_database_cluster:
    description: "EBO Database Cluster - SQL Server Express/Standard"
    database_type: sqlserver
    virtual_ip: "*************"
    auto_failover: true
    failover_threshold: 3
    heartbeat_interval: 5
    replication_mode: synchronous
    quorum_nodes: 2
    nodes:
      - name: "ebo-db-primary"
        host: "************"
        port: 1433
        database_type: sqlserver
        role: primary
        connection_string: "Server=************,1433;Database=EBO_Database;Integrated Security=true"
        username: "EBO_Service"
        password: "your_ebo_password"
        is_active: true
        database_path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation\\Database"
      - name: "ebo-db-secondary"
        host: "************"
        port: 1433
        database_type: sqlserver
        role: secondary
        connection_string: "Server=************,1433;Database=EBO_Database;Integrated Security=true"
        username: "EBO_Service"
        password: "your_ebo_password"
        is_active: true
        database_path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation\\Database"

# Storage Redundancy for EBO Files
storage_groups:
  ebo_application_data:
    description: "EBO Application Data and Configuration"
    storage_type: application_data
    replication_mode: real_time
    sync_interval: 60
    auto_failover: true
    compression_enabled: true
    encryption_enabled: false
    retention_days: 30
    exclude_patterns: ["*.tmp", "*.log", "Temp/*", "Cache/*"]
    locations:
      - name: "ebo_primary_app"
        path: "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation"
        host: "************"
        storage_type: application_data
        is_primary: true
        is_active: true
        capacity_gb: 50.0
      - name: "ebo_secondary_app"
        path: "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation"
        host: "************"
        storage_type: application_data
        is_primary: false
        is_active: true
        capacity_gb: 50.0
      - name: "ebo_backup_app"
        path: "D:\\EBO_Backup\\Application"
        host: "************"
        storage_type: application_data
        is_primary: false
        is_active: true
        capacity_gb: 100.0

  ebo_database_storage:
    description: "EBO Database Files and Backups"
    storage_type: database_files
    replication_mode: scheduled
    sync_interval: 300
    auto_failover: true
    compression_enabled: true
    encryption_enabled: true
    retention_days: 90
    exclude_patterns: ["*.ldf", "*.tmp"]
    locations:
      - name: "ebo_primary_db"
        path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation\\Database"
        host: "************"
        storage_type: database_files
        is_primary: true
        is_active: true
        capacity_gb: 200.0
      - name: "ebo_secondary_db"
        path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation\\Database"
        host: "************"
        storage_type: database_files
        is_primary: false
        is_active: true
        capacity_gb: 200.0
      - name: "ebo_backup_db"
        path: "D:\\EBO_Backup\\Database"
        host: "************"
        storage_type: database_files
        is_primary: false
        is_active: true
        capacity_gb: 500.0

  ebo_configuration_data:
    description: "EBO Configuration and License Files"
    storage_type: configuration_files
    replication_mode: on_change
    sync_interval: 30
    auto_failover: true
    compression_enabled: true
    encryption_enabled: true
    retention_days: 365
    exclude_patterns: []
    locations:
      - name: "ebo_primary_config"
        path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation"
        host: "************"
        storage_type: configuration_files
        is_primary: true
        is_active: true
        capacity_gb: 10.0
      - name: "ebo_secondary_config"
        path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation"
        host: "************"
        storage_type: configuration_files
        is_primary: false
        is_active: true
        capacity_gb: 10.0
      - name: "ebo_backup_config"
        path: "D:\\EBO_Backup\\Configuration"
        host: "************"
        storage_type: configuration_files
        is_primary: false
        is_active: true
        capacity_gb: 20.0

# Network Configuration
network_config:
  virtual_ip: "*************"
  primary_server: "************"
  secondary_server: "************"
  client_redirect_method: "dns_update"  # or "load_balancer"

# EBO Specific Settings
ebo_settings:
  installation_path: "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation"
  data_path: "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation"
  database_type: "SQL Server Express"  # or "SQL Server Standard"
  license_server_port: 1947
  web_server_port: 80
  automation_server_port: 443
  client_connections: 5

# Client PC Configuration
client_pcs:
  - name: "EBO-Client-01"
    ip: "************"
    connection_server: "*************"  # Virtual IP
  - name: "EBO-Client-02"
    ip: "************"
    connection_server: "*************"
  - name: "EBO-Client-03"
    ip: "************"
    connection_server: "*************"
  - name: "EBO-Client-04"
    ip: "************"
    connection_server: "*************"
  - name: "EBO-Client-05"
    ip: "************"
    connection_server: "*************"
