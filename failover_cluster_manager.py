"""
Enterprise Failover Cluster Manager
Similar to Microsoft Failover Cluster for comprehensive enterprise-level redundancy setup
"""

import yaml
import json
import time
import threading
import logging
import socket
import subprocess
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import requests
import psutil

class ClusterNodeState(Enum):
    """Cluster node states"""
    ONLINE = "online"
    OFFLINE = "offline"
    PAUSED = "paused"
    JOINING = "joining"
    FAILED = "failed"
    UNKNOWN = "unknown"

class ClusterResourceState(Enum):
    """Cluster resource states"""
    ONLINE = "online"
    OFFLINE = "offline"
    FAILED = "failed"
    PENDING = "pending"
    PARTIAL_ONLINE = "partial_online"

class ClusterResourceType(Enum):
    """Types of cluster resources"""
    APPLICATION = "application"
    DATABASE = "database"
    FILE_SHARE = "file_share"
    VIRTUAL_IP = "virtual_ip"
    DISK = "disk"
    SERVICE = "service"
    GENERIC_SCRIPT = "generic_script"

class FailoverPolicy(Enum):
    """Failover policies"""
    AUTOMATIC = "automatic"
    MANUAL = "manual"
    PREVENT = "prevent"

@dataclass
class ClusterNode:
    """Cluster node configuration"""
    name: str
    hostname: str
    ip_address: str
    port: int = 5000
    state: ClusterNodeState = ClusterNodeState.UNKNOWN
    is_preferred_owner: bool = False
    weight: int = 1000  # Node weight for failover decisions
    last_heartbeat: Optional[datetime] = None
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    disk_usage: float = 0.0
    network_latency: float = 0.0
    uptime: int = 0
    services_running: List[str] = None

    def __post_init__(self):
        if self.services_running is None:
            self.services_running = []

@dataclass
class ClusterResource:
    """Cluster resource configuration"""
    name: str
    resource_type: ClusterResourceType
    description: str
    state: ClusterResourceState = ClusterResourceState.OFFLINE
    current_owner: Optional[str] = None
    preferred_owners: List[str] = None
    possible_owners: List[str] = None
    failover_policy: FailoverPolicy = FailoverPolicy.AUTOMATIC
    restart_policy: str = "restart_on_failure"
    failure_threshold: int = 3
    restart_period: int = 900  # 15 minutes
    restart_delay: int = 0
    pending_timeout: int = 180  # 3 minutes
    look_alive_interval: int = 5
    is_alive_interval: int = 60
    dependencies: List[str] = None
    parameters: Dict[str, Any] = None

    def __post_init__(self):
        if self.preferred_owners is None:
            self.preferred_owners = []
        if self.possible_owners is None:
            self.possible_owners = []
        if self.dependencies is None:
            self.dependencies = []
        if self.parameters is None:
            self.parameters = {}

@dataclass
class ClusterGroup:
    """Cluster resource group"""
    name: str
    description: str
    resources: List[str] = None
    preferred_owners: List[str] = None
    auto_failback_type: str = "prevent"  # prevent, allow, scheduled
    failback_window_start: Optional[str] = None
    failback_window_end: Optional[str] = None
    failover_threshold: int = 3
    failover_period: int = 6  # hours

    def __post_init__(self):
        if self.resources is None:
            self.resources = []
        if self.preferred_owners is None:
            self.preferred_owners = []

class FailoverClusterManager:
    """Enterprise Failover Cluster Manager"""

    def __init__(self, config_file: str = "failover_cluster_config.yaml"):
        self.config_file = config_file
        self.config = {}
        self.nodes: Dict[str, ClusterNode] = {}
        self.resources: Dict[str, ClusterResource] = {}
        self.groups: Dict[str, ClusterGroup] = {}
        self.cluster_name = ""
        self.cluster_ip = ""
        self.monitoring_active = False
        self.heartbeat_threads = []
        self.resource_monitor_threads = []

        # Setup logging
        self.setup_logging()

        # Load configuration
        self.load_configuration()

        # Initialize cluster
        self.initialize_cluster()

    def setup_logging(self):
        """Setup logging for cluster manager"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('failover_cluster.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('FailoverCluster')

    def load_configuration(self):
        """Load cluster configuration"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    self.config = yaml.safe_load(f)
            else:
                self.create_default_cluster_config()

            self.logger.info(f"Loaded cluster configuration: {self.config_file}")

        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            self.create_default_cluster_config()

    def create_default_cluster_config(self):
        """Create default cluster configuration"""
        default_config = {
            'cluster': {
                'name': 'EBO-Failover-Cluster',
                'description': 'EcoStruxure Building Operation Failover Cluster',
                'cluster_ip': '**********',
                'heartbeat_interval': 5,
                'heartbeat_timeout': 15,
                'quorum_type': 'node_majority',  # node_majority, node_and_disk_majority, disk_only
                'witness_type': 'file_share',  # file_share, disk, cloud
                'witness_path': '\\\\witness-server\\cluster-witness',
                'default_failover_policy': 'automatic',
                'cluster_log_level': 'information'
            },
            'nodes': {
                'EBO-NODE-01': {
                    'hostname': 'EBO-PRIMARY-01',
                    'ip_address': '*********',
                    'port': 5000,
                    'is_preferred_owner': True,
                    'weight': 1000
                },
                'EBO-NODE-02': {
                    'hostname': 'EBO-SECONDARY-01',
                    'ip_address': '*********',
                    'port': 5000,
                    'is_preferred_owner': False,
                    'weight': 900
                },
                'EBO-NODE-03': {
                    'hostname': 'EBO-DR-01',
                    'ip_address': '*********',
                    'port': 5000,
                    'is_preferred_owner': False,
                    'weight': 800
                }
            },
            'resources': {
                'EBO-Database': {
                    'resource_type': 'database',
                    'description': 'EBO SQL Server Database',
                    'preferred_owners': ['EBO-NODE-01', 'EBO-NODE-02'],
                    'possible_owners': ['EBO-NODE-01', 'EBO-NODE-02', 'EBO-NODE-03'],
                    'failover_policy': 'automatic',
                    'failure_threshold': 3,
                    'parameters': {
                        'database_name': 'EBO_Database',
                        'instance_name': 'MSSQLSERVER',
                        'connection_string': 'Server=.;Database=EBO_Database;Integrated Security=true'
                    }
                },
                'EBO-Application': {
                    'resource_type': 'application',
                    'description': 'EBO Application Services',
                    'preferred_owners': ['EBO-NODE-01', 'EBO-NODE-02'],
                    'possible_owners': ['EBO-NODE-01', 'EBO-NODE-02'],
                    'failover_policy': 'automatic',
                    'dependencies': ['EBO-Database', 'EBO-VirtualIP'],
                    'parameters': {
                        'service_name': 'EcoStruxure Building Operation',
                        'executable_path': 'C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation\\EBO.exe',
                        'working_directory': 'C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation'
                    }
                },
                'EBO-VirtualIP': {
                    'resource_type': 'virtual_ip',
                    'description': 'EBO Cluster Virtual IP',
                    'preferred_owners': ['EBO-NODE-01', 'EBO-NODE-02'],
                    'possible_owners': ['EBO-NODE-01', 'EBO-NODE-02', 'EBO-NODE-03'],
                    'failover_policy': 'automatic',
                    'parameters': {
                        'ip_address': '*********0',
                        'subnet_mask': '*************',
                        'network_name': 'Cluster Network 1'
                    }
                },
                'EBO-FileShare': {
                    'resource_type': 'file_share',
                    'description': 'EBO Shared Storage',
                    'preferred_owners': ['EBO-NODE-01', 'EBO-NODE-02'],
                    'possible_owners': ['EBO-NODE-01', 'EBO-NODE-02'],
                    'failover_policy': 'automatic',
                    'dependencies': ['EBO-Disk'],
                    'parameters': {
                        'share_name': 'EBO-Data',
                        'path': 'E:\\EBO-Data',
                        'description': 'EBO Application Data Share'
                    }
                }
            },
            'groups': {
                'EBO-Services': {
                    'description': 'EBO Application Services Group',
                    'resources': ['EBO-Database', 'EBO-Application', 'EBO-VirtualIP', 'EBO-FileShare'],
                    'preferred_owners': ['EBO-NODE-01', 'EBO-NODE-02'],
                    'auto_failback_type': 'prevent',
                    'failover_threshold': 3,
                    'failover_period': 6
                }
            }
        }

        with open(self.config_file, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False, indent=2)

        self.config = default_config
        self.logger.info(f"Created default cluster configuration: {self.config_file}")

    def initialize_cluster(self):
        """Initialize cluster nodes, resources, and groups"""
        try:
            # Initialize cluster settings
            cluster_config = self.config.get('cluster', {})
            self.cluster_name = cluster_config.get('name', 'Default-Cluster')
            self.cluster_ip = cluster_config.get('cluster_ip', '**********')

            # Initialize nodes
            nodes_config = self.config.get('nodes', {})
            for node_name, node_config in nodes_config.items():
                self.nodes[node_name] = ClusterNode(
                    name=node_name,
                    hostname=node_config['hostname'],
                    ip_address=node_config['ip_address'],
                    port=node_config.get('port', 5000),
                    is_preferred_owner=node_config.get('is_preferred_owner', False),
                    weight=node_config.get('weight', 1000)
                )

            # Initialize resources
            resources_config = self.config.get('resources', {})
            for resource_name, resource_config in resources_config.items():
                self.resources[resource_name] = ClusterResource(
                    name=resource_name,
                    resource_type=ClusterResourceType(resource_config['resource_type']),
                    description=resource_config.get('description', ''),
                    preferred_owners=resource_config.get('preferred_owners', []),
                    possible_owners=resource_config.get('possible_owners', []),
                    failover_policy=FailoverPolicy(resource_config.get('failover_policy', 'automatic')),
                    failure_threshold=resource_config.get('failure_threshold', 3),
                    dependencies=resource_config.get('dependencies', []),
                    parameters=resource_config.get('parameters', {})
                )

            # Initialize groups
            groups_config = self.config.get('groups', {})
            for group_name, group_config in groups_config.items():
                self.groups[group_name] = ClusterGroup(
                    name=group_name,
                    description=group_config.get('description', ''),
                    resources=group_config.get('resources', []),
                    preferred_owners=group_config.get('preferred_owners', []),
                    auto_failback_type=group_config.get('auto_failback_type', 'prevent'),
                    failover_threshold=group_config.get('failover_threshold', 3),
                    failover_period=group_config.get('failover_period', 6)
                )

            self.logger.info(f"Initialized cluster '{self.cluster_name}' with {len(self.nodes)} nodes, {len(self.resources)} resources, {len(self.groups)} groups")

        except Exception as e:
            self.logger.error(f"Failed to initialize cluster: {e}")

    def start_cluster_monitoring(self):
        """Start cluster monitoring and heartbeat"""
        if self.monitoring_active:
            self.logger.warning("Cluster monitoring already active")
            return

        self.monitoring_active = True
        self.logger.info("Starting cluster monitoring")

        # Start heartbeat monitoring for each node
        for node_name in self.nodes.keys():
            heartbeat_thread = threading.Thread(
                target=self._monitor_node_heartbeat,
                args=(node_name,),
                daemon=True
            )
            heartbeat_thread.start()
            self.heartbeat_threads.append(heartbeat_thread)

        # Start resource monitoring
        for resource_name in self.resources.keys():
            resource_thread = threading.Thread(
                target=self._monitor_resource,
                args=(resource_name,),
                daemon=True
            )
            resource_thread.start()
            self.resource_monitor_threads.append(resource_thread)

        self.logger.info("Cluster monitoring started successfully")

    def stop_cluster_monitoring(self):
        """Stop cluster monitoring"""
        self.monitoring_active = False
        self.logger.info("Stopping cluster monitoring")

        # Wait for threads to finish
        for thread in self.heartbeat_threads + self.resource_monitor_threads:
            if thread.is_alive():
                thread.join(timeout=5)

        self.heartbeat_threads.clear()
        self.resource_monitor_threads.clear()
        self.logger.info("Cluster monitoring stopped")

    def _monitor_node_heartbeat(self, node_name: str):
        """Monitor heartbeat for a specific node"""
        node = self.nodes[node_name]
        failure_count = 0
        cluster_config = self.config.get('cluster', {})
        heartbeat_interval = cluster_config.get('heartbeat_interval', 5)
        heartbeat_timeout = cluster_config.get('heartbeat_timeout', 15)

        while self.monitoring_active:
            try:
                # Test node connectivity
                is_online = self._test_node_connectivity(node)

                if is_online:
                    node.state = ClusterNodeState.ONLINE
                    node.last_heartbeat = datetime.now()
                    failure_count = 0

                    # Update node performance metrics
                    self._update_node_metrics(node)

                else:
                    failure_count += 1
                    self.logger.warning(f"Node {node_name} heartbeat failed. Failure count: {failure_count}")

                    if failure_count >= 3:  # Node considered failed after 3 consecutive failures
                        if node.state != ClusterNodeState.FAILED:
                            self.logger.critical(f"Node {node_name} marked as FAILED")
                            node.state = ClusterNodeState.FAILED
                            self._handle_node_failure(node_name)

                time.sleep(heartbeat_interval)

            except Exception as e:
                self.logger.error(f"Error monitoring node {node_name}: {e}")
                time.sleep(heartbeat_interval)

    def _test_node_connectivity(self, node: ClusterNode) -> bool:
        """Test connectivity to a cluster node"""
        try:
            # Test ping
            ping_result = subprocess.run(
                ['ping', '-n', '1', '-w', '3000', node.ip_address],
                capture_output=True,
                text=True,
                timeout=5
            )

            if ping_result.returncode != 0:
                return False

            # Test cluster service port
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((node.ip_address, node.port))
            sock.close()

            return result == 0

        except Exception as e:
            self.logger.debug(f"Connectivity test failed for {node.name}: {e}")
            return False

    def _update_node_metrics(self, node: ClusterNode):
        """Update performance metrics for a node"""
        try:
            # In a real implementation, this would query the remote node for metrics
            # For now, we'll simulate some metrics
            import random
            node.cpu_usage = random.uniform(10, 90)
            node.memory_usage = random.uniform(20, 80)
            node.disk_usage = random.uniform(30, 70)
            node.network_latency = random.uniform(1, 50)
            node.uptime = int(time.time())  # Simplified uptime

        except Exception as e:
            self.logger.debug(f"Failed to update metrics for {node.name}: {e}")

    def _handle_node_failure(self, failed_node_name: str):
        """Handle node failure and initiate resource failover"""
        self.logger.critical(f"Handling failure of node: {failed_node_name}")

        # Find all resources currently owned by the failed node
        resources_to_failover = []
        for resource_name, resource in self.resources.items():
            if resource.current_owner == failed_node_name:
                resources_to_failover.append(resource_name)

        # Initiate failover for each resource
        for resource_name in resources_to_failover:
            self.logger.info(f"Initiating failover for resource: {resource_name}")
            self._failover_resource(resource_name, failed_node_name)

    def _monitor_resource(self, resource_name: str):
        """Monitor a specific cluster resource"""
        resource = self.resources[resource_name]
        failure_count = 0

        while self.monitoring_active:
            try:
                # Check if resource is online and healthy
                is_healthy = self._check_resource_health(resource)

                if is_healthy:
                    if resource.state != ClusterResourceState.ONLINE:
                        resource.state = ClusterResourceState.ONLINE
                        self.logger.info(f"Resource {resource_name} is now ONLINE")
                    failure_count = 0
                else:
                    failure_count += 1
                    self.logger.warning(f"Resource {resource_name} health check failed. Failure count: {failure_count}")

                    if failure_count >= resource.failure_threshold:
                        if resource.state != ClusterResourceState.FAILED:
                            self.logger.critical(f"Resource {resource_name} marked as FAILED")
                            resource.state = ClusterResourceState.FAILED

                            # Attempt restart or failover based on policy
                            if resource.failover_policy == FailoverPolicy.AUTOMATIC:
                                self._handle_resource_failure(resource_name)

                time.sleep(resource.look_alive_interval)

            except Exception as e:
                self.logger.error(f"Error monitoring resource {resource_name}: {e}")
                time.sleep(resource.look_alive_interval)

    def _check_resource_health(self, resource: ClusterResource) -> bool:
        """Check health of a cluster resource"""
        try:
            if resource.resource_type == ClusterResourceType.APPLICATION:
                return self._check_application_health(resource)
            elif resource.resource_type == ClusterResourceType.DATABASE:
                return self._check_database_health(resource)
            elif resource.resource_type == ClusterResourceType.VIRTUAL_IP:
                return self._check_virtual_ip_health(resource)
            elif resource.resource_type == ClusterResourceType.FILE_SHARE:
                return self._check_file_share_health(resource)
            elif resource.resource_type == ClusterResourceType.SERVICE:
                return self._check_service_health(resource)
            else:
                # Generic health check
                return True

        except Exception as e:
            self.logger.debug(f"Health check failed for {resource.name}: {e}")
            return False

    def _check_application_health(self, resource: ClusterResource) -> bool:
        """Check health of an application resource"""
        try:
            params = resource.parameters
            service_name = params.get('service_name')

            if service_name:
                # Check if service is running
                for proc in psutil.process_iter(['pid', 'name']):
                    if service_name.lower() in proc.info['name'].lower():
                        return True
                return False

            return True

        except Exception as e:
            self.logger.debug(f"Application health check failed: {e}")
            return False

    def _check_database_health(self, resource: ClusterResource) -> bool:
        """Check health of a database resource"""
        try:
            params = resource.parameters
            connection_string = params.get('connection_string')

            if connection_string:
                # In a real implementation, test database connectivity
                # For now, simulate health check
                return True

            return True

        except Exception as e:
            self.logger.debug(f"Database health check failed: {e}")
            return False

    def _check_virtual_ip_health(self, resource: ClusterResource) -> bool:
        """Check health of a virtual IP resource"""
        try:
            params = resource.parameters
            ip_address = params.get('ip_address')

            if ip_address:
                # Test if IP is reachable
                ping_result = subprocess.run(
                    ['ping', '-n', '1', '-w', '1000', ip_address],
                    capture_output=True,
                    text=True,
                    timeout=3
                )
                return ping_result.returncode == 0

            return True

        except Exception as e:
            self.logger.debug(f"Virtual IP health check failed: {e}")
            return False

    def _check_file_share_health(self, resource: ClusterResource) -> bool:
        """Check health of a file share resource"""
        try:
            params = resource.parameters
            path = params.get('path')

            if path:
                # Check if path is accessible
                import os
                return os.path.exists(path) and os.path.isdir(path)

            return True

        except Exception as e:
            self.logger.debug(f"File share health check failed: {e}")
            return False

    def _check_service_health(self, resource: ClusterResource) -> bool:
        """Check health of a Windows service resource"""
        try:
            params = resource.parameters
            service_name = params.get('service_name')

            if service_name:
                # Check Windows service status
                result = subprocess.run(
                    ['sc', 'query', service_name],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                return 'RUNNING' in result.stdout

            return True

        except Exception as e:
            self.logger.debug(f"Service health check failed: {e}")
            return False
