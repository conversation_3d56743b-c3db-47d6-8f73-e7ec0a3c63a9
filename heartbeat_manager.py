#!/usr/bin/env python3
"""
EBO Redundancy System - Heartbeat Manager
Implements comprehensive heartbeat monitoring for Dell PowerEdge servers
"""

import socket
import time
import threading
import subprocess
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import requests
import psutil

class HeartbeatManager:
    """Manages heartbeat monitoring between redundant servers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.running = False
        self.heartbeat_threads = []
        self.peer_status = {}
        self.local_status = {}
        self.failure_counts = {}
        
        # Setup logging
        self.logger = logging.getLogger('HeartbeatManager')
        handler = logging.FileHandler('logs/heartbeat.log')
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
        
        # Heartbeat configuration
        self.heartbeat_interval = config.get('heartbeat_interval', 5)
        self.heartbeat_timeout = config.get('heartbeat_timeout', 15)
        self.failure_threshold = config.get('failure_threshold', 3)
        self.peer_servers = config.get('peer_servers', [])
        
        # Network configuration
        self.heartbeat_port = config.get('heartbeat_port', 5405)
        self.heartbeat_multicast = config.get('heartbeat_multicast', '***********')
        
        # Initialize failure tracking
        for server in self.peer_servers:
            self.failure_counts[server['ip']] = 0
            self.peer_status[server['ip']] = {
                'status': 'unknown',
                'last_seen': None,
                'response_time': None,
                'services': {},
                'system_info': {}
            }
    
    def start_heartbeat_monitoring(self) -> bool:
        """Start all heartbeat monitoring threads"""
        if self.running:
            return False
            
        self.running = True
        self.logger.info("Starting heartbeat monitoring")
        
        try:
            # Start network ping heartbeat
            ping_thread = threading.Thread(target=self._network_ping_loop, daemon=True)
            ping_thread.start()
            self.heartbeat_threads.append(ping_thread)
            
            # Start service heartbeat
            service_thread = threading.Thread(target=self._service_heartbeat_loop, daemon=True)
            service_thread.start()
            self.heartbeat_threads.append(service_thread)
            
            # Start database heartbeat
            db_thread = threading.Thread(target=self._database_heartbeat_loop, daemon=True)
            db_thread.start()
            self.heartbeat_threads.append(db_thread)
            
            # Start application heartbeat
            app_thread = threading.Thread(target=self._application_heartbeat_loop, daemon=True)
            app_thread.start()
            self.heartbeat_threads.append(app_thread)
            
            # Start UDP heartbeat listener
            listener_thread = threading.Thread(target=self._udp_heartbeat_listener, daemon=True)
            listener_thread.start()
            self.heartbeat_threads.append(listener_thread)
            
            # Start UDP heartbeat sender
            sender_thread = threading.Thread(target=self._udp_heartbeat_sender, daemon=True)
            sender_thread.start()
            self.heartbeat_threads.append(sender_thread)
            
            self.logger.info(f"Started {len(self.heartbeat_threads)} heartbeat monitoring threads")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start heartbeat monitoring: {e}")
            self.running = False
            return False
    
    def stop_heartbeat_monitoring(self) -> bool:
        """Stop heartbeat monitoring"""
        self.running = False
        self.logger.info("Stopping heartbeat monitoring")
        
        # Wait for threads to finish
        for thread in self.heartbeat_threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        self.heartbeat_threads.clear()
        return True
    
    def _network_ping_loop(self):
        """Network ping heartbeat monitoring"""
        self.logger.info("Starting network ping heartbeat")
        
        while self.running:
            try:
                for server in self.peer_servers:
                    self._ping_server(server['ip'], server.get('name', server['ip']))
                
                time.sleep(self.heartbeat_interval)
                
            except Exception as e:
                self.logger.error(f"Network ping error: {e}")
                time.sleep(self.heartbeat_interval)
    
    def _ping_server(self, ip_address: str, server_name: str):
        """Ping a specific server"""
        try:
            start_time = time.time()
            
            # Use Windows ping command
            result = subprocess.run(
                ['ping', '-n', '1', '-w', '3000', ip_address],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            if result.returncode == 0:
                # Ping successful
                self.failure_counts[ip_address] = 0
                self.peer_status[ip_address].update({
                    'status': 'online',
                    'last_seen': datetime.now(),
                    'response_time': response_time
                })
                
                self.logger.debug(f"Ping to {server_name} ({ip_address}): {response_time:.1f}ms")
                
            else:
                # Ping failed
                self.failure_counts[ip_address] += 1
                self.peer_status[ip_address]['status'] = 'offline'
                
                self.logger.warning(f"Ping to {server_name} ({ip_address}) failed. Failure count: {self.failure_counts[ip_address]}")
                
                # Check if failure threshold reached
                if self.failure_counts[ip_address] >= self.failure_threshold:
                    self._trigger_failure_event(ip_address, 'network_ping', f"Network ping failed {self.failure_counts[ip_address]} times")
                
        except subprocess.TimeoutExpired:
            self.failure_counts[ip_address] += 1
            self.peer_status[ip_address]['status'] = 'timeout'
            self.logger.warning(f"Ping to {server_name} ({ip_address}) timed out")
            
        except Exception as e:
            self.logger.error(f"Ping error for {server_name} ({ip_address}): {e}")
    
    def _service_heartbeat_loop(self):
        """EBO service heartbeat monitoring"""
        self.logger.info("Starting service heartbeat monitoring")
        
        ebo_services = [
            "EcoStruxure Building Operation Enterprise Server",
            "EcoStruxure Building Operation Database Service",
            "EcoStruxure Building Operation Web Service",
            "EcoStruxure Building Operation Automation Server",
            "EcoStruxure Building Operation License Server"
        ]
        
        while self.running:
            try:
                local_services = {}
                
                for service_name in ebo_services:
                    try:
                        service = psutil.win_service_get(service_name)
                        status = service.status()
                        local_services[service_name] = {
                            'status': status,
                            'running': status == 'running'
                        }
                        
                    except psutil.NoSuchProcess:
                        local_services[service_name] = {
                            'status': 'not_found',
                            'running': False
                        }
                
                # Update local status
                self.local_status['services'] = local_services
                
                # Check for service failures
                failed_services = [name for name, info in local_services.items() if not info['running']]
                if failed_services:
                    self.logger.warning(f"Failed services detected: {failed_services}")
                    self._trigger_failure_event('localhost', 'service_failure', f"Services failed: {failed_services}")
                
                time.sleep(10)  # Check services every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Service heartbeat error: {e}")
                time.sleep(10)
    
    def _database_heartbeat_loop(self):
        """Database heartbeat monitoring"""
        self.logger.info("Starting database heartbeat monitoring")
        
        while self.running:
            try:
                for server in self.peer_servers:
                    self._check_database_connectivity(server['ip'], server.get('name', server['ip']))
                
                time.sleep(15)  # Check database every 15 seconds
                
            except Exception as e:
                self.logger.error(f"Database heartbeat error: {e}")
                time.sleep(15)
    
    def _check_database_connectivity(self, ip_address: str, server_name: str):
        """Check database connectivity to a server"""
        try:
            # Test SQL Server connection
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            start_time = time.time()
            
            result = sock.connect_ex((ip_address, 1433))
            response_time = (time.time() - start_time) * 1000
            
            sock.close()
            
            if result == 0:
                # Database connection successful
                if ip_address in self.peer_status:
                    self.peer_status[ip_address]['database'] = {
                        'status': 'online',
                        'response_time': response_time,
                        'last_check': datetime.now()
                    }
                
                self.logger.debug(f"Database connection to {server_name} ({ip_address}): {response_time:.1f}ms")
                
            else:
                # Database connection failed
                if ip_address in self.peer_status:
                    self.peer_status[ip_address]['database'] = {
                        'status': 'offline',
                        'last_check': datetime.now()
                    }
                
                self.logger.warning(f"Database connection to {server_name} ({ip_address}) failed")
                self._trigger_failure_event(ip_address, 'database_failure', "Database connection failed")
                
        except Exception as e:
            self.logger.error(f"Database check error for {server_name} ({ip_address}): {e}")
    
    def _application_heartbeat_loop(self):
        """EBO application heartbeat monitoring"""
        self.logger.info("Starting application heartbeat monitoring")
        
        while self.running:
            try:
                for server in self.peer_servers:
                    self._check_application_health(server['ip'], server.get('name', server['ip']))
                
                time.sleep(30)  # Check application every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Application heartbeat error: {e}")
                time.sleep(30)
    
    def _check_application_health(self, ip_address: str, server_name: str):
        """Check EBO application health"""
        try:
            # Check EBO web interface
            url = f"http://{ip_address}/EBO"
            
            start_time = time.time()
            response = requests.get(url, timeout=10)
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                # Application healthy
                if ip_address in self.peer_status:
                    self.peer_status[ip_address]['application'] = {
                        'status': 'online',
                        'response_time': response_time,
                        'status_code': response.status_code,
                        'last_check': datetime.now()
                    }
                
                self.logger.debug(f"Application health check for {server_name} ({ip_address}): {response_time:.1f}ms")
                
            else:
                # Application unhealthy
                if ip_address in self.peer_status:
                    self.peer_status[ip_address]['application'] = {
                        'status': 'unhealthy',
                        'status_code': response.status_code,
                        'last_check': datetime.now()
                    }
                
                self.logger.warning(f"Application health check failed for {server_name} ({ip_address}): HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            # Application unreachable
            if ip_address in self.peer_status:
                self.peer_status[ip_address]['application'] = {
                    'status': 'offline',
                    'error': str(e),
                    'last_check': datetime.now()
                }
            
            self.logger.warning(f"Application unreachable for {server_name} ({ip_address}): {e}")
            
        except Exception as e:
            self.logger.error(f"Application health check error for {server_name} ({ip_address}): {e}")
    
    def _udp_heartbeat_listener(self):
        """Listen for UDP heartbeat messages"""
        self.logger.info(f"Starting UDP heartbeat listener on port {self.heartbeat_port}")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.bind(('', self.heartbeat_port))
            sock.settimeout(1)
            
            while self.running:
                try:
                    data, addr = sock.recvfrom(1024)
                    self._process_heartbeat_message(data, addr[0])
                    
                except socket.timeout:
                    continue
                except Exception as e:
                    self.logger.error(f"UDP heartbeat listener error: {e}")
                    
        except Exception as e:
            self.logger.error(f"Failed to start UDP heartbeat listener: {e}")
        finally:
            try:
                sock.close()
            except:
                pass
    
    def _udp_heartbeat_sender(self):
        """Send UDP heartbeat messages"""
        self.logger.info("Starting UDP heartbeat sender")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            
            while self.running:
                try:
                    # Create heartbeat message
                    heartbeat_data = {
                        'timestamp': datetime.now().isoformat(),
                        'server_id': socket.gethostname(),
                        'services': self.local_status.get('services', {}),
                        'system_info': self._get_system_info()
                    }
                    
                    message = json.dumps(heartbeat_data).encode('utf-8')
                    
                    # Send to all peer servers
                    for server in self.peer_servers:
                        try:
                            sock.sendto(message, (server['ip'], self.heartbeat_port))
                        except Exception as e:
                            self.logger.debug(f"Failed to send heartbeat to {server['ip']}: {e}")
                    
                    time.sleep(self.heartbeat_interval)
                    
                except Exception as e:
                    self.logger.error(f"UDP heartbeat sender error: {e}")
                    time.sleep(self.heartbeat_interval)
                    
        except Exception as e:
            self.logger.error(f"Failed to start UDP heartbeat sender: {e}")
        finally:
            try:
                sock.close()
            except:
                pass
    
    def _process_heartbeat_message(self, data: bytes, sender_ip: str):
        """Process received heartbeat message"""
        try:
            heartbeat_data = json.loads(data.decode('utf-8'))
            
            if sender_ip in self.peer_status:
                self.peer_status[sender_ip].update({
                    'status': 'online',
                    'last_seen': datetime.now(),
                    'services': heartbeat_data.get('services', {}),
                    'system_info': heartbeat_data.get('system_info', {}),
                    'server_id': heartbeat_data.get('server_id', 'unknown')
                })
                
                # Reset failure count
                self.failure_counts[sender_ip] = 0
                
                self.logger.debug(f"Received heartbeat from {sender_ip}")
            
        except Exception as e:
            self.logger.error(f"Failed to process heartbeat from {sender_ip}: {e}")
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get current system information"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage': {disk.mountpoint: psutil.disk_usage(disk.mountpoint).percent 
                              for disk in psutil.disk_partitions() if disk.fstype},
                'uptime': time.time() - psutil.boot_time(),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Failed to get system info: {e}")
            return {}
    
    def _trigger_failure_event(self, server_ip: str, failure_type: str, details: str):
        """Trigger a failure event"""
        event = {
            'timestamp': datetime.now().isoformat(),
            'server_ip': server_ip,
            'failure_type': failure_type,
            'details': details,
            'failure_count': self.failure_counts.get(server_ip, 0)
        }
        
        self.logger.critical(f"FAILURE EVENT: {failure_type} for {server_ip} - {details}")
        
        # Here you would trigger the actual failover process
        # This could call the EBO redundancy manager's failover method
        
    def get_heartbeat_status(self) -> Dict[str, Any]:
        """Get current heartbeat status"""
        return {
            'running': self.running,
            'local_status': self.local_status,
            'peer_status': self.peer_status,
            'failure_counts': self.failure_counts,
            'configuration': {
                'heartbeat_interval': self.heartbeat_interval,
                'heartbeat_timeout': self.heartbeat_timeout,
                'failure_threshold': self.failure_threshold,
                'heartbeat_port': self.heartbeat_port
            }
        }
    
    def is_peer_healthy(self, server_ip: str) -> bool:
        """Check if a peer server is healthy"""
        if server_ip not in self.peer_status:
            return False
            
        status = self.peer_status[server_ip]
        
        # Check if we've seen the server recently
        if status.get('last_seen'):
            time_since_last_seen = datetime.now() - status['last_seen']
            if time_since_last_seen > timedelta(seconds=self.heartbeat_timeout):
                return False
        
        # Check overall status
        return status.get('status') == 'online'
