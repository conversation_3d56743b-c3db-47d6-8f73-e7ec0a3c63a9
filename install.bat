@echo off
title SafeKit-Style Redundancy Management System - Installer
color 0A

echo ================================================================
echo    SafeKit-Style Redundancy Management System
echo    Professional Installation Script v1.0
echo ================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: This installer must be run as Administrator
    echo Right-click on install.bat and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [1/6] Checking system requirements...
echo.

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows version: %VERSION%

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or higher:
    echo 1. Download from: https://python.org/downloads/
    echo 2. During installation, check "Add Python to PATH"
    echo 3. Run this installer again
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo Python version: %PYTHON_VERSION%

echo.
echo [2/6] Installing Python dependencies...
echo.

pip install --upgrade pip
pip install flask==2.3.3
pip install flask-cors==4.0.0
pip install psutil==5.9.5
pip install requests==2.31.0
pip install werkzeug==2.3.7

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to install Python dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

echo.
echo [3/6] Creating application directories...
echo.

if not exist "logs" (
    mkdir logs
    echo Created: logs\
)
if not exist "config" (
    mkdir config
    echo Created: config\
)
if not exist "data" (
    mkdir data
    echo Created: data\
)
if not exist "backup" (
    mkdir backup
    echo Created: backup\
)

echo.
echo [4/6] Setting up configuration files...
echo.

REM Create default configuration
echo {> config\default_config.json
echo   "system": {>> config\default_config.json
echo     "name": "SafeKit Redundancy System",>> config\default_config.json
echo     "version": "1.0.0",>> config\default_config.json
echo     "port": 5002,>> config\default_config.json
echo     "debug": false>> config\default_config.json
echo   },>> config\default_config.json
echo   "cluster": {>> config\default_config.json
echo     "heartbeat_interval": 5,>> config\default_config.json
echo     "failover_timeout": 30,>> config\default_config.json
echo     "auto_failback": true>> config\default_config.json
echo   }>> config\default_config.json
echo }>> config\default_config.json

echo Created: config\default_config.json

echo.
echo [5/6] Creating Windows service files...
echo.

REM Create service installer
echo @echo off> install_service.bat
echo echo Installing SafeKit Redundancy Service...>> install_service.bat
echo sc create "SafeKitRedundancy" binPath= "%CD%\service_wrapper.exe" start= auto>> install_service.bat
echo sc description "SafeKitRedundancy" "SafeKit-Style Redundancy Management System">> install_service.bat
echo echo Service installed successfully!>> install_service.bat
echo pause>> install_service.bat

REM Create service uninstaller
echo @echo off> uninstall_service.bat
echo echo Uninstalling SafeKit Redundancy Service...>> uninstall_service.bat
echo sc stop "SafeKitRedundancy">> uninstall_service.bat
echo sc delete "SafeKitRedundancy">> uninstall_service.bat
echo echo Service uninstalled successfully!>> uninstall_service.bat
echo pause>> uninstall_service.bat

echo.
echo [6/6] Creating startup scripts...
echo.

REM Create startup script
echo @echo off> start_redundancy_system.bat
echo title SafeKit Redundancy Management System>> start_redundancy_system.bat
echo color 0B>> start_redundancy_system.bat
echo echo ================================================================>> start_redundancy_system.bat
echo echo    SafeKit-Style Redundancy Management System>> start_redundancy_system.bat
echo echo    Starting Web Interface...>> start_redundancy_system.bat
echo echo ================================================================>> start_redundancy_system.bat
echo echo.>> start_redundancy_system.bat
echo echo Web Interface: http://localhost:5002>> start_redundancy_system.bat
echo echo SafeKit Console: http://localhost:5002/safekit-console>> start_redundancy_system.bat
echo echo Multi-Site Dashboard: http://localhost:5002/sites>> start_redundancy_system.bat
echo echo.>> start_redundancy_system.bat
echo echo Press Ctrl+C to stop the system>> start_redundancy_system.bat
echo echo.>> start_redundancy_system.bat
echo python multisite_web_interface.py>> start_redundancy_system.bat

REM Create quick start script
echo @echo off> quick_start.bat
echo start http://localhost:5002>> quick_start.bat
echo start start_redundancy_system.bat>> quick_start.bat

echo.
echo ================================================================
echo    INSTALLATION COMPLETED SUCCESSFULLY!
echo ================================================================
echo.
echo Next Steps:
echo.
echo 1. START THE SYSTEM:
echo    - Double-click: start_redundancy_system.bat
echo    - Or run: quick_start.bat (opens browser automatically)
echo.
echo 2. ACCESS WEB INTERFACE:
echo    - Main Dashboard: http://localhost:5002
echo    - SafeKit Console: http://localhost:5002/safekit-console
echo    - Site Management: http://localhost:5002/sites
echo.
echo 3. INSTALL AS WINDOWS SERVICE (Optional):
echo    - Run as Admin: install_service.bat
echo.
echo 4. NETWORK ACCESS:
echo    - Replace 'localhost' with server IP for remote access
echo    - Example: http://*************:5002
echo.
echo ================================================================
echo    Ready for Production Use!
echo ================================================================
echo.
pause
