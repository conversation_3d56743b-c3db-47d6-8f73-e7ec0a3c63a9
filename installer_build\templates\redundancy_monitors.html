{% extends "redundancy_base.html" %}

{% block title %}Monitors - Professional Redundancy Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="fas fa-heartbeat me-2 text-primary"></i>
                Monitor Management
            </h1>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMonitorModal">
                <i class="fas fa-plus me-1"></i>Add Monitor
            </button>
        </div>
    </div>
</div>

<!-- Monitor Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-heartbeat fa-2x text-primary mb-2"></i>
                <div class="metric-value text-primary" id="totalMonitors">-</div>
                <div class="metric-label">Total Monitors</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <div class="metric-value text-success" id="healthyMonitors">-</div>
                <div class="metric-label">Healthy Monitors</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                <div class="metric-value text-warning" id="unhealthyMonitors">-</div>
                <div class="metric-label">Unhealthy Monitors</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                <div class="metric-value text-info" id="lastCheck">-</div>
                <div class="metric-label">Last Check</div>
            </div>
        </div>
    </div>
</div>

<!-- Monitors by Application -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Monitors by Application
                </h5>
            </div>
            <div class="card-body">
                <div id="monitorsList">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">Loading monitors...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Monitor Modal -->
<div class="modal fade" id="addMonitorModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Monitor</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addMonitorForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="monitorApp" class="form-label">Application *</label>
                                <select class="form-select" id="monitorApp" required>
                                    <option value="">Select Application</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="monitorType" class="form-label">Monitor Type *</label>
                                <select class="form-select" id="monitorType" required>
                                    <option value="">Select Type</option>
                                    <option value="disk">Disk Monitor</option>
                                    <option value="network">Network Monitor</option>
                                    <option value="http">HTTP Monitor</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="monitorName" class="form-label">Monitor Name *</label>
                        <input type="text" class="form-control" id="monitorName" required>
                        <div class="form-text">Unique name for this monitor</div>
                    </div>
                    
                    <!-- Disk Monitor Fields -->
                    <div id="diskFields" class="monitor-fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="diskPath" class="form-label">Disk Path</label>
                                    <input type="text" class="form-control" id="diskPath" placeholder="e.g., / or C:\">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="diskThreshold" class="form-label">Threshold (%)</label>
                                    <input type="number" class="form-control" id="diskThreshold" min="1" max="100" value="90">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Network Monitor Fields -->
                    <div id="networkFields" class="monitor-fields" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="networkInterface" class="form-label">Network Interface</label>
                                    <select class="form-select" id="networkInterface">
                                        <option value="">Select Interface</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="networkStatus" class="form-label">Expected Status</label>
                                    <select class="form-select" id="networkStatus">
                                        <option value="up">Up</option>
                                        <option value="down">Down</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- HTTP Monitor Fields -->
                    <div id="httpFields" class="monitor-fields" style="display: none;">
                        <div class="mb-3">
                            <label for="httpUrl" class="form-label">URL</label>
                            <input type="url" class="form-control" id="httpUrl" placeholder="https://example.com/health">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="httpStatus" class="form-label">Expected Status Code</label>
                                    <input type="number" class="form-control" id="httpStatus" value="200" min="100" max="599">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="httpTimeout" class="form-label">Timeout (seconds)</label>
                                    <input type="number" class="form-control" id="httpTimeout" value="10" min="1" max="300">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveMonitor">Add Monitor</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentApplications = {};

$(document).ready(function() {
    loadMonitors();
    loadSystemInfo();
    
    $('#monitorType').change(function() {
        showMonitorFields($(this).val());
    });
    
    $('#saveMonitor').click(function() {
        addNewMonitor();
    });
    
    // Load applications for dropdown
    loadApplications();
});

function loadMonitors() {
    $.get('/api/status')
        .done(function(response) {
            if (response.success) {
                currentApplications = response.data.applications;
                displayMonitors(response.data.applications);
                updateMonitorMetrics(response.data.applications);
            } else {
                showAlert('danger', 'Failed to load monitors: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to connect to server');
        });
}

function displayMonitors(applications) {
    let html = '';
    let totalMonitors = 0;
    
    if (Object.keys(applications).length === 0) {
        html = '<div class="text-center py-5"><p class="text-muted">No applications configured.</p></div>';
    } else {
        Object.entries(applications).forEach(([appName, app]) => {
            const config = app.config;
            totalMonitors += config.monitors.length;
            
            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-server me-2"></i>
                            ${appName}
                            <span class="badge bg-info ms-2">${config.monitors.length} monitors</span>
                        </h6>
                    </div>
                    <div class="card-body">
            `;
            
            if (config.monitors.length === 0) {
                html += '<p class="text-muted">No monitors configured for this application.</p>';
            } else {
                html += '<div class="table-responsive"><table class="table table-sm">';
                html += '<thead><tr><th>Name</th><th>Type</th><th>Parameters</th><th>Status</th><th>Actions</th></tr></thead><tbody>';
                
                config.monitors.forEach((monitor, index) => {
                    let params = '';
                    if (monitor.params) {
                        params = Object.entries(monitor.params).map(([key, value]) => `${key}: ${value}`).join(', ');
                    }
                    
                    html += `
                        <tr>
                            <td><strong>${monitor.name}</strong></td>
                            <td><span class="badge bg-secondary">${monitor.type.toUpperCase()}</span></td>
                            <td><small class="text-muted">${params}</small></td>
                            <td><span class="status-indicator status-unknown"></span> Unknown</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="testMonitor('${appName}', '${monitor.name}')">
                                    <i class="fas fa-vial"></i> Test
                                </button>
                                <button class="btn btn-sm btn-outline-danger ms-1" onclick="removeMonitor('${appName}', ${index})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table></div>';
            }
            
            html += '</div></div>';
        });
    }
    
    $('#monitorsList').html(html);
}

function updateMonitorMetrics(applications) {
    let totalMonitors = 0;
    let healthyMonitors = 0;
    let unhealthyMonitors = 0;
    
    Object.values(applications).forEach(app => {
        totalMonitors += app.config.monitors.length;
        // Note: In a real implementation, you'd track individual monitor health
    });
    
    $('#totalMonitors').text(totalMonitors);
    $('#healthyMonitors').text(healthyMonitors);
    $('#unhealthyMonitors').text(unhealthyMonitors);
    $('#lastCheck').text(new Date().toLocaleTimeString());
}

function loadApplications() {
    $.get('/api/applications')
        .done(function(response) {
            if (response.success) {
                const select = $('#monitorApp');
                select.empty().append('<option value="">Select Application</option>');
                
                Object.keys(response.data).forEach(appName => {
                    select.append(`<option value="${appName}">${appName}</option>`);
                });
            }
        });
}

function loadSystemInfo() {
    $.get('/api/system/info')
        .done(function(response) {
            if (response.success) {
                populateNetworkInterfaces(response.data.network_interfaces);
            }
        });
}

function populateNetworkInterfaces(interfaces) {
    const select = $('#networkInterface');
    select.empty().append('<option value="">Select Interface</option>');
    
    interfaces.forEach(iface => {
        select.append(`<option value="${iface}">${iface}</option>`);
    });
}

function showMonitorFields(type) {
    $('.monitor-fields').hide();
    if (type) {
        $(`#${type}Fields`).show();
    }
}

function addNewMonitor() {
    const appName = $('#monitorApp').val();
    const monitorType = $('#monitorType').val();
    const monitorName = $('#monitorName').val();
    
    if (!appName || !monitorType || !monitorName) {
        showAlert('warning', 'Please fill in all required fields');
        return;
    }
    
    let params = {};
    
    if (monitorType === 'disk') {
        params = {
            path: $('#diskPath').val(),
            threshold_percentage: parseInt($('#diskThreshold').val())
        };
    } else if (monitorType === 'network') {
        params = {
            interface_name: $('#networkInterface').val(),
            expected_status: $('#networkStatus').val()
        };
    } else if (monitorType === 'http') {
        params = {
            url: $('#httpUrl').val(),
            expected_status_code: parseInt($('#httpStatus').val()),
            timeout_seconds: parseInt($('#httpTimeout').val())
        };
    }
    
    const monitorConfig = {
        name: monitorName,
        type: monitorType,
        params: params
    };
    
    $.ajax({
        url: `/api/applications/${appName}/monitors`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(monitorConfig),
        success: function(response) {
            if (response.success) {
                showAlert('success', 'Monitor added successfully');
                $('#addMonitorModal').modal('hide');
                $('#addMonitorForm')[0].reset();
                $('.monitor-fields').hide();
                loadMonitors();
            } else {
                showAlert('danger', 'Failed to add monitor: ' + response.message);
            }
        },
        error: function() {
            showAlert('danger', 'Failed to add monitor');
        }
    });
}

function testMonitor(appName, monitorName) {
    showAlert('info', `Testing monitor: ${monitorName}`);
    
    $.post(`/api/applications/${appName}/monitors/${monitorName}/test`)
        .done(function(response) {
            if (response.success) {
                const status = response.data.status;
                const message = response.data.message;
                const responseTime = response.data.response_time;
                
                showAlert(status === 'healthy' ? 'success' : 'warning', 
                         `Monitor test result: ${message} (${responseTime.toFixed(2)}s)`);
            } else {
                showAlert('danger', 'Monitor test failed: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to test monitor');
        });
}

function removeMonitor(appName, monitorIndex) {
    if (!confirm(`Are you sure you want to remove this monitor?`)) {
        return;
    }
    
    showAlert('info', 'Monitor removal not implemented yet');
    // Note: You would need to implement a remove monitor API endpoint
}
</script>
{% endblock %}
