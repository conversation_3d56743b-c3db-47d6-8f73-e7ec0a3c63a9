\
from src.redundancy.utils.logger import app_logger, setup_logger # Modified import
from src.redundancy.config.config_loader import load_config
from src.redundancy.monitors import DiskMonitor, NetworkMonitor, HTTPMonitor
import time
import logging # Added for log level mapping
import os # Added for path joining

# Map monitor types from config to their respective classes
MONITOR_CLASS_MAP = {
    "disk": DiskMonitor,
    "network": NetworkMonitor,
    "http": HTTPMonitor, # Added HTTPMonitor
}

def initialize_monitors(config_data):
    """
    Initializes monitor instances based on the loaded configuration.
    """
    if not config_data or "monitors" not in config_data:
        app_logger.warning("No monitors configuration found or configuration is empty.")
        return []

    active_monitors = []
    for monitor_conf in config_data["monitors"]:
        monitor_type = monitor_conf.get("type")
        monitor_name = monitor_conf.get("name", "UnnamedMonitor")

        MonitorClass = MONITOR_CLASS_MAP.get(monitor_type)
        if not MonitorClass:
            app_logger.error(f"Unknown monitor type: \'{monitor_type}\' for monitor \'{monitor_name}\'. Skipping.")
            continue

        try:
            # Create config dictionary for monitor initialization
            if monitor_type == "disk":
                path = monitor_conf.get("path")
                threshold = monitor_conf.get("threshold_percentage")
                if path is None or threshold is None:
                    app_logger.error(f"Missing 'path' or 'threshold_percentage' for disk monitor \'{monitor_name}\'. Skipping.")
                    continue
                config = {
                    "name": monitor_name,
                    "type": monitor_type,
                    "params": {
                        "path": path,
                        "threshold_percentage": int(threshold)
                    }
                }
                monitor_instance = DiskMonitor(config, app_logger)
            elif monitor_type == "network":
                interface = monitor_conf.get("interface")
                expected_status = monitor_conf.get("expected_status", "up")
                if interface is None:
                    app_logger.error(f"Missing 'interface' for network monitor \'{monitor_name}\'. Skipping.")
                    continue
                config = {
                    "name": monitor_name,
                    "type": monitor_type,
                    "params": {
                        "interface_name": interface,
                        "expected_status": expected_status
                    }
                }
                monitor_instance = NetworkMonitor(config, app_logger)
            elif monitor_type == "http":
                url = monitor_conf.get("url")
                expected_status = monitor_conf.get("expected_status", 200)
                timeout = monitor_conf.get("timeout_seconds", 10)
                if url is None:
                    app_logger.error(f"Missing 'url' for http monitor \'{monitor_name}\'. Skipping.")
                    continue
                config = {
                    "name": monitor_name,
                    "type": monitor_type,
                    "params": {
                        "url": url,
                        "expected_status_code": int(expected_status),
                        "timeout_seconds": int(timeout)
                    }
                }
                monitor_instance = HTTPMonitor(config, app_logger)
            else:
                # Should not happen if MONITOR_CLASS_MAP is correct
                app_logger.error(f"Monitor type \'{monitor_type}\' configured but no initialization logic in main.py. Skipping \'{monitor_name}\'.")
                continue

            active_monitors.append(monitor_instance)
            app_logger.info(f"Successfully initialized monitor: \'{monitor_name}\' of type \'{monitor_type}\'.")
        except Exception as e:
            app_logger.error(f"Failed to initialize monitor \'{monitor_name}\' of type \'{monitor_type}\': {e}")

    return active_monitors

def main():
    # Initial minimal logger setup (before config is loaded)
    # This will be reconfigured once config is read.
    # Using a global app_logger means we need to be careful if reconfiguring.
    # setup_logger will handle this by not adding duplicate handlers if called again.
    global app_logger # Declare that we are modifying the global app_logger from utils

    config = load_config() # Uses default "config.yaml"

    if not config:
        # Use the initially configured app_logger if config fails
        app_logger.error("Failed to load configuration. Exiting application. Using default logger settings.")
        return

    # Configure logging based on the loaded configuration
    log_config = config.get("logging", {})
    log_file_path = log_config.get("log_file", "hardware_redundancy_app.log")
    # Ensure log_file_path is absolute or relative to the project root if not already absolute
    if not os.path.isabs(log_file_path):
        # Assuming main.py is in the project root. Adjust if structure is different.
        project_root = os.path.dirname(os.path.abspath(__file__))
        log_file_path = os.path.join(project_root, log_file_path)

    log_level_str = log_config.get("log_level", "INFO").upper()
    log_levels = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }
    log_level = log_levels.get(log_level_str, logging.INFO)
    log_to_console = log_config.get("log_to_console", True)
    log_to_file = log_config.get("log_to_file", True)

    # Reconfigure the global app_logger with settings from config
    # The setup_logger function is designed to handle being called multiple times
    # by clearing existing handlers of the 'HardwareRedundancyApp' logger before adding new ones.
    app_logger = setup_logger(
        'HardwareRedundancyApp',
        level=log_level,
        log_file=log_file_path,
        log_to_console=log_to_console,
        log_to_file=log_to_file
    )

    app_logger.info("Hardware Redundancy Application starting with configured logger...")
    app_logger.info(f"Configuration loaded successfully. Log level: {log_level_str}, Log file: {log_file_path if log_to_file else 'Disabled'}")

    monitors = initialize_monitors(config)

    if not monitors:
        app_logger.warning("No monitors were initialized. The application will run but monitor nothing.")
        # Depending on requirements, we might want to exit here
        # return

    app_logger.info(f"Initialized {len(monitors)} monitor(s). Starting monitoring loop...")

    # Main monitoring loop
    # Configurable check interval (e.g., from config file or default)
    check_interval_seconds = config.get("global_settings", {}).get("check_interval_seconds", 60)
    app_logger.info(f"Monitoring check interval set to {check_interval_seconds} seconds.")

    try:
        while True:
            app_logger.info("--- Starting new monitoring cycle ---")
            for monitor in monitors:
                app_logger.debug(f"Checking status for monitor: \'{monitor.get_name()}\'")
                monitor.check_status()
                if not monitor.is_healthy:
                    app_logger.warning(f"Monitor \'{monitor.get_name()}\' reported an issue. Triggering redundancy measures.")
                    monitor.trigger_redundancy()
                else:
                    app_logger.debug(f"Monitor \'{monitor.get_name()}\' status OK.")

            app_logger.info(f"--- Monitoring cycle complete. Waiting for {check_interval_seconds} seconds. ---")
            time.sleep(check_interval_seconds)
    except KeyboardInterrupt:
        app_logger.info("Keyboard interrupt received. Shutting down Hardware Redundancy Application...")
    except Exception as e:
        app_logger.error(f"An unexpected error occurred in the main loop: {e}", exc_info=True)
    finally:
        app_logger.info("Hardware Redundancy Application finished.")

if __name__ == "__main__":
    main()
