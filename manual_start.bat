@echo off
title SafeKit Redundancy System - Manual Start
color 0B

echo ================================================================
echo    SafeKit-Style Redundancy Management System
echo    Manual Start (No Installation Required)
echo ================================================================
echo.

echo Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found. Please install Python 3.8+ from python.org
    pause
    exit /b 1
)

echo ✅ Python found
echo.
echo Starting SafeKit Redundancy System...
echo.
echo 🌐 Web Interface will be available at:
echo    Main Dashboard: http://localhost:5002
echo    SafeKit Console: http://localhost:5002/safekit-console
echo    Site Management: http://localhost:5002/sites
echo.
echo 📝 Note: If you get import errors, install packages with:
echo    python -m pip install flask flask-cors psutil requests
echo.
echo Press Ctrl+C to stop the system
echo.

REM Try to start the application directly
python multisite_web_interface.py

echo.
echo System stopped.
pause
