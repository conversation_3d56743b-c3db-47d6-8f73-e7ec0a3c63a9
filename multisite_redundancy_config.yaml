monitoring:
  alerting:
    alert_recipients:
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    email_enabled: true
    escalation_levels: 3
    sms_enabled: false
  performance_monitoring:
    collection_interval: 60
    enabled: true
    metrics:
    - cpu
    - memory
    - disk
    - network
    - ebo_services
  site_health_check:
    interval: 30
    methods:
    - ping
    - http
    - ebo_service
    - database
    timeout: 10
multisite_config:
  dns_failover: true
  dns_server: your-dns-server.com
  failover_strategy: priority_based
  global_virtual_ip: **********
  inter_site_replication: true
  primary_site: T3
  site_check_interval: 30
  site_failover_threshold: 3
replication:
  database_replication:
    backup_schedule: every_4_hours
    enabled: true
    method: sql_server_always_on
    retention_days: 30
    sync_mode: asynchronous
  file_replication:
    enabled: true
    method: dfsr
    paths:
    - C:\ProgramData\Schneider Electric\EcoStruxure Building Operation
    - C:\EBO_Config
    - C:\EBO_Backup
    sync_interval: 300
sites:
  branch_office_2:
    description: Branch Office - West Coast
    ebo_config:
      client_count: 3
      database_server: *********
      installation_path: C:\Program Files\Schneider Electric\EcoStruxure Building
        Operation
      license_server: *********
    location:
      address: West Coast Office
      city: West City
      country: Your Country
      timezone: UTC-8
    network:
      bandwidth: 500Mbps
      site_network: ********/16
      vpn_endpoint: west-vpn.yourcompany.com
      wan_ip: ************
    priority: 3
    servers:
      primary:
        hostname: WEST-EBO-PRIMARY
        ip_address: *********
        server_type: Dell PowerEdge R750xa
      secondary:
        hostname: WEST-EBO-SECONDARY
        ip_address: *********
        server_type: Dell PowerEdge R750xa
  disaster_recovery:
    description: Disaster Recovery Site
    ebo_config:
      client_count: 20
      database_server: *********
      installation_path: C:\Program Files\Schneider Electric\EcoStruxure Building
        Operation
      license_server: *********
    location:
      address: DR Facility
      city: DR City
      country: Your Country
      timezone: UTC-6
    network:
      bandwidth: 1Gbps
      site_network: ********/16
      vpn_endpoint: dr-vpn.yourcompany.com
      wan_ip: ************
    priority: 4
    servers:
      primary:
        hostname: DR-EBO-PRIMARY
        ip_address: *********
        server_type: Dell PowerEdge R750xa
      secondary:
        hostname: DR-EBO-SECONDARY
        ip_address: *********
        server_type: Dell PowerEdge R750xa
  headquarters:
    description: Main Headquarters Building
    ebo_config:
      client_count: 10
      database_server: *********
      installation_path: C:\Program Files\Schneider Electric\EcoStruxure Building
        Operation
      license_server: *********
    location:
      address: Main Campus, Building A
      city: Your City
      country: Your Country
      timezone: UTC-5
    network:
      bandwidth: 1Gbps
      site_network: ********/16
      vpn_endpoint: hq-vpn.yourcompany.com
      wan_ip: ************
    priority: 1
    servers:
      primary:
        hostname: HQ-EBO-PRIMARY
        ip_address: *********
        server_type: Dell PowerEdge R750xa
      secondary:
        hostname: HQ-EBO-SECONDARY
        ip_address: *********
        server_type: Dell PowerEdge R750xa
