#!/usr/bin/env python3
"""
EBO Multi-Site Redundancy Manager
Enterprise-grade multi-site redundancy for EcoStruxure Building Operation
Supports multiple buildings, campuses, and geographic locations
"""

import os
import sys
import yaml
import time
import threading
import subprocess
import socket
import psutil
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
from pathlib import Path

class MultiSiteRedundancyManager:
    """Manages redundancy across multiple sites and locations"""

    def __init__(self, config_file: str = "multisite_redundancy_config.yaml"):
        self.config_file = config_file
        self.config = {}
        self.sites = {}
        self.site_status = {}
        self.monitoring_active = False
        self.failover_in_progress = False
        self.current_primary_site = None

        # Setup logging
        self.setup_logging()

        # Load configuration
        self.load_configuration()

        # Initialize sites
        self.initialize_sites()

    def setup_logging(self):
        """Setup logging for multi-site operations"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'multisite_redundancy.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('MultiSiteRedundancy')

    def load_configuration(self):
        """Load multi-site configuration"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    self.config = yaml.safe_load(f)
            else:
                self.create_default_multisite_config()

            self.logger.info(f"Loaded configuration for {len(self.config.get('sites', {}))} sites")

        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            self.create_default_multisite_config()

    def create_default_multisite_config(self):
        """Create default multi-site configuration"""
        default_config = {
            'multisite_config': {
                'primary_site': 'headquarters',
                'failover_strategy': 'priority_based',  # priority_based, geographic, load_based
                'site_check_interval': 30,
                'site_failover_threshold': 3,
                'inter_site_replication': True,
                'global_virtual_ip': '**********',
                'dns_failover': True,
                'dns_server': 'your-dns-server.com'
            },
            'sites': {
                'headquarters': {
                    'description': 'Main Headquarters Building',
                    'priority': 1,
                    'location': {
                        'address': 'Main Campus, Building A',
                        'city': 'Your City',
                        'country': 'Your Country',
                        'timezone': 'UTC-5'
                    },
                    'network': {
                        'site_network': '********/16',
                        'wan_ip': '************',
                        'vpn_endpoint': 'hq-vpn.yourcompany.com',
                        'bandwidth': '1Gbps'
                    },
                    'servers': {
                        'primary': {
                            'hostname': 'HQ-EBO-PRIMARY',
                            'ip_address': '*********',
                            'server_type': 'Dell PowerEdge R750xa'
                        },
                        'secondary': {
                            'hostname': 'HQ-EBO-SECONDARY',
                            'ip_address': '*********',
                            'server_type': 'Dell PowerEdge R750xa'
                        }
                    },
                    'ebo_config': {
                        'installation_path': 'C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation',
                        'database_server': '*********',
                        'license_server': '*********',
                        'client_count': 10
                    }
                },
                'branch_office_1': {
                    'description': 'Branch Office - East Coast',
                    'priority': 2,
                    'location': {
                        'address': 'East Coast Office',
                        'city': 'East City',
                        'country': 'Your Country',
                        'timezone': 'UTC-5'
                    },
                    'network': {
                        'site_network': '********/16',
                        'wan_ip': '************',
                        'vpn_endpoint': 'east-vpn.yourcompany.com',
                        'bandwidth': '500Mbps'
                    },
                    'servers': {
                        'primary': {
                            'hostname': 'EAST-EBO-PRIMARY',
                            'ip_address': '*********',
                            'server_type': 'Dell PowerEdge R750xa'
                        },
                        'secondary': {
                            'hostname': 'EAST-EBO-SECONDARY',
                            'ip_address': '*********',
                            'server_type': 'Dell PowerEdge R750xa'
                        }
                    },
                    'ebo_config': {
                        'installation_path': 'C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation',
                        'database_server': '*********',
                        'license_server': '*********',
                        'client_count': 5
                    }
                },
                'branch_office_2': {
                    'description': 'Branch Office - West Coast',
                    'priority': 3,
                    'location': {
                        'address': 'West Coast Office',
                        'city': 'West City',
                        'country': 'Your Country',
                        'timezone': 'UTC-8'
                    },
                    'network': {
                        'site_network': '********/16',
                        'wan_ip': '************',
                        'vpn_endpoint': 'west-vpn.yourcompany.com',
                        'bandwidth': '500Mbps'
                    },
                    'servers': {
                        'primary': {
                            'hostname': 'WEST-EBO-PRIMARY',
                            'ip_address': '*********',
                            'server_type': 'Dell PowerEdge R750xa'
                        },
                        'secondary': {
                            'hostname': 'WEST-EBO-SECONDARY',
                            'ip_address': '*********',
                            'server_type': 'Dell PowerEdge R750xa'
                        }
                    },
                    'ebo_config': {
                        'installation_path': 'C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation',
                        'database_server': '*********',
                        'license_server': '*********',
                        'client_count': 3
                    }
                },
                'disaster_recovery': {
                    'description': 'Disaster Recovery Site',
                    'priority': 4,
                    'location': {
                        'address': 'DR Facility',
                        'city': 'DR City',
                        'country': 'Your Country',
                        'timezone': 'UTC-6'
                    },
                    'network': {
                        'site_network': '********/16',
                        'wan_ip': '************',
                        'vpn_endpoint': 'dr-vpn.yourcompany.com',
                        'bandwidth': '1Gbps'
                    },
                    'servers': {
                        'primary': {
                            'hostname': 'DR-EBO-PRIMARY',
                            'ip_address': '*********',
                            'server_type': 'Dell PowerEdge R750xa'
                        },
                        'secondary': {
                            'hostname': 'DR-EBO-SECONDARY',
                            'ip_address': '*********',
                            'server_type': 'Dell PowerEdge R750xa'
                        }
                    },
                    'ebo_config': {
                        'installation_path': 'C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation',
                        'database_server': '*********',
                        'license_server': '*********',
                        'client_count': 20  # Can handle all sites in DR scenario
                    }
                }
            },
            'replication': {
                'database_replication': {
                    'enabled': True,
                    'method': 'sql_server_always_on',  # sql_server_always_on, log_shipping, mirroring
                    'sync_mode': 'asynchronous',  # synchronous, asynchronous
                    'backup_schedule': 'every_4_hours',
                    'retention_days': 30
                },
                'file_replication': {
                    'enabled': True,
                    'method': 'dfsr',  # dfsr, robocopy, rsync
                    'sync_interval': 300,  # 5 minutes
                    'paths': [
                        'C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation',
                        'C:\\EBO_Config',
                        'C:\\EBO_Backup'
                    ]
                }
            },
            'monitoring': {
                'site_health_check': {
                    'interval': 30,
                    'timeout': 10,
                    'methods': ['ping', 'http', 'ebo_service', 'database']
                },
                'performance_monitoring': {
                    'enabled': True,
                    'metrics': ['cpu', 'memory', 'disk', 'network', 'ebo_services'],
                    'collection_interval': 60
                },
                'alerting': {
                    'email_enabled': True,
                    'sms_enabled': False,
                    'escalation_levels': 3,
                    'alert_recipients': [
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>'
                    ]
                }
            }
        }

        with open(self.config_file, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False, indent=2)

        self.config = default_config
        self.logger.info(f"Created default multi-site configuration: {self.config_file}")

    def initialize_sites(self):
        """Initialize all sites"""
        sites_config = self.config.get('sites', {})

        for site_id, site_config in sites_config.items():
            self.sites[site_id] = {
                'config': site_config,
                'status': 'unknown',
                'last_check': None,
                'servers': {},
                'ebo_status': {},
                'performance': {}
            }

            # Initialize server status for each site
            for server_role, server_config in site_config.get('servers', {}).items():
                self.sites[site_id]['servers'][server_role] = {
                    'config': server_config,
                    'status': 'unknown',
                    'last_seen': None,
                    'services': {},
                    'performance': {}
                }

        # Set primary site
        primary_site = self.config.get('multisite_config', {}).get('primary_site')
        if primary_site and primary_site in self.sites:
            self.current_primary_site = primary_site
            self.sites[primary_site]['status'] = 'primary'

        self.logger.info(f"Initialized {len(self.sites)} sites")

    def start_multisite_monitoring(self) -> bool:
        """Start multi-site monitoring"""
        if self.monitoring_active:
            return False

        self.monitoring_active = True

        # Start site monitoring thread
        monitor_thread = threading.Thread(target=self._multisite_monitoring_loop, daemon=True)
        monitor_thread.start()

        # Start replication monitoring
        replication_thread = threading.Thread(target=self._replication_monitoring_loop, daemon=True)
        replication_thread.start()

        # Start performance monitoring
        performance_thread = threading.Thread(target=self._performance_monitoring_loop, daemon=True)
        performance_thread.start()

        self.logger.info("Multi-site monitoring started")
        return True

    def stop_multisite_monitoring(self) -> bool:
        """Stop multi-site monitoring"""
        self.monitoring_active = False
        self.logger.info("Multi-site monitoring stopped")
        return True

    def _multisite_monitoring_loop(self):
        """Main multi-site monitoring loop"""
        check_interval = self.config.get('multisite_config', {}).get('site_check_interval', 30)

        while self.monitoring_active:
            try:
                for site_id in self.sites:
                    self._check_site_health(site_id)

                # Check if failover is needed
                self._evaluate_failover_conditions()

                time.sleep(check_interval)

            except Exception as e:
                self.logger.error(f"Multi-site monitoring error: {e}")
                time.sleep(check_interval)

    def _check_site_health(self, site_id: str):
        """Check health of a specific site"""
        try:
            site = self.sites[site_id]
            site_config = site['config']

            # Check each server at the site
            site_healthy = True

            for server_role, server_config in site_config.get('servers', {}).items():
                server_ip = server_config['ip_address']
                server_healthy = self._check_server_health(site_id, server_role, server_ip)

                if not server_healthy:
                    site_healthy = False

            # Update site status
            previous_status = site['status']
            site['status'] = 'healthy' if site_healthy else 'unhealthy'
            site['last_check'] = datetime.now()

            # Log status changes
            if previous_status != site['status']:
                self.logger.warning(f"Site {site_id} status changed: {previous_status} -> {site['status']}")

        except Exception as e:
            self.logger.error(f"Failed to check site {site_id} health: {e}")
            self.sites[site_id]['status'] = 'error'

    def _check_server_health(self, site_id: str, server_role: str, server_ip: str) -> bool:
        """Check health of a specific server"""
        try:
            # Ping test
            ping_result = subprocess.run(
                ['ping', '-n', '1', '-w', '3000', server_ip],
                capture_output=True,
                text=True,
                timeout=5
            )

            if ping_result.returncode != 0:
                self.logger.warning(f"Ping failed for {site_id}/{server_role} ({server_ip})")
                return False

            # HTTP test (EBO web interface)
            try:
                response = requests.get(f"http://{server_ip}", timeout=10)
                if response.status_code != 200:
                    self.logger.warning(f"HTTP check failed for {site_id}/{server_role}")
                    return False
            except requests.exceptions.RequestException:
                self.logger.warning(f"HTTP request failed for {site_id}/{server_role}")
                return False

            # Update server status
            self.sites[site_id]['servers'][server_role]['status'] = 'healthy'
            self.sites[site_id]['servers'][server_role]['last_seen'] = datetime.now()

            return True

        except Exception as e:
            self.logger.error(f"Server health check failed for {site_id}/{server_role}: {e}")
            return False

    def _evaluate_failover_conditions(self):
        """Evaluate if multi-site failover is needed"""
        if self.failover_in_progress:
            return

        primary_site = self.current_primary_site
        if not primary_site:
            return

        primary_site_status = self.sites[primary_site]['status']

        # Check if primary site is unhealthy
        if primary_site_status in ['unhealthy', 'error']:
            self.logger.critical(f"Primary site {primary_site} is {primary_site_status}")

            # Find best failover target
            failover_target = self._find_best_failover_site()

            if failover_target:
                self._initiate_multisite_failover(failover_target)

    def _find_best_failover_site(self) -> Optional[str]:
        """Find the best site for failover"""
        failover_strategy = self.config.get('multisite_config', {}).get('failover_strategy', 'priority_based')

        available_sites = []

        for site_id, site in self.sites.items():
            if site_id != self.current_primary_site and site['status'] == 'healthy':
                available_sites.append((site_id, site))

        if not available_sites:
            self.logger.error("No healthy sites available for failover")
            return None

        if failover_strategy == 'priority_based':
            # Sort by priority (lower number = higher priority)
            available_sites.sort(key=lambda x: x[1]['config'].get('priority', 999))
            return available_sites[0][0]

        elif failover_strategy == 'geographic':
            # Choose geographically closest site (simplified)
            return available_sites[0][0]

        elif failover_strategy == 'load_based':
            # Choose site with lowest load (simplified)
            return available_sites[0][0]

        return available_sites[0][0]

    def _initiate_multisite_failover(self, target_site: str):
        """Initiate failover to target site"""
        self.failover_in_progress = True

        try:
            self.logger.critical(f"Initiating multi-site failover from {self.current_primary_site} to {target_site}")

            # Update DNS records (if enabled)
            if self.config.get('multisite_config', {}).get('dns_failover', False):
                self._update_dns_records(target_site)

            # Update global virtual IP
            self._update_global_virtual_ip(target_site)

            # Notify administrators
            self._send_failover_alerts(self.current_primary_site, target_site)

            # Update primary site
            if self.current_primary_site:
                self.sites[self.current_primary_site]['status'] = 'secondary'

            self.current_primary_site = target_site
            self.sites[target_site]['status'] = 'primary'

            self.logger.info(f"Multi-site failover completed: {target_site} is now primary")

        except Exception as e:
            self.logger.error(f"Multi-site failover failed: {e}")
        finally:
            self.failover_in_progress = False

    def _update_dns_records(self, target_site: str):
        """Update DNS records for failover"""
        # This would integrate with your DNS provider's API
        self.logger.info(f"Updating DNS records to point to {target_site}")
        # Implementation depends on your DNS provider

    def _update_global_virtual_ip(self, target_site: str):
        """Update global virtual IP routing"""
        # This would update network routing or load balancer configuration
        self.logger.info(f"Updating global virtual IP routing to {target_site}")
        # Implementation depends on your network infrastructure

    def _send_failover_alerts(self, from_site: str, to_site: str):
        """Send failover alert notifications"""
        alert_message = f"CRITICAL: Multi-site failover executed\nFrom: {from_site}\nTo: {to_site}\nTime: {datetime.now()}"

        # Send email alerts
        recipients = self.config.get('monitoring', {}).get('alerting', {}).get('alert_recipients', [])
        for recipient in recipients:
            self._send_email_alert(recipient, "EBO Multi-Site Failover Alert", alert_message)

    def _send_email_alert(self, recipient: str, subject: str, message: str):
        """Send email alert"""
        # Email implementation would go here
        self.logger.info(f"Email alert sent to {recipient}: {subject}")

    def _replication_monitoring_loop(self):
        """Monitor inter-site replication"""
        while self.monitoring_active:
            try:
                # Check database replication status
                self._check_database_replication()

                # Check file replication status
                self._check_file_replication()

                time.sleep(300)  # Check every 5 minutes

            except Exception as e:
                self.logger.error(f"Replication monitoring error: {e}")
                time.sleep(300)

    def _check_database_replication(self):
        """Check database replication between sites"""
        # Implementation for checking SQL Server Always On, log shipping, etc.
        self.logger.debug("Checking database replication status")

    def _check_file_replication(self):
        """Check file replication between sites"""
        # Implementation for checking DFSR, robocopy, etc.
        self.logger.debug("Checking file replication status")

    def _performance_monitoring_loop(self):
        """Monitor performance across all sites"""
        while self.monitoring_active:
            try:
                for site_id in self.sites:
                    self._collect_site_performance(site_id)

                time.sleep(60)  # Collect every minute

            except Exception as e:
                self.logger.error(f"Performance monitoring error: {e}")
                time.sleep(60)

    def _collect_site_performance(self, site_id: str):
        """Collect performance metrics for a site"""
        # Implementation for collecting CPU, memory, disk, network metrics
        self.logger.debug(f"Collecting performance metrics for {site_id}")

    def get_multisite_status(self) -> Dict[str, Any]:
        """Get current multi-site status"""
        return {
            'monitoring_active': self.monitoring_active,
            'current_primary_site': self.current_primary_site,
            'sites': self.sites,
            'failover_in_progress': self.failover_in_progress,
            'total_sites': len(self.sites),
            'healthy_sites': len([s for s in self.sites.values() if s['status'] == 'healthy']),
            'last_update': datetime.now().isoformat()
        }

    def manual_failover(self, target_site: str) -> bool:
        """Manually initiate failover to specific site"""
        if target_site not in self.sites:
            self.logger.error(f"Target site {target_site} not found")
            return False

        if self.sites[target_site]['status'] != 'healthy':
            self.logger.error(f"Target site {target_site} is not healthy")
            return False

        self.logger.info(f"Manual failover initiated to {target_site}")
        self._initiate_multisite_failover(target_site)
        return True

    def add_site(self, site_id: str, site_config: Dict[str, Any]) -> bool:
        """Add a new site to the multi-site configuration"""
        try:
            if site_id in self.sites:
                self.logger.error(f"Site {site_id} already exists")
                return False

            # Add to sites dictionary
            self.sites[site_id] = {
                'config': site_config,
                'status': 'unknown',
                'last_check': None,
                'servers': {},
                'ebo_status': {},
                'performance': {}
            }

            # Initialize server status for the new site
            for server_role, server_config in site_config.get('servers', {}).items():
                self.sites[site_id]['servers'][server_role] = {
                    'config': server_config,
                    'status': 'unknown',
                    'last_seen': None,
                    'services': {},
                    'performance': {}
                }

            # Update configuration file
            self.config['sites'][site_id] = site_config
            self._save_configuration()

            self.logger.info(f"Site {site_id} added successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add site {site_id}: {e}")
            return False

    def remove_site(self, site_id: str) -> bool:
        """Remove a site from the multi-site configuration"""
        try:
            if site_id not in self.sites:
                self.logger.error(f"Site {site_id} not found")
                return False

            if site_id == self.current_primary_site:
                self.logger.error(f"Cannot remove primary site {site_id}")
                return False

            # Remove from sites dictionary
            del self.sites[site_id]

            # Update configuration file
            if site_id in self.config.get('sites', {}):
                del self.config['sites'][site_id]
                self._save_configuration()

            self.logger.info(f"Site {site_id} removed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to remove site {site_id}: {e}")
            return False

    def _save_configuration(self):
        """Save current configuration to file"""
        try:
            import yaml
            with open(self.config_file, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
            self.logger.info(f"Configuration saved to {self.config_file}")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")

if __name__ == '__main__':
    # Example usage
    manager = MultiSiteRedundancyManager()
    manager.start_multisite_monitoring()

    try:
        while True:
            status = manager.get_multisite_status()
            print(f"Primary Site: {status['current_primary_site']}")
            print(f"Healthy Sites: {status['healthy_sites']}/{status['total_sites']}")
            time.sleep(30)
    except KeyboardInterrupt:
        manager.stop_multisite_monitoring()
        print("Multi-site monitoring stopped")
