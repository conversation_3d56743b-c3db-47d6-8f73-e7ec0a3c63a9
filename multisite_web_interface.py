#!/usr/bin/env python3
"""
EBO Multi-Site Redundancy Web Interface
Professional web interface for managing multi-site EBO redundancy
"""

from flask import Flask, render_template, jsonify, request, redirect, url_for, session
import json
from datetime import datetime
from multisite_redundancy_manager import MultiSiteRedundancyManager
from site_replication_wizard import SiteReplicationWizard
from auth_middleware import (
    login_required, permission_required, admin_required,
    get_current_user, get_client_ip, get_user_agent,
    log_user_action, create_login_page
)
from user_management import user_manager, Permission, UserRole

app = Flask(__name__)
app.secret_key = 'ebo-multisite-redundancy-secret-key-2024'  # Change this in production
multisite_manager = None
replication_wizard = None

def initialize_multisite_manager():
    """Initialize the multi-site redundancy manager"""
    global multisite_manager
    if multisite_manager is None:
        multisite_manager = MultiSiteRedundancyManager()
        multisite_manager.start_multisite_monitoring()

def initialize_replication_wizard():
    """Initialize the site replication wizard"""
    global replication_wizard
    if replication_wizard is None:
        replication_wizard = SiteReplicationWizard()

@app.route('/')
@login_required
def multisite_dashboard():
    """Multi-site dashboard"""
    initialize_multisite_manager()
    log_user_action("VIEW_DASHBOARD", "dashboard", "Accessed main dashboard")
    return render_template('multisite_dashboard.html')

@app.route('/api/multisite/status')
@permission_required(Permission.VIEW_DASHBOARD)
def api_multisite_status():
    """API endpoint for multi-site status"""
    initialize_multisite_manager()
    status = multisite_manager.get_multisite_status()
    return jsonify(status)

@app.route('/api/multisite/sites')
@permission_required(Permission.VIEW_SITES)
def api_sites():
    """API endpoint for site information"""
    initialize_multisite_manager()
    sites = multisite_manager.sites

    # Format site data for web interface
    formatted_sites = {}
    for site_id, site_data in sites.items():
        formatted_sites[site_id] = {
            'id': site_id,
            'description': site_data['config'].get('description', site_id),
            'priority': site_data['config'].get('priority', 999),
            'status': site_data.get('status', 'unknown'),
            'location': site_data['config'].get('location', {}),
            'network': site_data['config'].get('network', {}),
            'servers': site_data.get('servers', {}),
            'ebo_config': site_data['config'].get('ebo_config', {}),
            'last_check': site_data.get('last_check').isoformat() if site_data.get('last_check') else None
        }

    return jsonify(formatted_sites)

@app.route('/api/multisite/failover', methods=['POST'])
@permission_required(Permission.MANAGE_REPLICATION)
def api_manual_failover():
    """API endpoint for manual failover"""
    initialize_multisite_manager()
    log_user_action("MANUAL_FAILOVER", "failover", "Initiated manual failover")

    data = request.get_json()
    target_site = data.get('target_site')

    if not target_site:
        return jsonify({'error': 'Target site not specified'}), 400

    success = multisite_manager.manual_failover(target_site)

    if success:
        log_user_action("MANUAL_FAILOVER_SUCCESS", "failover", f"Failover to {target_site} successful")
        return jsonify({'message': f'Failover initiated to {target_site}'})
    else:
        log_user_action("MANUAL_FAILOVER_FAILED", "failover", f"Failover to {target_site} failed")
        return jsonify({'error': 'Failover failed'}), 500

@app.route('/add-site')
@permission_required(Permission.EDIT_SITES)
def add_site_page():
    """Add new site page"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Add New Site</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
            input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; }
            .form-row { display: flex; gap: 20px; }
            .form-row .form-group { flex: 1; }
            .btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
            .btn:hover { background: #0056b3; }
            .btn-secondary { background: #6c757d; }
            .btn-secondary:hover { background: #545b62; }
            h1 { color: #2c3e50; margin-bottom: 30px; }
            h2 { color: #34495e; margin-top: 30px; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
            .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
            .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🏗️ Add New Site to Multi-Site Redundancy</h1>

            <div id="message"></div>

            <form id="addSiteForm">
                <h2>📍 Basic Site Information</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="site_id">Site ID (unique identifier):</label>
                        <input type="text" id="site_id" name="site_id" required placeholder="e.g., branch_office_3">
                    </div>
                    <div class="form-group">
                        <label for="description">Site Description:</label>
                        <input type="text" id="description" name="description" required placeholder="e.g., Branch Office - South Region">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="priority">Priority (1=highest):</label>
                        <input type="number" id="priority" name="priority" required min="1" max="10" value="5">
                    </div>
                    <div class="form-group">
                        <label for="client_count">Expected Client Count:</label>
                        <input type="number" id="client_count" name="client_count" required min="1" max="100" value="5">
                    </div>
                </div>

                <h2>📍 Location Information</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="address">Address:</label>
                        <input type="text" id="address" name="address" required placeholder="e.g., 123 Business Park Drive">
                    </div>
                    <div class="form-group">
                        <label for="city">City:</label>
                        <input type="text" id="city" name="city" required placeholder="e.g., South City">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="country">Country:</label>
                        <input type="text" id="country" name="country" required placeholder="e.g., United States">
                    </div>
                    <div class="form-group">
                        <label for="timezone">Timezone:</label>
                        <select id="timezone" name="timezone" required>
                            <option value="UTC-12">UTC-12 (Baker Island)</option>
                            <option value="UTC-11">UTC-11 (American Samoa)</option>
                            <option value="UTC-10">UTC-10 (Hawaii)</option>
                            <option value="UTC-9">UTC-9 (Alaska)</option>
                            <option value="UTC-8">UTC-8 (Pacific Time)</option>
                            <option value="UTC-7">UTC-7 (Mountain Time)</option>
                            <option value="UTC-6">UTC-6 (Central Time)</option>
                            <option value="UTC-5" selected>UTC-5 (Eastern Time)</option>
                            <option value="UTC-4">UTC-4 (Atlantic Time)</option>
                            <option value="UTC-3">UTC-3 (Argentina)</option>
                            <option value="UTC-2">UTC-2 (South Georgia)</option>
                            <option value="UTC-1">UTC-1 (Azores)</option>
                            <option value="UTC+0">UTC+0 (London)</option>
                            <option value="UTC+1">UTC+1 (Central Europe)</option>
                            <option value="UTC+2">UTC+2 (Eastern Europe)</option>
                            <option value="UTC+3">UTC+3 (Moscow)</option>
                            <option value="UTC+4">UTC+4 (Dubai)</option>
                            <option value="UTC+5">UTC+5 (Pakistan)</option>
                            <option value="UTC+6">UTC+6 (Bangladesh)</option>
                            <option value="UTC+7">UTC+7 (Thailand)</option>
                            <option value="UTC+8">UTC+8 (China)</option>
                            <option value="UTC+9">UTC+9 (Japan)</option>
                            <option value="UTC+10">UTC+10 (Australia East)</option>
                            <option value="UTC+11">UTC+11 (Solomon Islands)</option>
                            <option value="UTC+12">UTC+12 (New Zealand)</option>
                        </select>
                    </div>
                </div>

                <h2>🌐 Network Configuration</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="site_network">Site Network (CIDR):</label>
                        <input type="text" id="site_network" name="site_network" required placeholder="e.g., ********/16">
                    </div>
                    <div class="form-group">
                        <label for="wan_ip">WAN/Public IP:</label>
                        <input type="text" id="wan_ip" name="wan_ip" required placeholder="e.g., ************">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="vpn_endpoint">VPN Endpoint:</label>
                        <input type="text" id="vpn_endpoint" name="vpn_endpoint" required placeholder="e.g., south-vpn.yourcompany.com">
                    </div>
                    <div class="form-group">
                        <label for="bandwidth">Bandwidth:</label>
                        <select id="bandwidth" name="bandwidth" required>
                            <option value="100Mbps">100Mbps</option>
                            <option value="500Mbps" selected>500Mbps</option>
                            <option value="1Gbps">1Gbps</option>
                            <option value="10Gbps">10Gbps</option>
                        </select>
                    </div>
                </div>

                <h2>🖥️ Server Configuration</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="primary_hostname">Primary Server Hostname:</label>
                        <input type="text" id="primary_hostname" name="primary_hostname" required placeholder="e.g., SOUTH-EBO-PRIMARY">
                    </div>
                    <div class="form-group">
                        <label for="primary_ip">Primary Server IP:</label>
                        <input type="text" id="primary_ip" name="primary_ip" required placeholder="e.g., *********">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="secondary_hostname">Secondary Server Hostname:</label>
                        <input type="text" id="secondary_hostname" name="secondary_hostname" required placeholder="e.g., SOUTH-EBO-SECONDARY">
                    </div>
                    <div class="form-group">
                        <label for="secondary_ip">Secondary Server IP:</label>
                        <input type="text" id="secondary_ip" name="secondary_ip" required placeholder="e.g., *********">
                    </div>
                </div>

                <div class="form-group">
                    <label for="server_type">Server Type:</label>
                    <select id="server_type" name="server_type" required>
                        <option value="Dell PowerEdge R750xa" selected>Dell PowerEdge R750xa</option>
                        <option value="Dell PowerEdge R750">Dell PowerEdge R750</option>
                        <option value="Dell PowerEdge R740">Dell PowerEdge R740</option>
                        <option value="Dell PowerEdge R640">Dell PowerEdge R640</option>
                        <option value="Other Dell Server">Other Dell Server</option>
                        <option value="HP ProLiant">HP ProLiant</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <h2>🏗️ EBO Configuration</h2>
                <div class="form-group">
                    <label for="ebo_installation_path">EBO Installation Path:</label>
                    <input type="text" id="ebo_installation_path" name="ebo_installation_path" required
                           value="C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="database_server">Database Server IP:</label>
                        <input type="text" id="database_server" name="database_server" required placeholder="Usually same as primary server">
                    </div>
                    <div class="form-group">
                        <label for="license_server">License Server IP:</label>
                        <input type="text" id="license_server" name="license_server" required placeholder="Usually same as primary server">
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <button type="submit" class="btn">🏗️ Add Site</button>
                    <button type="button" class="btn btn-secondary" onclick="window.location.href='/sites'">Cancel</button>
                </div>
            </form>
        </div>

        <script>
            document.getElementById('addSiteForm').addEventListener('submit', function(e) {
                e.preventDefault();

                // Auto-fill database and license server IPs if empty
                const primaryIp = document.getElementById('primary_ip').value;
                if (!document.getElementById('database_server').value) {
                    document.getElementById('database_server').value = primaryIp;
                }
                if (!document.getElementById('license_server').value) {
                    document.getElementById('license_server').value = primaryIp;
                }

                const formData = new FormData(e.target);
                const siteData = Object.fromEntries(formData.entries());

                // Convert to the expected format
                const siteConfig = {
                    site_id: siteData.site_id,
                    config: {
                        description: siteData.description,
                        priority: parseInt(siteData.priority),
                        location: {
                            address: siteData.address,
                            city: siteData.city,
                            country: siteData.country,
                            timezone: siteData.timezone
                        },
                        network: {
                            site_network: siteData.site_network,
                            wan_ip: siteData.wan_ip,
                            vpn_endpoint: siteData.vpn_endpoint,
                            bandwidth: siteData.bandwidth
                        },
                        servers: {
                            primary: {
                                hostname: siteData.primary_hostname,
                                ip_address: siteData.primary_ip,
                                server_type: siteData.server_type
                            },
                            secondary: {
                                hostname: siteData.secondary_hostname,
                                ip_address: siteData.secondary_ip,
                                server_type: siteData.server_type
                            }
                        },
                        ebo_config: {
                            installation_path: siteData.ebo_installation_path,
                            database_server: siteData.database_server,
                            license_server: siteData.license_server,
                            client_count: parseInt(siteData.client_count)
                        }
                    }
                };

                fetch('/api/sites/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(siteConfig)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('message').innerHTML =
                            '<div class="success">✅ Site added successfully! Redirecting to sites overview...</div>';
                        setTimeout(() => {
                            window.location.href = '/sites';
                        }, 2000);
                    } else {
                        document.getElementById('message').innerHTML =
                            '<div class="error">❌ Error: ' + (data.error || 'Unknown error') + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('message').innerHTML =
                        '<div class="error">❌ Error: ' + error + '</div>';
                });
            });
        </script>
    </body>
    </html>
    '''

@app.route('/api/sites/add', methods=['POST'])
def api_add_site():
    """API endpoint to add a new site"""
    initialize_multisite_manager()

    try:
        data = request.get_json()
        site_id = data.get('site_id')
        site_config = data.get('config')

        if not site_id or not site_config:
            return jsonify({'success': False, 'error': 'Missing site_id or config'}), 400

        # Add site to manager
        success = multisite_manager.add_site(site_id, site_config)

        if success:
            return jsonify({'success': True, 'message': f'Site {site_id} added successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to add site'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sites/<site_id>/remove', methods=['DELETE'])
def api_remove_site(site_id):
    """API endpoint to remove a site"""
    initialize_multisite_manager()

    try:
        success = multisite_manager.remove_site(site_id)

        if success:
            return jsonify({'success': True, 'message': f'Site {site_id} removed successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to remove site'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/replicate-site')
def replicate_site_page():
    """Site replication wizard page"""
    return render_template('site_replication_wizard.html')

@app.route('/api/scan-source-site')
def api_scan_source_site():
    """API endpoint to scan source site"""
    initialize_replication_wizard()

    try:
        scan_results = replication_wizard.scan_existing_site()
        return jsonify(scan_results)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/validate-target-site', methods=['POST'])
def api_validate_target_site():
    """API endpoint to validate target site"""
    try:
        data = request.get_json()
        # Simple validation - in real implementation, would test connectivity
        if data.get('ip') and data.get('name'):
            return jsonify({'valid': True})
        else:
            return jsonify({'valid': False, 'error': 'Missing required information'})
    except Exception as e:
        return jsonify({'valid': False, 'error': str(e)})

@app.route('/api/create-replication-plan', methods=['POST'])
def api_create_replication_plan():
    """API endpoint to create replication plan"""
    initialize_replication_wizard()

    try:
        data = request.get_json()
        source_scan = data.get('source_scan')
        target_info = data.get('target_info')

        plan = replication_wizard.create_replication_plan(source_scan, target_info)
        return jsonify(plan)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/execute-replication', methods=['POST'])
def api_execute_replication():
    """API endpoint to execute replication"""
    initialize_replication_wizard()

    try:
        data = request.get_json()
        plan = data.get('plan')

        # In real implementation, this would execute the actual replication
        # For demo, we'll just return success
        success = True  # replication_wizard.execute_replication(plan)

        return jsonify({'success': success})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Authentication Routes
@app.route('/login')
def login_page():
    """Login page"""
    user = get_current_user()
    if user:
        return redirect('/')
    return create_login_page()

@app.route('/api/auth/login', methods=['POST'])
def api_login():
    """API endpoint for user login"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'error': 'Username and password required'}), 400

        user = user_manager.authenticate_user(
            username, password,
            get_client_ip(), get_user_agent()
        )

        if user:
            session['session_token'] = user['session_token']
            return jsonify({'success': True, 'user': user})
        else:
            return jsonify({'error': 'Invalid username or password'}), 401

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/logout', methods=['POST'])
def api_logout():
    """API endpoint for user logout"""
    session_token = session.get('session_token')
    if session_token:
        user_manager.logout_user(session_token, get_client_ip(), get_user_agent())
        session.clear()
    return jsonify({'success': True})

@app.route('/api/auth/current-user')
@login_required
def api_current_user():
    """Get current authenticated user"""
    user = get_current_user()
    return jsonify({'user': user})

# User Management Routes
@app.route('/users')
@admin_required
def user_management():
    """User management page"""
    return render_template('user_management.html')

@app.route('/api/users')
@admin_required
def api_users():
    """Get all users"""
    try:
        user = get_current_user()
        users = user_manager.get_users(user['organization'])
        return jsonify({'users': users})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users', methods=['POST'])
@admin_required
def api_create_user():
    """Create new user"""
    try:
        data = request.get_json()
        current_user = get_current_user()

        success = user_manager.create_user(
            data['username'],
            data['email'],
            data['password'],
            UserRole(data['role']),
            data['organization'],
            data['full_name'],
            current_user['user_id']
        )

        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'error': 'User creation failed'}), 400

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
@admin_required
def api_delete_user(user_id):
    """Delete user"""
    try:
        # Implementation would go here
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/audit-logs')
@permission_required(Permission.VIEW_AUDIT_LOGS)
def api_audit_logs():
    """Get audit logs"""
    try:
        user = get_current_user()
        logs = user_manager.get_audit_logs(100, user['organization'])
        return jsonify({'logs': logs})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/sites')
@login_required
def sites_overview():
    """Sites overview page"""
    # For now, return a simple HTML page instead of template
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Multi-Site Overview</title>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <style>
            :root {
                --primary-color: #3b82f6;
                --primary-dark: #1d4ed8;
                --secondary-color: #10b981;
                --danger-color: #ef4444;
                --warning-color: #f59e0b;
                --success-color: #22c55e;
                --info-color: #06b6d4;
                --dark-color: #1f2937;
                --light-color: #f8fafc;
                --border-color: #e2e8f0;
                --text-primary: #1e293b;
                --text-secondary: #64748b;
                --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
                --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
                --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
                --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
                --radius-sm: 0.375rem;
                --radius-md: 0.5rem;
                --radius-lg: 0.75rem;
                --radius-xl: 1rem;
            }

            * { margin: 0; padding: 0; box-sizing: border-box; }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
                background-size: 400% 400%;
                animation: gradientShift 15s ease infinite;
                min-height: 100vh;
                color: var(--text-primary);
                line-height: 1.6;
                zoom: 0.9;
            }

            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            .header {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                padding: 1rem 0;
                box-shadow: var(--shadow-lg);
                margin-bottom: 2rem;
            }

            .header-content {
                max-width: 100%;
                margin: 0;
                padding: 0 2rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .header h1 {
                color: var(--text-primary);
                font-size: 1.75rem;
                font-weight: 700;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-left: -0.5rem;
            }

            .header h1 i {
                color: var(--primary-color);
                font-size: 1.5rem;
            }

            .actions {
                display: flex;
                gap: 1rem;
                align-items: center;
                margin-right: -0.5rem;
            }

            .btn {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.875rem 1.75rem;
                border: none;
                border-radius: var(--radius-lg);
                font-weight: 600;
                font-size: 0.875rem;
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                text-decoration: none;
                text-align: center;
                position: relative;
                overflow: hidden;
                box-shadow: var(--shadow-md);
            }

            .btn-success {
                background: linear-gradient(135deg, var(--success-color), #16a34a);
                color: white;
            }

            .btn-success:hover {
                transform: translateY(-3px);
                box-shadow: var(--shadow-lg);
                background: linear-gradient(135deg, #16a34a, #15803d);
            }

            .btn-primary {
                background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
                color: white;
            }

            .btn-primary:hover {
                transform: translateY(-3px);
                box-shadow: var(--shadow-lg);
                background: linear-gradient(135deg, var(--primary-dark), #1e40af);
            }

            .container {
                max-width: 100%;
                margin: 0;
                padding: 0 2rem;
            }

            .sites-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
                gap: 1.5rem;
                margin-top: 2rem;
            }

            .site-card {
                background: rgba(255, 255, 255, 0.95);
                border-radius: var(--radius-xl);
                padding: 2rem;
                box-shadow: var(--shadow-xl);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }

            .site-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: var(--border-color);
                transition: all 0.3s ease;
            }

            .site-card.primary::before {
                background: linear-gradient(90deg, var(--success-color), #16a34a);
            }

            .site-card.healthy::before {
                background: linear-gradient(90deg, var(--info-color), #0891b2);
            }

            .site-card.unhealthy::before {
                background: linear-gradient(90deg, var(--danger-color), #dc2626);
            }

            .site-card:hover {
                transform: translateY(-8px) scale(1.02);
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            }

            .site-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 1.5rem;
            }

            .site-title {
                font-size: 1.5rem;
                font-weight: 700;
                color: var(--text-primary);
                margin-bottom: 0.5rem;
            }

            .site-status {
                padding: 0.5rem 1rem;
                border-radius: 9999px;
                font-size: 0.75rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .site-status.primary {
                background: var(--success-color);
                color: white;
            }

            .site-status.healthy {
                background: var(--info-color);
                color: white;
            }

            .site-status.unhealthy {
                background: var(--danger-color);
                color: white;
            }

            .site-status.unknown {
                background: var(--text-secondary);
                color: white;
            }

            .site-info {
                margin-bottom: 1.5rem;
            }

            .site-info-item {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-bottom: 0.75rem;
                color: var(--text-secondary);
                font-size: 0.875rem;
            }

            .site-info-item i {
                color: var(--primary-color);
                width: 1rem;
                text-align: center;
            }

            .site-actions {
                display: flex;
                gap: 0.75rem;
                margin-top: 1.5rem;
            }

            .btn-sm {
                padding: 0.5rem 1rem;
                font-size: 0.75rem;
            }

            .btn-info {
                background: linear-gradient(135deg, var(--info-color), #0891b2);
                color: white;
            }

            .btn-info:hover {
                background: linear-gradient(135deg, #0891b2, #0e7490);
                transform: translateY(-2px);
            }

            .btn-danger {
                background: linear-gradient(135deg, var(--danger-color), #dc2626);
                color: white;
            }

            .btn-danger:hover {
                background: linear-gradient(135deg, #dc2626, #b91c1c);
                transform: translateY(-2px);
            }

            .loading {
                text-align: center;
                padding: 4rem;
                color: var(--text-secondary);
                font-size: 1.125rem;
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .fade-in {
                animation: fadeInUp 0.6s ease-out;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="header-content">
                <h1><i class="fas fa-building"></i>Multi-Site Overview</h1>
                <div class="actions">
                    <a href="/add-site" class="btn btn-success"><i class="fas fa-plus"></i>Add New Site</a>
                    <a href="/" class="btn btn-primary"><i class="fas fa-home"></i>Dashboard</a>
                </div>
            </div>
        </div>

        <div class="container">
            <div id="sites-container" class="sites-grid">
                <div class="loading">Loading sites...</div>
            </div>
        </div>

        <script>
            function loadSites() {
                fetch('/api/multisite/sites')
                    .then(response => response.json())
                    .then(sites => {
                        const container = document.getElementById('sites-container');
                        container.innerHTML = '';

                        Object.entries(sites).forEach(([siteId, site], index) => {
                            const siteCard = document.createElement('div');
                            siteCard.className = `site-card ${site.status} fade-in`;
                            siteCard.style.animationDelay = `${index * 0.1}s`;

                            const statusIcon = getStatusIcon(site.status);

                            siteCard.innerHTML = `
                                <div class="site-header">
                                    <div>
                                        <div class="site-title">${site.description}</div>
                                    </div>
                                    <div class="site-status ${site.status}">
                                        <i class="${statusIcon}"></i>
                                        ${site.status}
                                    </div>
                                </div>

                                <div class="site-info">
                                    <div class="site-info-item">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>${site.location.city || 'Unknown'}, ${site.location.country || 'Unknown'}</span>
                                    </div>
                                    <div class="site-info-item">
                                        <i class="fas fa-server"></i>
                                        <span>Primary: ${site.servers?.primary?.hostname || 'Not configured'} (${site.servers?.primary?.ip_address || 'N/A'})</span>
                                    </div>
                                    <div class="site-info-item">
                                        <i class="fas fa-server"></i>
                                        <span>Secondary: ${site.servers?.secondary?.hostname || 'Not configured'} (${site.servers?.secondary?.ip_address || 'N/A'})</span>
                                    </div>
                                    <div class="site-info-item">
                                        <i class="fas fa-desktop"></i>
                                        <span>EBO Clients: ${site.ebo_config?.client_count || 'Not configured'}</span>
                                    </div>
                                    <div class="site-info-item">
                                        <i class="fas fa-database"></i>
                                        <span>Database: ${site.ebo_config?.database_server || 'Not configured'}</span>
                                    </div>
                                </div>

                                <div class="site-actions">
                                    <button class="btn btn-info btn-sm" onclick="viewSite('${siteId}')">
                                        <i class="fas fa-eye"></i> View Details
                                    </button>
                                    <button class="btn btn-primary btn-sm" onclick="editSite('${siteId}')">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="removeSite('${siteId}')">
                                        <i class="fas fa-trash"></i> Remove
                                    </button>
                                </div>
                            `;

                            container.appendChild(siteCard);
                        });
                    })
                    .catch(error => {
                        document.getElementById('sites-container').innerHTML = 'Error loading sites: ' + error;
                    });
            }

            function getStatusIcon(status) {
                switch(status) {
                    case 'primary': return 'fas fa-crown';
                    case 'healthy': return 'fas fa-check-circle';
                    case 'unhealthy': return 'fas fa-exclamation-triangle';
                    default: return 'fas fa-question-circle';
                }
            }

            function viewSite(siteId) {
                window.location.href = `/sites/${siteId}`;
            }

            function editSite(siteId) {
                window.location.href = `/sites/${siteId}/edit`;
            }

            function removeSite(siteId) {
                if (confirm(`Are you sure you want to remove site "${siteId}"?`)) {
                    fetch(`/api/sites/${siteId}/remove`, {
                        method: 'DELETE'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Site removed successfully!');
                            loadSites(); // Reload the sites list
                        } else {
                            alert('Error removing site: ' + (data.error || 'Unknown error'));
                        }
                    })
                    .catch(error => {
                        alert('Error removing site: ' + error);
                    });
                }
            }

            loadSites();
            setInterval(loadSites, 30000); // Refresh every 30 seconds
        </script>
    </body>
    </html>
    '''

@app.route('/sites/<site_id>')
@permission_required(Permission.VIEW_SITES)
def site_detail(site_id):
    """Site detail page"""
    initialize_multisite_manager()
    log_user_action("VIEW_SITE", "site_detail", f"Viewed site details for {site_id}")

    if site_id not in multisite_manager.sites:
        return "Site not found", 404

    return f'''
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Site Detail: {site_id}</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            :root {{
                --primary-color: #3b82f6;
                --secondary-color: #10b981;
                --accent-color: #f59e0b;
                --danger-color: #ef4444;
                --warning-color: #f59e0b;
                --success-color: #10b981;
                --info-color: #06b6d4;
                --text-primary: #1f2937;
                --text-secondary: #6b7280;
                --text-muted: #9ca3af;
                --bg-primary: #ffffff;
                --bg-secondary: #f8fafc;
                --border-color: #e5e7eb;
                --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                --radius-sm: 0.375rem;
                --radius-md: 0.5rem;
                --radius-lg: 0.75rem;
                --radius-xl: 1rem;
            }}

            * {{
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }}

            body {{
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
                background-size: 400% 400%;
                animation: gradientShift 15s ease infinite;
                min-height: 100vh;
                color: var(--text-primary);
                line-height: 1.6;
                zoom: 0.9;
            }}

            @keyframes gradientShift {{
                0% {{ background-position: 0% 50%; }}
                50% {{ background-position: 100% 50%; }}
                100% {{ background-position: 0% 50%; }}
            }}

            .header {{
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                padding: 1rem 0;
                box-shadow: var(--shadow-lg);
                margin-bottom: 2rem;
            }}

            .header-content {{
                max-width: 100%;
                margin: 0;
                padding: 0 2rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }}

            .header h1 {{
                color: var(--text-primary);
                font-size: 1.75rem;
                font-weight: 700;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-left: -0.5rem;
            }}

            .header h1 i {{
                color: var(--info-color);
                font-size: 1.5rem;
            }}

            .subtitle {{
                color: var(--text-secondary);
                font-size: 1rem;
                margin-top: 0.5rem;
                font-weight: 400;
            }}

            .header-actions {{
                display: flex;
                gap: 1rem;
                margin-right: -0.5rem;
            }}

            .nav-btn {{
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.875rem 1.75rem;
                border: none;
                border-radius: var(--radius-lg);
                font-weight: 600;
                font-size: 0.875rem;
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                text-decoration: none;
                text-align: center;
                box-shadow: var(--shadow-md);
            }}

            .btn-secondary {{
                background: var(--secondary-color);
                color: white;
            }}

            .btn-secondary:hover {{
                background: #059669;
                transform: translateY(-2px);
                box-shadow: var(--shadow-xl);
            }}

            .btn-primary {{
                background: var(--primary-color);
                color: white;
            }}

            .btn-primary:hover {{
                background: #2563eb;
                transform: translateY(-2px);
                box-shadow: var(--shadow-xl);
            }}

            .container {{
                max-width: 100%;
                margin: 0;
                padding: 0 2rem;
            }}

            .detail-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                gap: 2rem;
                margin-bottom: 2rem;
            }}

            .detail-card {{
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: var(--radius-xl);
                padding: 2rem;
                box-shadow: var(--shadow-lg);
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }}

            .detail-card::before {{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            }}

            .detail-card:hover {{
                transform: translateY(-4px);
                box-shadow: var(--shadow-xl);
            }}

            .overview-card::before {{
                background: linear-gradient(90deg, var(--info-color), var(--primary-color));
            }}

            .location-card::before {{
                background: linear-gradient(90deg, var(--success-color), var(--secondary-color));
            }}

            .network-card::before {{
                background: linear-gradient(90deg, var(--warning-color), var(--accent-color));
            }}

            .servers-card::before {{
                background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            }}

            .ebo-card::before {{
                background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
            }}

            .performance-card::before {{
                background: linear-gradient(90deg, var(--accent-color), var(--warning-color));
            }}

            .card-header {{
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-bottom: 1.5rem;
            }}

            .card-icon {{
                width: 3rem;
                height: 3rem;
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.25rem;
                color: white;
            }}

            .overview-icon {{
                background: linear-gradient(135deg, var(--info-color), var(--primary-color));
            }}

            .location-icon {{
                background: linear-gradient(135deg, var(--success-color), var(--secondary-color));
            }}

            .network-icon {{
                background: linear-gradient(135deg, var(--warning-color), var(--accent-color));
            }}

            .servers-icon {{
                background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            }}

            .ebo-icon {{
                background: linear-gradient(135deg, var(--secondary-color), var(--success-color));
            }}

            .performance-icon {{
                background: linear-gradient(135deg, var(--accent-color), var(--warning-color));
            }}

            .card-title {{
                font-size: 1.5rem;
                font-weight: 700;
                color: var(--text-primary);
            }}

            .detail-grid-inner {{
                display: grid;
                gap: 1rem;
            }}

            .detail-item {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem;
                background: rgba(248, 250, 252, 0.8);
                border-radius: var(--radius-md);
                border: 1px solid var(--border-color);
            }}

            .detail-label {{
                font-weight: 600;
                color: var(--text-primary);
            }}

            .detail-value {{
                font-weight: 500;
                color: var(--text-secondary);
            }}

            .status-badge {{
                padding: 0.375rem 0.75rem;
                border-radius: var(--radius-md);
                font-size: 0.875rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.025em;
            }}

            .status-healthy {{
                background: rgba(16, 185, 129, 0.1);
                color: var(--success-color);
                border: 1px solid rgba(16, 185, 129, 0.2);
            }}

            .status-warning {{
                background: rgba(245, 158, 11, 0.1);
                color: var(--warning-color);
                border: 1px solid rgba(245, 158, 11, 0.2);
            }}

            .status-error {{
                background: rgba(239, 68, 68, 0.1);
                color: var(--danger-color);
                border: 1px solid rgba(239, 68, 68, 0.2);
            }}

            .status-unknown {{
                background: rgba(107, 114, 128, 0.1);
                color: var(--text-muted);
                border: 1px solid rgba(107, 114, 128, 0.2);
            }}

            .loading {{
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 3rem;
                color: var(--text-muted);
                font-size: 1.125rem;
            }}

            .loading i {{
                margin-right: 0.5rem;
                animation: spin 1s linear infinite;
            }}

            @keyframes spin {{
                from {{ transform: rotate(0deg); }}
                to {{ transform: rotate(360deg); }}
            }}

            .fade-in {{
                animation: fadeIn 0.6s ease-out;
            }}

            @keyframes fadeIn {{
                from {{
                    opacity: 0;
                    transform: translateY(20px);
                }}
                to {{
                    opacity: 1;
                    transform: translateY(0);
                }}
            }}

            .metric-value {{
                font-size: 1.25rem;
                font-weight: 700;
                color: var(--primary-color);
            }}

            .priority-badge {{
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 1rem;
                border-radius: var(--radius-md);
                font-weight: 600;
                font-size: 0.875rem;
            }}

            .priority-1 {{
                background: rgba(239, 68, 68, 0.1);
                color: var(--danger-color);
                border: 1px solid rgba(239, 68, 68, 0.2);
            }}

            .priority-2 {{
                background: rgba(245, 158, 11, 0.1);
                color: var(--warning-color);
                border: 1px solid rgba(245, 158, 11, 0.2);
            }}

            .priority-3 {{
                background: rgba(59, 130, 246, 0.1);
                color: var(--primary-color);
                border: 1px solid rgba(59, 130, 246, 0.2);
            }}

            .priority-999 {{
                background: rgba(107, 114, 128, 0.1);
                color: var(--text-muted);
                border: 1px solid rgba(107, 114, 128, 0.2);
            }}

            .server-status {{
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }}

            .server-indicator {{
                width: 0.75rem;
                height: 0.75rem;
                border-radius: 50%;
            }}

            .server-online {{
                background: var(--success-color);
                box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
            }}

            .server-offline {{
                background: var(--danger-color);
                box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
            }}

            .server-unknown {{
                background: var(--text-muted);
                box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.2);
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <div class="header-content">
                <div>
                    <h1><i class="fas fa-info-circle"></i>Site Detail: {site_id}</h1>
                    <div class="subtitle">Comprehensive site information and status overview</div>
                </div>
                <div class="header-actions">
                    <a href="/sites/{site_id}/edit" class="nav-btn btn-primary">
                        <i class="fas fa-edit"></i>Edit Site
                    </a>
                    <a href="/sites" class="nav-btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>Back to Sites
                    </a>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="detail-grid fade-in" id="site-detail">
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    Loading site details...
                </div>
            </div>
        </div>

        <script>
            function getStatusBadge(status) {{
                const statusClass = status === 'healthy' || status === 'primary' ? 'status-healthy' :
                                  status === 'warning' || status === 'secondary' ? 'status-warning' :
                                  status === 'unhealthy' || status === 'error' ? 'status-error' : 'status-unknown';
                return `<span class="status-badge ${{statusClass}}">${{status}}</span>`;
            }}

            function getPriorityBadge(priority) {{
                const priorityText = priority === 1 ? 'Primary' :
                                   priority === 2 ? 'Secondary' :
                                   priority === 3 ? 'Tertiary' : 'Backup';
                const priorityIcon = priority === 1 ? 'fas fa-star' :
                                   priority === 2 ? 'fas fa-star-half-alt' :
                                   priority === 3 ? 'far fa-star' : 'fas fa-archive';
                return `<span class="priority-badge priority-${{priority}}"><i class="${{priorityIcon}}"></i>${{priorityText}}</span>`;
            }}

            function getServerStatus(status) {{
                const indicatorClass = status === 'online' ? 'server-online' :
                                     status === 'offline' ? 'server-offline' : 'server-unknown';
                return `<div class="server-status"><div class="server-indicator ${{indicatorClass}}"></div>${{status || 'unknown'}}</div>`;
            }}

            function formatTime(isoString) {{
                if (!isoString) return 'Never';
                return new Date(isoString).toLocaleString();
            }}

            function loadSiteDetail() {{
                fetch('/api/sites/{site_id}')
                    .then(response => response.json())
                    .then(data => {{
                        if (data.error) {{
                            document.getElementById('site-detail').innerHTML = `
                                <div class="detail-card">
                                    <div class="loading">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Error: ${{data.error}}
                                    </div>
                                </div>
                            `;
                            return;
                        }}

                        const config = data.config;
                        document.getElementById('site-detail').innerHTML = `
                            <!-- Site Overview Card -->
                            <div class="detail-card overview-card">
                                <div class="card-header">
                                    <div class="card-icon overview-icon">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div class="card-title">Site Overview</div>
                                </div>

                                <div class="detail-grid-inner">
                                    <div class="detail-item">
                                        <span class="detail-label">Site ID</span>
                                        <span class="detail-value">{site_id}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Description</span>
                                        <span class="detail-value">${{config.description || 'Not specified'}}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Status</span>
                                        ${{getStatusBadge(data.status)}}
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Priority</span>
                                        ${{getPriorityBadge(config.priority)}}
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Last Check</span>
                                        <span class="detail-value">${{formatTime(data.last_check)}}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Location Information Card -->
                            <div class="detail-card location-card">
                                <div class="card-header">
                                    <div class="card-icon location-icon">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div class="card-title">Location Information</div>
                                </div>

                                <div class="detail-grid-inner">
                                    <div class="detail-item">
                                        <span class="detail-label">Address</span>
                                        <span class="detail-value">${{config.location?.address || 'Not specified'}}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">City</span>
                                        <span class="detail-value">${{config.location?.city || 'Not specified'}}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Country</span>
                                        <span class="detail-value">${{config.location?.country || 'Not specified'}}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Timezone</span>
                                        <span class="detail-value">${{config.location?.timezone || 'UTC'}}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Network Configuration Card -->
                            <div class="detail-card network-card">
                                <div class="card-header">
                                    <div class="card-icon network-icon">
                                        <i class="fas fa-network-wired"></i>
                                    </div>
                                    <div class="card-title">Network Configuration</div>
                                </div>

                                <div class="detail-grid-inner">
                                    <div class="detail-item">
                                        <span class="detail-label">Site Network</span>
                                        <span class="detail-value">${{config.network?.site_network || 'Not configured'}}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">WAN IP</span>
                                        <span class="detail-value">${{config.network?.wan_ip || 'Not configured'}}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">VPN Endpoint</span>
                                        <span class="detail-value">${{config.network?.vpn_endpoint || 'Not configured'}}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Bandwidth</span>
                                        <span class="detail-value">${{config.network?.bandwidth || 'Not specified'}}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Server Configuration Card -->
                            <div class="detail-card servers-card">
                                <div class="card-header">
                                    <div class="card-icon servers-icon">
                                        <i class="fas fa-server"></i>
                                    </div>
                                    <div class="card-title">Server Configuration</div>
                                </div>

                                <div class="detail-grid-inner">
                                    <div class="detail-item">
                                        <span class="detail-label">Primary Server</span>
                                        <span class="detail-value">${{config.servers?.primary?.hostname || 'Not configured'}} (${{config.servers?.primary?.ip_address || 'No IP'}})</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Primary Status</span>
                                        ${{getServerStatus(data.servers?.primary?.status)}}
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Secondary Server</span>
                                        <span class="detail-value">${{config.servers?.secondary?.hostname || 'Not configured'}} (${{config.servers?.secondary?.ip_address || 'No IP'}})</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Secondary Status</span>
                                        ${{getServerStatus(data.servers?.secondary?.status)}}
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Server Type</span>
                                        <span class="detail-value">${{config.servers?.primary?.server_type || 'Not specified'}}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- EBO Configuration Card -->
                            <div class="detail-card ebo-card">
                                <div class="card-header">
                                    <div class="card-icon ebo-icon">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div class="card-title">EBO Configuration</div>
                                </div>

                                <div class="detail-grid-inner">
                                    <div class="detail-item">
                                        <span class="detail-label">Installation Path</span>
                                        <span class="detail-value">${{config.ebo_config?.installation_path || 'Default path'}}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Database Server</span>
                                        <span class="detail-value">${{config.ebo_config?.database_server || 'Not configured'}}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">License Server</span>
                                        <span class="detail-value">${{config.ebo_config?.license_server || 'Not configured'}}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Client Count</span>
                                        <span class="metric-value">${{config.ebo_config?.client_count || 0}}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">EBO Status</span>
                                        ${{getStatusBadge(data.ebo_status?.status || 'unknown')}}
                                    </div>
                                </div>
                            </div>

                            <!-- Performance Metrics Card -->
                            <div class="detail-card performance-card">
                                <div class="card-header">
                                    <div class="card-icon performance-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="card-title">Performance Metrics</div>
                                </div>

                                <div class="detail-grid-inner">
                                    <div class="detail-item">
                                        <span class="detail-label">CPU Usage</span>
                                        <span class="metric-value">${{data.performance?.cpu_usage || 0}}%</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Memory Usage</span>
                                        <span class="metric-value">${{data.performance?.memory_usage || 0}}%</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Disk Usage</span>
                                        <span class="metric-value">${{data.performance?.disk_usage || 0}}%</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Network Latency</span>
                                        <span class="metric-value">${{data.performance?.network_latency || 0}}ms</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Uptime</span>
                                        <span class="detail-value">${{data.performance?.uptime || 'Unknown'}}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    }})
                    .catch(error => {{
                        document.getElementById('site-detail').innerHTML = `
                            <div class="detail-card">
                                <div class="loading">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Error loading site details: ${{error}}
                                </div>
                            </div>
                        `;
                    }});
            }}

            // Load site details on page load
            loadSiteDetail();

            // Refresh every 30 seconds
            setInterval(loadSiteDetail, 30000);
        </script>
    </body>
    </html>
    '''

@app.route('/sites/<site_id>/edit')
def site_edit(site_id):
    """Site edit page"""
    initialize_multisite_manager()

    if site_id not in multisite_manager.sites:
        return "Site not found", 404

    return f'''
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Edit Site: {site_id}</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            :root {{
                --primary-color: #3b82f6;
                --secondary-color: #10b981;
                --accent-color: #f59e0b;
                --danger-color: #ef4444;
                --warning-color: #f59e0b;
                --success-color: #10b981;
                --info-color: #06b6d4;
                --text-primary: #1f2937;
                --text-secondary: #6b7280;
                --text-muted: #9ca3af;
                --bg-primary: #ffffff;
                --bg-secondary: #f8fafc;
                --border-color: #e5e7eb;
                --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                --radius-sm: 0.375rem;
                --radius-md: 0.5rem;
                --radius-lg: 0.75rem;
                --radius-xl: 1rem;
            }}

            * {{
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }}

            body {{
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
                background-size: 400% 400%;
                animation: gradientShift 15s ease infinite;
                min-height: 100vh;
                color: var(--text-primary);
                line-height: 1.6;
                zoom: 0.9;
            }}

            @keyframes gradientShift {{
                0% {{ background-position: 0% 50%; }}
                50% {{ background-position: 100% 50%; }}
                100% {{ background-position: 0% 50%; }}
            }}

            .header {{
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                padding: 1rem 0;
                box-shadow: var(--shadow-lg);
                margin-bottom: 2rem;
            }}

            .header-content {{
                max-width: 100%;
                margin: 0;
                padding: 0 2rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }}

            .header h1 {{
                color: var(--text-primary);
                font-size: 1.75rem;
                font-weight: 700;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-left: -0.5rem;
            }}

            .header h1 i {{
                color: var(--primary-color);
                font-size: 1.5rem;
            }}

            .subtitle {{
                color: var(--text-secondary);
                font-size: 1rem;
                margin-top: 0.5rem;
                font-weight: 400;
            }}

            .nav-btn {{
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.875rem 1.75rem;
                background: var(--secondary-color);
                color: white;
                text-decoration: none;
                border-radius: var(--radius-lg);
                font-weight: 600;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: var(--shadow-md);
                margin-right: -0.5rem;
            }}

            .nav-btn:hover {{
                background: #059669;
                transform: translateY(-2px);
                box-shadow: var(--shadow-xl);
            }}

            .container {{
                max-width: 100%;
                margin: 0;
                padding: 0 2rem;
            }}

            .edit-form {{
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: var(--radius-xl);
                padding: 2rem;
                box-shadow: var(--shadow-lg);
                border: 1px solid rgba(255, 255, 255, 0.2);
                margin-bottom: 2rem;
            }}

            .form-section {{
                margin-bottom: 2rem;
            }}

            .form-section h3 {{
                color: var(--text-primary);
                font-size: 1.25rem;
                font-weight: 600;
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }}

            .form-section h3 i {{
                color: var(--primary-color);
            }}

            .form-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 1.5rem;
            }}

            .form-group {{
                margin-bottom: 1.5rem;
            }}

            .form-label {{
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 600;
                color: var(--text-primary);
                font-size: 0.875rem;
            }}

            .form-input {{
                width: 100%;
                padding: 0.875rem 1rem;
                border: 2px solid var(--border-color);
                border-radius: var(--radius-md);
                font-size: 0.875rem;
                transition: all 0.3s ease;
                background: white;
            }}

            .form-input:focus {{
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }}

            .form-help {{
                display: block;
                margin-top: 0.5rem;
                font-size: 0.875rem;
                color: var(--warning-color);
                font-style: italic;
            }}

            .form-select {{
                width: 100%;
                padding: 0.875rem 1rem;
                border: 2px solid var(--border-color);
                border-radius: var(--radius-md);
                font-size: 0.875rem;
                background: white;
                cursor: pointer;
            }}

            .btn {{
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.875rem 1.75rem;
                border: none;
                border-radius: var(--radius-lg);
                font-weight: 600;
                font-size: 0.875rem;
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                text-decoration: none;
                text-align: center;
                box-shadow: var(--shadow-md);
            }}

            .btn-primary {{
                background: linear-gradient(135deg, var(--primary-color), #2563eb);
                color: white;
            }}

            .btn-primary:hover {{
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }}

            .btn-secondary {{
                background: var(--border-color);
                color: var(--text-secondary);
            }}

            .btn-secondary:hover {{
                background: #d1d5db;
                transform: translateY(-2px);
            }}

            .btn-success {{
                background: linear-gradient(135deg, var(--success-color), #059669);
                color: white;
            }}

            .btn-success:hover {{
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }}

            .form-actions {{
                display: flex;
                gap: 1rem;
                justify-content: flex-end;
                margin-top: 2rem;
                padding-top: 2rem;
                border-top: 1px solid var(--border-color);
            }}

            .loading {{
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 3rem;
                color: var(--text-muted);
                font-size: 1.125rem;
            }}

            .loading i {{
                margin-right: 0.5rem;
                animation: spin 1s linear infinite;
            }}

            @keyframes spin {{
                from {{ transform: rotate(0deg); }}
                to {{ transform: rotate(360deg); }}
            }}

            .alert {{
                padding: 1rem;
                border-radius: var(--radius-md);
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }}

            .alert-success {{
                background: rgba(16, 185, 129, 0.1);
                border: 1px solid rgba(16, 185, 129, 0.2);
                color: var(--success-color);
            }}

            .alert-error {{
                background: rgba(239, 68, 68, 0.1);
                border: 1px solid rgba(239, 68, 68, 0.2);
                color: var(--danger-color);
            }}

            .fade-in {{
                animation: fadeIn 0.6s ease-out;
            }}

            @keyframes fadeIn {{
                from {{
                    opacity: 0;
                    transform: translateY(20px);
                }}
                to {{
                    opacity: 1;
                    transform: translateY(0);
                }}
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <div class="header-content">
                <div>
                    <h1><i class="fas fa-edit"></i>Edit Site: {site_id}</h1>
                    <div class="subtitle">Modify site configuration and settings</div>
                </div>
                <a href="/sites" class="nav-btn"><i class="fas fa-arrow-left"></i>Back to Sites</a>
            </div>
        </div>

        <div class="container">
            <div id="message"></div>
            <div class="edit-form fade-in" id="edit-form">
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    Loading site configuration...
                </div>
            </div>
        </div>

        <script>
            let siteData = null;

            function loadSiteData() {{
                fetch('/api/sites/{site_id}')
                    .then(response => response.json())
                    .then(data => {{
                        if (data.error) {{
                            document.getElementById('edit-form').innerHTML = `
                                <div class="alert alert-error">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Error: ${{data.error}}
                                </div>
                            `;
                            return;
                        }}

                        siteData = data;
                        renderEditForm();
                    }})
                    .catch(error => {{
                        document.getElementById('edit-form').innerHTML = `
                            <div class="alert alert-error">
                                <i class="fas fa-exclamation-triangle"></i>
                                Error loading site data: ${{error}}
                            </div>
                        `;
                    }});
            }}

            function renderEditForm() {{
                const config = siteData.config;
                document.getElementById('edit-form').innerHTML = `
                    <form id="siteEditForm">
                        <!-- Basic Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-info-circle"></i>Basic Information</h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">Site ID</label>
                                    <input type="text" class="form-input" id="site_id" value="{site_id}" required>
                                    <small class="form-help">⚠️ Changing Site ID will create a new site and remove the old one</small>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Description</label>
                                    <input type="text" class="form-input" id="description" value="${{config.description || ''}}" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Priority</label>
                                    <select class="form-select" id="priority">
                                        <option value="1" ${{config.priority === 1 ? 'selected' : ''}}>Primary (1)</option>
                                        <option value="2" ${{config.priority === 2 ? 'selected' : ''}}>Secondary (2)</option>
                                        <option value="3" ${{config.priority === 3 ? 'selected' : ''}}>Tertiary (3)</option>
                                        <option value="999" ${{config.priority === 999 ? 'selected' : ''}}>Backup (999)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-map-marker-alt"></i>Location</h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">Address</label>
                                    <input type="text" class="form-input" id="address" value="${{config.location?.address || ''}}" placeholder="Street address">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">City</label>
                                    <input type="text" class="form-input" id="city" value="${{config.location?.city || ''}}" placeholder="City">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Country</label>
                                    <input type="text" class="form-input" id="country" value="${{config.location?.country || ''}}" placeholder="Country">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Timezone</label>
                                    <select class="form-select" id="timezone">
                                        <option value="UTC" ${{config.location?.timezone === 'UTC' ? 'selected' : ''}}>UTC</option>
                                        <option value="America/New_York" ${{config.location?.timezone === 'America/New_York' ? 'selected' : ''}}>Eastern Time</option>
                                        <option value="America/Chicago" ${{config.location?.timezone === 'America/Chicago' ? 'selected' : ''}}>Central Time</option>
                                        <option value="America/Denver" ${{config.location?.timezone === 'America/Denver' ? 'selected' : ''}}>Mountain Time</option>
                                        <option value="America/Los_Angeles" ${{config.location?.timezone === 'America/Los_Angeles' ? 'selected' : ''}}>Pacific Time</option>
                                        <option value="Europe/London" ${{config.location?.timezone === 'Europe/London' ? 'selected' : ''}}>London</option>
                                        <option value="Europe/Paris" ${{config.location?.timezone === 'Europe/Paris' ? 'selected' : ''}}>Paris</option>
                                        <option value="Asia/Tokyo" ${{config.location?.timezone === 'Asia/Tokyo' ? 'selected' : ''}}>Tokyo</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Network Configuration -->
                        <div class="form-section">
                            <h3><i class="fas fa-network-wired"></i>Network Configuration</h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">Site Network</label>
                                    <input type="text" class="form-input" id="site_network" value="${{config.network?.site_network || ''}}" placeholder="***********/24">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">WAN IP</label>
                                    <input type="text" class="form-input" id="wan_ip" value="${{config.network?.wan_ip || ''}}" placeholder="Public IP address">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">VPN Endpoint</label>
                                    <input type="text" class="form-input" id="vpn_endpoint" value="${{config.network?.vpn_endpoint || ''}}" placeholder="VPN server address">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Bandwidth</label>
                                    <input type="text" class="form-input" id="bandwidth" value="${{config.network?.bandwidth || ''}}" placeholder="100 Mbps">
                                </div>
                            </div>
                        </div>

                        <!-- Server Configuration -->
                        <div class="form-section">
                            <h3><i class="fas fa-server"></i>Server Configuration</h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">Primary Server Hostname</label>
                                    <input type="text" class="form-input" id="primary_hostname" value="${{config.servers?.primary?.hostname || ''}}" placeholder="SERVER-PRIMARY">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Primary Server IP</label>
                                    <input type="text" class="form-input" id="primary_ip" value="${{config.servers?.primary?.ip_address || ''}}" placeholder="************">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Secondary Server Hostname</label>
                                    <input type="text" class="form-input" id="secondary_hostname" value="${{config.servers?.secondary?.hostname || ''}}" placeholder="SERVER-SECONDARY">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Secondary Server IP</label>
                                    <input type="text" class="form-input" id="secondary_ip" value="${{config.servers?.secondary?.ip_address || ''}}" placeholder="************">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Server Type</label>
                                    <select class="form-select" id="server_type">
                                        <option value="Dell PowerEdge R750xa" ${{config.servers?.primary?.server_type === 'Dell PowerEdge R750xa' ? 'selected' : ''}}>Dell PowerEdge R750xa</option>
                                        <option value="Dell PowerEdge R740" ${{config.servers?.primary?.server_type === 'Dell PowerEdge R740' ? 'selected' : ''}}>Dell PowerEdge R740</option>
                                        <option value="HP ProLiant DL380" ${{config.servers?.primary?.server_type === 'HP ProLiant DL380' ? 'selected' : ''}}>HP ProLiant DL380</option>
                                        <option value="Virtual Machine" ${{config.servers?.primary?.server_type === 'Virtual Machine' ? 'selected' : ''}}>Virtual Machine</option>
                                        <option value="Other" ${{config.servers?.primary?.server_type === 'Other' ? 'selected' : ''}}>Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- EBO Configuration -->
                        <div class="form-section">
                            <h3><i class="fas fa-building"></i>EBO Configuration</h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">Installation Path</label>
                                    <input type="text" class="form-input" id="ebo_installation_path" value="${{config.ebo_config?.installation_path || 'C:\\\\Program Files\\\\Schneider Electric\\\\EcoStruxure Building Operation'}}" placeholder="C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Database Server</label>
                                    <input type="text" class="form-input" id="database_server" value="${{config.ebo_config?.database_server || ''}}" placeholder="Database server IP">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">License Server</label>
                                    <input type="text" class="form-input" id="license_server" value="${{config.ebo_config?.license_server || ''}}" placeholder="License server IP">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Client Count</label>
                                    <input type="number" class="form-input" id="client_count" value="${{config.ebo_config?.client_count || 5}}" min="1" max="100">
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" onclick="window.location.href='/sites'">
                                <i class="fas fa-times"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i>Save Changes
                            </button>
                        </div>
                    </form>
                `;

                // Add form submit handler
                document.getElementById('siteEditForm').addEventListener('submit', saveSite);
            }}

            function saveSite(e) {{
                e.preventDefault();

                const newSiteId = document.getElementById('site_id').value;
                const originalSiteId = '{site_id}';

                const updatedConfig = {{
                    description: document.getElementById('description').value,
                    priority: parseInt(document.getElementById('priority').value),
                    location: {{
                        address: document.getElementById('address').value,
                        city: document.getElementById('city').value,
                        country: document.getElementById('country').value,
                        timezone: document.getElementById('timezone').value
                    }},
                    network: {{
                        site_network: document.getElementById('site_network').value,
                        wan_ip: document.getElementById('wan_ip').value,
                        vpn_endpoint: document.getElementById('vpn_endpoint').value,
                        bandwidth: document.getElementById('bandwidth').value
                    }},
                    servers: {{
                        primary: {{
                            hostname: document.getElementById('primary_hostname').value,
                            ip_address: document.getElementById('primary_ip').value,
                            server_type: document.getElementById('server_type').value
                        }},
                        secondary: {{
                            hostname: document.getElementById('secondary_hostname').value,
                            ip_address: document.getElementById('secondary_ip').value,
                            server_type: document.getElementById('server_type').value
                        }}
                    }},
                    ebo_config: {{
                        installation_path: document.getElementById('ebo_installation_path').value,
                        database_server: document.getElementById('database_server').value,
                        license_server: document.getElementById('license_server').value,
                        client_count: parseInt(document.getElementById('client_count').value)
                    }}
                }};

                // Check if site ID changed
                if (newSiteId !== originalSiteId) {{
                    if (!confirm(`⚠️ Changing Site ID from "${{originalSiteId}}" to "${{newSiteId}}" will create a new site and remove the old one. Continue?`)) {{
                        return;
                    }}
                }}

                const requestData = {{
                    config: updatedConfig,
                    new_site_id: newSiteId !== originalSiteId ? newSiteId : null
                }};

                fetch('/api/sites/{site_id}/update', {{
                    method: 'PUT',
                    headers: {{
                        'Content-Type': 'application/json'
                    }},
                    body: JSON.stringify(requestData)
                }})
                .then(response => response.json())
                .then(data => {{
                    if (data.success) {{
                        const message = newSiteId !== originalSiteId ?
                            `Site ID changed from "${{originalSiteId}}" to "${{newSiteId}}" successfully!` :
                            'Site updated successfully!';

                        document.getElementById('message').innerHTML = `
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                ${{message}} Redirecting to sites overview...
                            </div>
                        `;
                        setTimeout(() => {{
                            window.location.href = '/sites';
                        }}, 2000);
                    }} else {{
                        document.getElementById('message').innerHTML = `
                            <div class="alert alert-error">
                                <i class="fas fa-exclamation-triangle"></i>
                                Error: ${{data.error || 'Unknown error'}}
                            </div>
                        `;
                    }}
                }})
                .catch(error => {{
                    document.getElementById('message').innerHTML = `
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-triangle"></i>
                            Error saving site: ${{error}}
                        </div>
                    `;
                }});
            }}

            // Load site data on page load
            loadSiteData();
        </script>
    </body>
    </html>
    '''

@app.route('/api/sites/<site_id>')
def api_site_detail(site_id):
    """API endpoint for site detail"""
    initialize_multisite_manager()

    if site_id not in multisite_manager.sites:
        return jsonify({'error': 'Site not found'}), 404

    site_data = multisite_manager.sites[site_id]

    return jsonify({
        'id': site_id,
        'config': site_data['config'],
        'status': site_data.get('status', 'unknown'),
        'servers': site_data.get('servers', {}),
        'ebo_status': site_data.get('ebo_status', {}),
        'performance': site_data.get('performance', {}),
        'last_check': site_data.get('last_check').isoformat() if site_data.get('last_check') else None
    })

@app.route('/api/sites/<site_id>/update', methods=['PUT'])
@permission_required(Permission.EDIT_SITES)
def api_update_site(site_id):
    """API endpoint to update a site"""
    initialize_multisite_manager()
    log_user_action("UPDATE_SITE", "site_edit", f"Updated site {site_id}")

    try:
        data = request.get_json()
        updated_config = data.get('config')
        new_site_id = data.get('new_site_id')

        if not updated_config:
            return jsonify({'success': False, 'error': 'Missing config data'}), 400

        if site_id not in multisite_manager.sites:
            return jsonify({'success': False, 'error': 'Site not found'}), 404

        # Handle site ID change
        if new_site_id and new_site_id != site_id:
            # Check if new site ID already exists
            if new_site_id in multisite_manager.sites:
                return jsonify({'success': False, 'error': f'Site ID "{new_site_id}" already exists'}), 400

            # Create new site with new ID
            old_site_data = multisite_manager.sites[site_id].copy()
            old_site_data['config'] = updated_config
            multisite_manager.sites[new_site_id] = old_site_data

            # Update current primary site if this was the primary
            if multisite_manager.current_primary_site == site_id:
                multisite_manager.current_primary_site = new_site_id
                multisite_manager.sites[new_site_id]['status'] = 'primary'
                log_user_action("PRIMARY_SITE_UPDATED", "site_edit", f"Primary site updated from {site_id} to {new_site_id}")

            # Update configuration file to reflect the new primary site
            if hasattr(multisite_manager, 'config') and multisite_manager.config.get('multisite_config', {}).get('primary_site') == site_id:
                multisite_manager.config['multisite_config']['primary_site'] = new_site_id
                # Save the updated configuration
                try:
                    multisite_manager._save_configuration()
                except Exception as e:
                    print(f"Warning: Could not save configuration: {e}")

            # Remove old site
            del multisite_manager.sites[site_id]

            log_user_action("SITE_ID_CHANGED", "site_edit", f"Changed site ID from {site_id} to {new_site_id}")

            return jsonify({'success': True, 'message': f'Site ID changed from {site_id} to {new_site_id} successfully'})
        else:
            # Just update the existing site configuration
            multisite_manager.sites[site_id]['config'] = updated_config

            # Save the updated configuration (in a real implementation, this would save to file/database)
            # For now, we'll just update the in-memory configuration

            return jsonify({'success': True, 'message': f'Site {site_id} updated successfully'})

    except Exception as e:
        log_user_action("UPDATE_SITE_FAILED", "site_edit", f"Failed to update site {site_id}: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/replication')
def replication_status():
    """Replication status page"""
    return '''
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>EBO Multi-Site Redundancy - Replication Status</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            :root {
                --primary-color: #3b82f6;
                --secondary-color: #10b981;
                --accent-color: #f59e0b;
                --danger-color: #ef4444;
                --warning-color: #f59e0b;
                --success-color: #10b981;
                --info-color: #06b6d4;
                --text-primary: #1f2937;
                --text-secondary: #6b7280;
                --text-muted: #9ca3af;
                --bg-primary: #ffffff;
                --bg-secondary: #f8fafc;
                --border-color: #e5e7eb;
                --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                --radius-sm: 0.375rem;
                --radius-md: 0.5rem;
                --radius-lg: 0.75rem;
                --radius-xl: 1rem;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
                background-size: 400% 400%;
                animation: gradientShift 15s ease infinite;
                min-height: 100vh;
                color: var(--text-primary);
                line-height: 1.6;
                zoom: 0.9;
            }

            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            .header {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                padding: 1rem 0;
                box-shadow: var(--shadow-lg);
                margin-bottom: 2rem;
            }

            .header-content {
                max-width: 100%;
                margin: 0;
                padding: 0 2rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .header h1 {
                color: var(--text-primary);
                font-size: 1.75rem;
                font-weight: 700;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-left: -0.5rem;
            }

            .header h1 i {
                color: var(--info-color);
                font-size: 1.5rem;
            }

            .subtitle {
                color: var(--text-secondary);
                font-size: 1rem;
                margin-top: 0.5rem;
                font-weight: 400;
            }

            .nav-btn {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.875rem 1.75rem;
                background: var(--primary-color);
                color: white;
                text-decoration: none;
                border-radius: var(--radius-lg);
                font-weight: 600;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: var(--shadow-md);
                margin-right: -0.5rem;
            }

            .nav-btn:hover {
                background: #2563eb;
                transform: translateY(-2px);
                box-shadow: var(--shadow-xl);
            }

            .container {
                max-width: 100%;
                margin: 0;
                padding: 0 2rem;
            }

            .replication-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                gap: 2rem;
                margin-bottom: 2rem;
            }

            .replication-card {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: var(--radius-xl);
                padding: 2rem;
                box-shadow: var(--shadow-lg);
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }

            .replication-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
                border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            }

            .replication-card:hover {
                transform: translateY(-4px);
                box-shadow: var(--shadow-xl);
            }

            .card-header {
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .card-icon {
                width: 3rem;
                height: 3rem;
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.25rem;
                color: white;
            }

            .database-icon {
                background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            }

            .file-icon {
                background: linear-gradient(135deg, #10b981, #059669);
            }

            .card-title {
                font-size: 1.5rem;
                font-weight: 700;
                color: var(--text-primary);
            }

            .status-grid {
                display: grid;
                gap: 1rem;
            }

            .status-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem;
                background: rgba(248, 250, 252, 0.8);
                border-radius: var(--radius-md);
                border: 1px solid var(--border-color);
            }

            .status-label {
                font-weight: 600;
                color: var(--text-primary);
            }

            .status-value {
                font-weight: 500;
                color: var(--text-secondary);
            }

            .status-badge {
                padding: 0.375rem 0.75rem;
                border-radius: var(--radius-md);
                font-size: 0.875rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.025em;
            }

            .status-healthy {
                background: rgba(16, 185, 129, 0.1);
                color: var(--success-color);
                border: 1px solid rgba(16, 185, 129, 0.2);
            }

            .status-warning {
                background: rgba(245, 158, 11, 0.1);
                color: var(--warning-color);
                border: 1px solid rgba(245, 158, 11, 0.2);
            }

            .status-error {
                background: rgba(239, 68, 68, 0.1);
                color: var(--danger-color);
                border: 1px solid rgba(239, 68, 68, 0.2);
            }

            .loading {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 3rem;
                color: var(--text-muted);
                font-size: 1.125rem;
            }

            .loading i {
                margin-right: 0.5rem;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .fade-in {
                animation: fadeIn 0.6s ease-out;
            }

            @keyframes fadeIn {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .metric-value {
                font-size: 1.25rem;
                font-weight: 700;
                color: var(--primary-color);
            }

            .sync-indicator {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 1rem;
                background: rgba(16, 185, 129, 0.1);
                border: 1px solid rgba(16, 185, 129, 0.2);
                border-radius: var(--radius-md);
                color: var(--success-color);
                font-weight: 600;
                font-size: 0.875rem;
            }

            .sync-indicator i {
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="header-content">
                <div>
                    <h1><i class="fas fa-sync-alt"></i>Replication Status</h1>
                    <div class="subtitle">Real-time monitoring of database and file replication across sites</div>
                </div>
                <a href="/" class="nav-btn"><i class="fas fa-home"></i>Dashboard</a>
            </div>
        </div>

        <div class="container">
            <div class="replication-grid fade-in" id="replication-status">
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    Loading replication status...
                </div>
            </div>
        </div>

        <script>
            function getStatusBadge(status) {
                const statusClass = status === 'healthy' ? 'status-healthy' :
                                  status === 'warning' ? 'status-warning' : 'status-error';
                return `<span class="status-badge ${statusClass}">${status}</span>`;
            }

            function formatTime(isoString) {
                return new Date(isoString).toLocaleString();
            }

            function loadReplicationStatus() {
                fetch('/api/replication/status')
                    .then(response => response.json())
                    .then(data => {
                        const container = document.getElementById('replication-status');
                        container.innerHTML = `
                            <!-- Database Replication Card -->
                            <div class="replication-card">
                                <div class="card-header">
                                    <div class="card-icon database-icon">
                                        <i class="fas fa-database"></i>
                                    </div>
                                    <div class="card-title">Database Replication</div>
                                </div>

                                <div class="status-grid">
                                    <div class="status-item">
                                        <span class="status-label">Status</span>
                                        ${getStatusBadge(data.database_replication.status)}
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Method</span>
                                        <span class="status-value">${data.database_replication.method.replace('_', ' ').toUpperCase()}</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Last Sync</span>
                                        <span class="status-value">${formatTime(data.database_replication.last_sync)}</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Replication Lag</span>
                                        <span class="metric-value">${data.database_replication.lag_seconds}s</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Sync Status</span>
                                        <div class="sync-indicator">
                                            <i class="fas fa-sync-alt"></i>
                                            Synchronized
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- File Replication Card -->
                            <div class="replication-card">
                                <div class="card-header">
                                    <div class="card-icon file-icon">
                                        <i class="fas fa-folder-open"></i>
                                    </div>
                                    <div class="card-title">File Replication</div>
                                </div>

                                <div class="status-grid">
                                    <div class="status-item">
                                        <span class="status-label">Status</span>
                                        ${getStatusBadge(data.file_replication.status)}
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Method</span>
                                        <span class="status-value">${data.file_replication.method.toUpperCase()}</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Last Sync</span>
                                        <span class="status-value">${formatTime(data.file_replication.last_sync)}</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Pending Files</span>
                                        <span class="metric-value">${data.file_replication.pending_files}</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Sync Status</span>
                                        <div class="sync-indicator">
                                            <i class="fas fa-check-circle"></i>
                                            Up to Date
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    })
                    .catch(error => {
                        console.error('Error loading replication status:', error);
                        document.getElementById('replication-status').innerHTML = `
                            <div class="loading">
                                <i class="fas fa-exclamation-triangle"></i>
                                Error loading replication status
                            </div>
                        `;
                    });
            }

            // Load initial data
            loadReplicationStatus();

            // Refresh every 30 seconds
            setInterval(loadReplicationStatus, 30000);
        </script>
    </body>
    </html>
    '''

@app.route('/api/replication/status')
def api_replication_status():
    """API endpoint for replication status"""
    # This would return actual replication status
    return jsonify({
        'database_replication': {
            'enabled': True,
            'method': 'sql_server_always_on',
            'status': 'healthy',
            'last_sync': datetime.now().isoformat(),
            'lag_seconds': 2.5
        },
        'file_replication': {
            'enabled': True,
            'method': 'dfsr',
            'status': 'healthy',
            'last_sync': datetime.now().isoformat(),
            'pending_files': 0
        }
    })

@app.route('/failover-cluster')
@login_required
def failover_cluster_config():
    """Failover cluster configuration page"""
    log_user_action("VIEW_CLUSTER_CONFIG", "cluster", "Viewed failover cluster configuration")
    return render_template('failover_cluster_config.html')

@app.route('/safekit-cluster')
@login_required
def safekit_cluster_dashboard():
    """SafeKit-style cluster dashboard"""
    log_user_action("VIEW_SAFEKIT_CLUSTER", "cluster", "Viewed SafeKit-style cluster dashboard")
    return render_template('safekit_cluster_dashboard.html')

@app.route('/safekit-console')
@login_required
def safekit_web_console():
    """SafeKit web console - comprehensive management interface"""
    log_user_action("VIEW_SAFEKIT_CONSOLE", "cluster", "Viewed SafeKit web console")
    return render_template('safekit_web_console.html')

@app.route('/network')
@login_required
def network_topology():
    """Network topology page"""
    return '''
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Network Topology</title>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <style>
            :root {
                --primary-color: #3b82f6;
                --primary-dark: #1d4ed8;
                --secondary-color: #10b981;
                --danger-color: #ef4444;
                --warning-color: #f59e0b;
                --success-color: #22c55e;
                --info-color: #06b6d4;
                --dark-color: #1f2937;
                --light-color: #f8fafc;
                --border-color: #e2e8f0;
                --text-primary: #1e293b;
                --text-secondary: #64748b;
                --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
                --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
                --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
                --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
                --radius-sm: 0.375rem;
                --radius-md: 0.5rem;
                --radius-lg: 0.75rem;
                --radius-xl: 1rem;
            }

            * { margin: 0; padding: 0; box-sizing: border-box; }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
                background-size: 400% 400%;
                animation: gradientShift 15s ease infinite;
                min-height: 100vh;
                color: var(--text-primary);
                line-height: 1.6;
                zoom: 0.9;
            }

            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            .header {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                padding: 1rem 0;
                box-shadow: var(--shadow-lg);
                margin-bottom: 2rem;
            }

            .header-content {
                max-width: 1400px;
                margin: 0 auto;
                padding: 0 1rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .header h1 {
                color: var(--text-primary);
                font-size: 1.75rem;
                font-weight: 700;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-left: -0.5rem;
            }

            .header h1 i {
                color: var(--info-color);
                font-size: 1.5rem;
            }

            .nav-btn {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.875rem 1.75rem;
                background: var(--primary-color);
                color: white;
                text-decoration: none;
                border-radius: var(--radius-lg);
                font-weight: 600;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: var(--shadow-md);
                margin-right: -0.5rem;
            }

            .nav-btn:hover {
                background: var(--primary-dark);
                transform: translateY(-3px);
                box-shadow: var(--shadow-lg);
            }

            .container {
                max-width: 100%;
                margin: 0;
                padding: 0 2rem;
            }

            .topology-container {
                background: rgba(255, 255, 255, 0.95);
                border-radius: var(--radius-xl);
                padding: 2rem;
                box-shadow: var(--shadow-xl);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                margin-bottom: 2rem;
            }

            .sites-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-top: 2rem;
            }

            .site-node {
                background: rgba(255, 255, 255, 0.9);
                border-radius: var(--radius-lg);
                padding: 1.5rem;
                box-shadow: var(--shadow-md);
                border: 2px solid var(--border-color);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }

            .site-node::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: var(--border-color);
                transition: all 0.3s ease;
            }

            .site-node.primary::before {
                background: linear-gradient(90deg, var(--success-color), #16a34a);
            }

            .site-node.healthy::before {
                background: linear-gradient(90deg, var(--info-color), #0891b2);
            }

            .site-node.unhealthy::before {
                background: linear-gradient(90deg, var(--danger-color), #dc2626);
            }

            .site-node:hover {
                transform: translateY(-4px);
                box-shadow: var(--shadow-lg);
            }

            .site-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
            }

            .site-name {
                font-size: 1.25rem;
                font-weight: 600;
                color: var(--text-primary);
            }

            .site-status {
                padding: 0.25rem 0.75rem;
                border-radius: 9999px;
                font-size: 0.75rem;
                font-weight: 600;
                text-transform: uppercase;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .site-status.primary {
                background: var(--success-color);
                color: white;
            }

            .site-status.healthy {
                background: var(--info-color);
                color: white;
            }

            .site-status.unhealthy {
                background: var(--danger-color);
                color: white;
            }

            .site-status.unknown {
                background: var(--text-secondary);
                color: white;
            }

            .site-info {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .site-info-item {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                color: var(--text-secondary);
                font-size: 0.875rem;
            }

            .site-info-item i {
                color: var(--primary-color);
                width: 1rem;
                text-align: center;
            }

            .connections-info {
                background: var(--light-color);
                border-radius: var(--radius-lg);
                padding: 1.5rem;
                margin-top: 2rem;
                border: 1px solid var(--border-color);
            }

            .connection-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.75rem 0;
                border-bottom: 1px solid var(--border-color);
            }

            .connection-item:last-child {
                border-bottom: none;
            }

            .connection-status {
                padding: 0.25rem 0.75rem;
                border-radius: 9999px;
                font-size: 0.75rem;
                font-weight: 600;
                background: var(--success-color);
                color: white;
            }

            .loading {
                text-align: center;
                padding: 4rem;
                color: var(--text-secondary);
                font-size: 1.125rem;
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .fade-in {
                animation: fadeInUp 0.6s ease-out;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="header-content">
                <h1><i class="fas fa-network-wired"></i>Network Topology</h1>
                <a href="/" class="nav-btn"><i class="fas fa-home"></i>Dashboard</a>
            </div>
        </div>

        <div class="container">
            <div class="topology-container fade-in">
                <h2>Multi-Site Network Overview</h2>
                <div id="network-topology" class="sites-grid">
                    <div class="loading">Loading network topology...</div>
                </div>

                <div id="connections-info" class="connections-info" style="display: none;">
                    <h3>Site Connections</h3>
                    <div id="connections-list"></div>
                </div>
            </div>
        </div>

        <script>
            function getStatusIcon(status) {
                switch(status) {
                    case 'primary': return 'fas fa-crown';
                    case 'healthy': return 'fas fa-check-circle';
                    case 'unhealthy': return 'fas fa-exclamation-triangle';
                    default: return 'fas fa-question-circle';
                }
            }

            fetch('/api/network/topology')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('network-topology');
                    const connectionsInfo = document.getElementById('connections-info');
                    const connectionsList = document.getElementById('connections-list');

                    container.innerHTML = '';

                    // Display sites
                    Object.values(data.sites).forEach((site, index) => {
                        const siteNode = document.createElement('div');
                        siteNode.className = `site-node ${site.status} fade-in`;
                        siteNode.style.animationDelay = `${index * 0.1}s`;

                        const statusIcon = getStatusIcon(site.status);

                        siteNode.innerHTML = `
                            <div class="site-header">
                                <div class="site-name">${site.name}</div>
                                <div class="site-status ${site.status}">
                                    <i class="${statusIcon}"></i>
                                    ${site.status}
                                </div>
                            </div>

                            <div class="site-info">
                                <div class="site-info-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>${site.location.city || 'Unknown'}, ${site.location.country || 'Unknown'}</span>
                                </div>
                                <div class="site-info-item">
                                    <i class="fas fa-network-wired"></i>
                                    <span>${site.network.site_network || 'Network not configured'}</span>
                                </div>
                                <div class="site-info-item">
                                    <i class="fas fa-globe"></i>
                                    <span>${site.network.wan_ip || 'WAN IP not configured'}</span>
                                </div>
                                <div class="site-info-item">
                                    <i class="fas fa-clock"></i>
                                    <span>${site.location.timezone || 'Timezone not set'}</span>
                                </div>
                            </div>
                        `;

                        container.appendChild(siteNode);
                    });

                    // Display connections
                    if (data.connections && data.connections.length > 0) {
                        connectionsInfo.style.display = 'block';

                        data.connections.forEach(connection => {
                            const connectionItem = document.createElement('div');
                            connectionItem.className = 'connection-item';

                            connectionItem.innerHTML = `
                                <div>
                                    <strong>${data.sites[connection.from].name}</strong>
                                    <i class="fas fa-arrows-alt-h" style="margin: 0 0.5rem; color: var(--primary-color);"></i>
                                    <strong>${data.sites[connection.to].name}</strong>
                                </div>
                                <div class="connection-status">${connection.status}</div>
                            `;

                            connectionsList.appendChild(connectionItem);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading network topology:', error);
                    document.getElementById('network-topology').innerHTML = '<div class="loading">Error loading network topology</div>';
                });
        </script>
    </body>
    </html>
    '''

@app.route('/api/network/topology')
def api_network_topology():
    """API endpoint for network topology"""
    initialize_multisite_manager()

    # Build network topology data
    topology = {
        'sites': {},
        'connections': []
    }

    for site_id, site_data in multisite_manager.sites.items():
        topology['sites'][site_id] = {
            'id': site_id,
            'name': site_data['config'].get('description', site_id),
            'network': site_data['config'].get('network', {}),
            'location': site_data['config'].get('location', {}),
            'status': site_data.get('status', 'unknown')
        }

    # Add connections between sites (simplified)
    site_ids = list(multisite_manager.sites.keys())
    for i, site1 in enumerate(site_ids):
        for site2 in site_ids[i+1:]:
            topology['connections'].append({
                'from': site1,
                'to': site2,
                'type': 'vpn',
                'status': 'active'
            })

    return jsonify(topology)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5002, debug=True)
