backup:
  backup_path: D:\EBO_Backup
  enabled: true
  retention_days: 30
  schedule: daily
client_pcs:
- ip: YOUR_CLIENT_IP_1
  name: EBO-Client-01
- ip: YOUR_CLIENT_IP_2
  name: EBO-Client-02
- ip: YOUR_CLIENT_IP_3
  name: EBO-Client-03
- ip: YOUR_CLIENT_IP_4
  name: EBO-Client-04
- ip: YOUR_CLIENT_IP_5
  name: EBO-Client-05
deployment:
  auto_start: true
  environment: production
  install_path: C:\EBO_Redundancy_Production
  log_level: INFO
  service_name: EBO_Redundancy_Service
monitoring:
  alert_recipients:
  - <EMAIL>
  check_interval: 30
  email_alerts: true
  email_server: YOUR_SMTP_SERVER
  failover_threshold: 3
  recovery_threshold: 2
network:
  api_port: 5002
  domain: YOUR_DOMAIN.local
  monitoring_port: 5001
  subnet: YOUR_SUBNET
  virtual_ip: YOUR_VIRTUAL_IP
servers:
  primary:
    database_path: C:\ProgramData\Schneider Electric\EcoStruxure Building Operation\Database
    ebo_installation_path: C:\Program Files\Schneider Electric\EcoStruxure Building
      Operation
    hostname: YOUR_PRIMARY_SERVER
    ip_address: YOUR_PRIMARY_IP
  secondary:
    database_path: C:\ProgramData\Schneider Electric\EcoStruxure Building Operation\Database
    ebo_installation_path: C:\Program Files\Schneider Electric\EcoStruxure Building
      Operation
    hostname: YOUR_SECONDARY_SERVER
    ip_address: YOUR_SECONDARY_IP
