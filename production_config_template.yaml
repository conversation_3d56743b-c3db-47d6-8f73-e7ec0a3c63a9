# EBO Redundancy System - Production Configuration Template
# IMPORTANT: Replace all placeholder values with your actual environment details

deployment:
  environment: 'production'
  install_path: 'C:\EBO_Redundancy_Production'
  service_name: 'EBO_Redundancy_Service'
  auto_start: true
  log_level: 'INFO'
  
# ============================================================================
# SERVER CONFIGURATION - UPDATE WITH YOUR ACTUAL SERVER DETAILS
# ============================================================================
servers:
  primary:
    hostname: 'YOUR_PRIMARY_SERVER_NAME'           # e.g., 'EBO-PRIMARY-01'
    ip_address: 'YOUR_PRIMARY_SERVER_IP'           # e.g., '*********'
    domain: 'YOUR_DOMAIN.local'                    # e.g., 'yourcompany.local'
    ebo_installation_path: 'C:\Program Files\Schneider Electric\EcoStruxure Building Operation'
    database_path: 'C:\ProgramData\Schneider Electric\EcoStruxure Building Operation\Database'
    backup_path: 'D:\EBO_Backup'
    
  secondary:
    hostname: 'YOUR_SECONDARY_SERVER_NAME'         # e.g., 'EBO-SECONDARY-01'
    ip_address: 'YOUR_SECONDARY_SERVER_IP'         # e.g., '*********'
    domain: 'YOUR_DOMAIN.local'                    # e.g., 'yourcompany.local'
    ebo_installation_path: 'C:\Program Files\Schneider Electric\EcoStruxure Building Operation'
    database_path: 'C:\ProgramData\Schneider Electric\EcoStruxure Building Operation\Database'
    backup_path: 'D:\EBO_Backup'

# ============================================================================
# NETWORK CONFIGURATION - UPDATE WITH YOUR NETWORK DETAILS
# ============================================================================
network:
  virtual_ip: 'YOUR_VIRTUAL_IP'                   # e.g., '*********0'
  subnet: 'YOUR_SUBNET'                           # e.g., '********/24'
  gateway: 'YOUR_GATEWAY'                         # e.g., '********'
  dns_servers:
    - 'YOUR_DNS_SERVER_1'                         # e.g., '********'
    - 'YOUR_DNS_SERVER_2'                         # e.g., '********'
  domain: 'YOUR_DOMAIN.local'                     # e.g., 'yourcompany.local'
  monitoring_port: 5001
  api_port: 5002
  
# ============================================================================
# CLIENT PC CONFIGURATION - ADD ALL YOUR EBO CLIENT WORKSTATIONS
# ============================================================================
client_pcs:
  - name: 'YOUR_CLIENT_PC_1'                      # e.g., 'EBO-Workstation-01'
    ip: 'YOUR_CLIENT_IP_1'                        # e.g., '*********'
    user: 'YOUR_CLIENT_USER_1'                    # e.g., 'operator1'
    location: 'YOUR_LOCATION_1'                   # e.g., 'Control Room'
    
  - name: 'YOUR_CLIENT_PC_2'                      # e.g., 'EBO-Workstation-02'
    ip: 'YOUR_CLIENT_IP_2'                        # e.g., '*********'
    user: 'YOUR_CLIENT_USER_2'                    # e.g., 'operator2'
    location: 'YOUR_LOCATION_2'                   # e.g., 'Engineering Office'
    
  - name: 'YOUR_CLIENT_PC_3'                      # e.g., 'EBO-Workstation-03'
    ip: 'YOUR_CLIENT_IP_3'                        # e.g., '*********'
    user: 'YOUR_CLIENT_USER_3'                    # e.g., 'maintenance'
    location: 'YOUR_LOCATION_3'                   # e.g., 'Maintenance Shop'
    
  - name: 'YOUR_CLIENT_PC_4'                      # e.g., 'EBO-Workstation-04'
    ip: 'YOUR_CLIENT_IP_4'                        # e.g., '*********'
    user: 'YOUR_CLIENT_USER_4'                    # e.g., 'supervisor'
    location: 'YOUR_LOCATION_4'                   # e.g., 'Supervisor Office'
    
  - name: 'YOUR_CLIENT_PC_5'                      # e.g., 'EBO-Workstation-05'
    ip: 'YOUR_CLIENT_IP_5'                        # e.g., '*********'
    user: 'YOUR_CLIENT_USER_5'                    # e.g., 'admin'
    location: 'YOUR_LOCATION_5'                   # e.g., 'IT Office'

# ============================================================================
# MONITORING CONFIGURATION - ADJUST BASED ON YOUR REQUIREMENTS
# ============================================================================
monitoring:
  check_interval: 30                              # Check every 30 seconds
  failover_threshold: 3                           # Fail after 3 consecutive failures (90 seconds)
  recovery_threshold: 2                           # Recover after 2 consecutive successes (60 seconds)
  
  # Email alert configuration
  email_alerts: true
  email_server: 'YOUR_SMTP_SERVER'                # e.g., 'mail.yourcompany.com'
  email_port: 587                                 # SMTP port (587 for TLS, 25 for plain)
  email_username: 'YOUR_SMTP_USERNAME'            # e.g., '<EMAIL>'
  email_password: 'YOUR_SMTP_PASSWORD'            # SMTP password
  email_use_tls: true                             # Use TLS encryption
  
  # Alert recipients
  alert_recipients:
    - 'YOUR_ADMIN_EMAIL'                          # e.g., '<EMAIL>'
    - 'YOUR_FACILITIES_EMAIL'                     # e.g., '<EMAIL>'
    - 'YOUR_IT_EMAIL'                             # e.g., '<EMAIL>'
  
  # Alert conditions
  alert_on_failover: true
  alert_on_recovery: true
  alert_on_service_failure: true
  alert_on_database_issues: true
  alert_on_storage_problems: true
  
  # Performance thresholds
  cpu_threshold: 85                               # Alert if CPU > 85%
  memory_threshold: 85                            # Alert if Memory > 85%
  disk_threshold: 90                              # Alert if Disk > 90%

# ============================================================================
# DATABASE CONFIGURATION - SQL SERVER SETTINGS
# ============================================================================
database:
  type: 'sqlserver'                               # SQL Server type
  instance: 'SQLEXPRESS'                          # SQL Server instance name
  port: 1433                                      # SQL Server port
  
  # Database connection settings
  connection_timeout: 30                          # Connection timeout in seconds
  command_timeout: 60                             # Command timeout in seconds
  
  # Backup settings
  backup_enabled: true
  backup_schedule: 'every 4 hours'                # Backup frequency
  backup_retention_days: 30                       # Keep backups for 30 days
  backup_compression: true                         # Enable backup compression
  backup_encryption: true                          # Enable backup encryption
  
  # Replication settings
  replication_mode: 'synchronous'                 # Synchronous or asynchronous
  replication_timeout: 10                         # Replication timeout in seconds

# ============================================================================
# STORAGE AND BACKUP CONFIGURATION
# ============================================================================
backup:
  enabled: true
  backup_path: 'D:\EBO_Backup'                    # Local backup path
  network_backup_path: '\\YOUR_BACKUP_SERVER\EBO_Backup'  # Network backup path
  retention_days: 30                              # Keep backups for 30 days
  schedule: 'daily'                               # Backup schedule
  compression: true                               # Enable compression
  encryption: true                                # Enable encryption
  
  # Backup types
  full_backup_schedule: 'weekly'                  # Full backup weekly
  incremental_backup_schedule: 'daily'            # Incremental backup daily
  log_backup_schedule: 'every 15 minutes'         # Transaction log backup

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================
security:
  # Service account for redundancy system
  service_account: 'YOUR_DOMAIN\ebo-redundancy-svc'  # e.g., 'COMPANY\ebo-redundancy-svc'
  service_password: 'YOUR_SERVICE_PASSWORD'           # Strong password for service account
  
  # Web interface security
  web_authentication: true                        # Enable web authentication
  web_ssl: true                                   # Enable HTTPS
  ssl_certificate_path: 'C:\EBO_Redundancy_Production\ssl\certificate.pfx'
  ssl_certificate_password: 'YOUR_SSL_PASSWORD'
  
  # Database security
  database_encryption: true                       # Enable database encryption
  database_backup_encryption: true                # Encrypt database backups
  
  # File system security
  config_file_encryption: true                    # Encrypt configuration files
  log_file_encryption: false                      # Encrypt log files (optional)

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================
logging:
  level: 'INFO'                                   # Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
  max_file_size: '10MB'                           # Maximum log file size
  backup_count: 10                                # Number of log files to keep
  
  # Log file locations
  system_log: 'C:\EBO_Redundancy_Production\logs\system.log'
  failover_log: 'C:\EBO_Redundancy_Production\logs\failover.log'
  monitoring_log: 'C:\EBO_Redundancy_Production\logs\monitoring.log'
  database_log: 'C:\EBO_Redundancy_Production\logs\database.log'
  
  # Log retention
  log_retention_days: 90                          # Keep logs for 90 days
  archive_old_logs: true                          # Archive old logs

# ============================================================================
# PERFORMANCE TUNING
# ============================================================================
performance:
  # System resources
  max_cpu_usage: 80                               # Maximum CPU usage percentage
  max_memory_usage: 80                            # Maximum memory usage percentage
  
  # Monitoring performance
  monitoring_threads: 4                           # Number of monitoring threads
  database_pool_size: 10                          # Database connection pool size
  
  # Network performance
  network_timeout: 30                             # Network timeout in seconds
  retry_attempts: 3                               # Number of retry attempts
  retry_delay: 5                                  # Delay between retries in seconds

# ============================================================================
# MAINTENANCE CONFIGURATION
# ============================================================================
maintenance:
  # Automatic maintenance tasks
  auto_cleanup_logs: true                         # Automatically clean up old logs
  auto_cleanup_backups: true                      # Automatically clean up old backups
  auto_update_check: true                         # Check for system updates
  
  # Maintenance schedules
  log_cleanup_schedule: 'weekly'                  # Clean up logs weekly
  backup_cleanup_schedule: 'monthly'              # Clean up backups monthly
  health_check_schedule: 'daily'                  # System health check daily
  
  # Maintenance windows
  maintenance_window_start: '02:00'               # Maintenance window start time
  maintenance_window_end: '04:00'                 # Maintenance window end time
  maintenance_day: 'Sunday'                       # Preferred maintenance day

# ============================================================================
# INTEGRATION CONFIGURATION
# ============================================================================
integration:
  # SNMP integration for monitoring systems
  snmp_enabled: false                             # Enable SNMP monitoring
  snmp_community: 'public'                        # SNMP community string
  snmp_port: 161                                  # SNMP port
  
  # Syslog integration
  syslog_enabled: false                           # Enable syslog
  syslog_server: 'YOUR_SYSLOG_SERVER'             # Syslog server address
  syslog_port: 514                                # Syslog port
  
  # REST API integration
  api_enabled: true                               # Enable REST API
  api_authentication: true                        # Require API authentication
  api_rate_limiting: true                         # Enable API rate limiting

# ============================================================================
# EXAMPLE CONFIGURATION VALUES
# ============================================================================
# Here are example values for a typical corporate environment:
#
# servers:
#   primary:
#     hostname: 'EBO-PRIMARY-01'
#     ip_address: '*********'
#     domain: 'company.local'
#   secondary:
#     hostname: 'EBO-SECONDARY-01'
#     ip_address: '*********'
#     domain: 'company.local'
#
# network:
#   virtual_ip: '*********0'
#   subnet: '********/24'
#   gateway: '********'
#   dns_servers: ['********', '********']
#   domain: 'company.local'
#
# client_pcs:
#   - name: 'EBO-Workstation-01'
#     ip: '*********'
#     user: 'operator1'
#     location: 'Control Room'
#
# monitoring:
#   email_server: 'mail.company.com'
#   alert_recipients: ['<EMAIL>', '<EMAIL>']
#
# ============================================================================
