#!/usr/bin/env python3
"""
EBO Redundancy System - Production Deployment Script
Automates the deployment and configuration for production use
"""

import os
import sys
import yaml
import shutil
import subprocess
import socket
import time
from pathlib import Path
import winreg
import psutil

class ProductionDeployment:
    """Production deployment manager for EBO redundancy system"""
    
    def __init__(self):
        self.deployment_config = {}
        self.current_dir = Path(__file__).parent
        self.production_dir = Path("C:\\EBO_Redundancy_Production")
        self.service_name = "EBO_Redundancy_Service"
        
    def load_deployment_config(self):
        """Load production deployment configuration"""
        config_file = self.current_dir / "production_config.yaml"
        
        if not config_file.exists():
            self.create_default_production_config()
            
        with open(config_file, 'r') as f:
            self.deployment_config = yaml.safe_load(f)
            
    def create_default_production_config(self):
        """Create default production configuration template"""
        default_config = {
            'deployment': {
                'environment': 'production',
                'install_path': 'C:\\EBO_Redundancy_Production',
                'service_name': 'EBO_Redundancy_Service',
                'auto_start': True,
                'log_level': 'INFO'
            },
            'servers': {
                'primary': {
                    'hostname': 'YOUR_PRIMARY_SERVER',
                    'ip_address': 'YOUR_PRIMARY_IP',
                    'ebo_installation_path': 'C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation',
                    'database_path': 'C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation\\Database'
                },
                'secondary': {
                    'hostname': 'YOUR_SECONDARY_SERVER',
                    'ip_address': 'YOUR_SECONDARY_IP', 
                    'ebo_installation_path': 'C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation',
                    'database_path': 'C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation\\Database'
                }
            },
            'network': {
                'virtual_ip': 'YOUR_VIRTUAL_IP',
                'subnet': 'YOUR_SUBNET',
                'domain': 'YOUR_DOMAIN.local',
                'monitoring_port': 5001,
                'api_port': 5002
            },
            'client_pcs': [
                {'name': 'EBO-Client-01', 'ip': 'YOUR_CLIENT_IP_1'},
                {'name': 'EBO-Client-02', 'ip': 'YOUR_CLIENT_IP_2'},
                {'name': 'EBO-Client-03', 'ip': 'YOUR_CLIENT_IP_3'},
                {'name': 'EBO-Client-04', 'ip': 'YOUR_CLIENT_IP_4'},
                {'name': 'EBO-Client-05', 'ip': 'YOUR_CLIENT_IP_5'}
            ],
            'monitoring': {
                'check_interval': 30,
                'failover_threshold': 3,
                'recovery_threshold': 2,
                'email_alerts': True,
                'email_server': 'YOUR_SMTP_SERVER',
                'alert_recipients': ['<EMAIL>']
            },
            'backup': {
                'enabled': True,
                'backup_path': 'D:\\EBO_Backup',
                'retention_days': 30,
                'schedule': 'daily'
            }
        }
        
        config_file = self.current_dir / "production_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False, indent=2)
            
        print(f"✅ Created production configuration template: {config_file}")
        print("📝 Please edit this file with your actual server details before deployment")
        
    def validate_environment(self):
        """Validate production environment requirements"""
        print("🔍 Validating production environment...")
        
        checks = []
        
        # Check Windows version
        try:
            import platform
            windows_version = platform.platform()
            if "Windows" in windows_version:
                checks.append(("Windows OS", True, windows_version))
            else:
                checks.append(("Windows OS", False, "Non-Windows OS detected"))
        except Exception as e:
            checks.append(("Windows OS", False, str(e)))
            
        # Check Python version
        try:
            python_version = sys.version
            if sys.version_info >= (3, 8):
                checks.append(("Python 3.8+", True, python_version))
            else:
                checks.append(("Python 3.8+", False, f"Python {sys.version_info} is too old"))
        except Exception as e:
            checks.append(("Python 3.8+", False, str(e)))
            
        # Check required packages
        required_packages = ['flask', 'yaml', 'psutil', 'requests']
        for package in required_packages:
            try:
                __import__(package)
                checks.append((f"Package {package}", True, "Installed"))
            except ImportError:
                checks.append((f"Package {package}", False, "Not installed"))
                
        # Check EBO installation
        ebo_paths = [
            "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation",
            "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation"
        ]
        
        for path in ebo_paths:
            if os.path.exists(path):
                checks.append((f"EBO Path {path}", True, "Found"))
            else:
                checks.append((f"EBO Path {path}", False, "Not found"))
                
        # Check administrative privileges
        try:
            is_admin = os.access(sys.executable, os.W_OK)
            checks.append(("Administrative Rights", is_admin, "Required for service installation"))
        except Exception as e:
            checks.append(("Administrative Rights", False, str(e)))
            
        # Display results
        print("\n📊 Environment Validation Results:")
        print("=" * 60)
        
        all_passed = True
        for check_name, passed, details in checks:
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{status} {check_name}: {details}")
            if not passed:
                all_passed = False
                
        print("=" * 60)
        
        if all_passed:
            print("🎉 All environment checks passed!")
            return True
        else:
            print("⚠️  Some environment checks failed. Please resolve issues before deployment.")
            return False
    
    def create_production_structure(self):
        """Create production directory structure"""
        print("📁 Creating production directory structure...")
        
        # Create main production directory
        self.production_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        subdirs = [
            'bin',           # Executable files
            'config',        # Configuration files
            'logs',          # Log files
            'templates',     # Web templates
            'static',        # Static web files
            'data',          # Data files
            'backup',        # Backup files
            'scripts'        # Utility scripts
        ]
        
        for subdir in subdirs:
            (self.production_dir / subdir).mkdir(exist_ok=True)
            print(f"  ✅ Created: {self.production_dir / subdir}")
            
    def copy_application_files(self):
        """Copy application files to production directory"""
        print("📋 Copying application files to production...")
        
        # Files to copy
        files_to_copy = [
            'redundancy_manager.py',
            'database_cluster_manager.py', 
            'storage_redundancy_manager.py',
            'ebo_redundancy_manager.py',
            'redundancy_web_ui.py',
            'base_monitor.py',
            'disk_monitor.py',
            'service_monitor.py',
            'http_monitor.py',
            'network_monitor.py',
            'port_monitor.py',
            'performance_monitor.py'
        ]
        
        # Copy Python files
        for file_name in files_to_copy:
            src_file = self.current_dir / file_name
            if src_file.exists():
                dst_file = self.production_dir / 'bin' / file_name
                shutil.copy2(src_file, dst_file)
                print(f"  ✅ Copied: {file_name}")
            else:
                print(f"  ⚠️  Missing: {file_name}")
                
        # Copy templates
        templates_dir = self.current_dir / 'templates'
        if templates_dir.exists():
            shutil.copytree(templates_dir, self.production_dir / 'templates', dirs_exist_ok=True)
            print(f"  ✅ Copied: templates directory")
            
        # Copy static files
        static_dir = self.current_dir / 'static'
        if static_dir.exists():
            shutil.copytree(static_dir, self.production_dir / 'static', dirs_exist_ok=True)
            print(f"  ✅ Copied: static directory")
            
    def create_production_config(self):
        """Create production-ready configuration files"""
        print("⚙️  Creating production configuration...")
        
        # Update EBO configuration with production values
        ebo_config = {
            'applications': {
                'ebo_enterprise_primary': {
                    'description': 'EBO Enterprise Server - Primary',
                    'priority': 1,
                    'is_primary': True,
                    'auto_failover': True,
                    'failover_threshold': self.deployment_config['monitoring']['failover_threshold'],
                    'recovery_threshold': self.deployment_config['monitoring']['recovery_threshold'],
                    'failover_target': 'ebo_enterprise_secondary',
                    'server_ip': self.deployment_config['servers']['primary']['ip_address'],
                    'server_name': self.deployment_config['servers']['primary']['hostname'],
                    'ebo_services': [
                        'EcoStruxure Building Operation Enterprise Server',
                        'EcoStruxure Building Operation Database Service',
                        'EcoStruxure Building Operation Web Service',
                        'EcoStruxure Building Operation Automation Server'
                    ]
                },
                'ebo_enterprise_secondary': {
                    'description': 'EBO Enterprise Server - Secondary',
                    'priority': 2,
                    'is_primary': False,
                    'auto_failover': True,
                    'failover_threshold': self.deployment_config['monitoring']['failover_threshold'],
                    'recovery_threshold': self.deployment_config['monitoring']['recovery_threshold'],
                    'failover_target': None,
                    'server_ip': self.deployment_config['servers']['secondary']['ip_address'],
                    'server_name': self.deployment_config['servers']['secondary']['hostname'],
                    'ebo_services': [
                        'EcoStruxure Building Operation Enterprise Server',
                        'EcoStruxure Building Operation Database Service',
                        'EcoStruxure Building Operation Web Service',
                        'EcoStruxure Building Operation Automation Server'
                    ]
                }
            },
            'network_config': {
                'virtual_ip': self.deployment_config['network']['virtual_ip'],
                'primary_server': self.deployment_config['servers']['primary']['ip_address'],
                'secondary_server': self.deployment_config['servers']['secondary']['ip_address'],
                'client_redirect_method': 'dns_update'
            },
            'ebo_settings': {
                'installation_path': self.deployment_config['servers']['primary']['ebo_installation_path'],
                'data_path': self.deployment_config['servers']['primary']['database_path'],
                'database_type': 'SQL Server Express',
                'license_server_port': 1947,
                'web_server_port': 80,
                'automation_server_port': 443,
                'client_connections': len(self.deployment_config['client_pcs'])
            },
            'client_pcs': self.deployment_config['client_pcs']
        }
        
        # Save production EBO configuration
        config_file = self.production_dir / 'config' / 'ebo_redundancy_config.yaml'
        with open(config_file, 'w') as f:
            yaml.dump(ebo_config, f, default_flow_style=False, indent=2)
            
        print(f"  ✅ Created: {config_file}")
        
    def create_windows_service(self):
        """Create Windows service for production deployment"""
        print("🔧 Creating Windows service...")
        
        # Create service script
        service_script = f"""
import sys
import os
import time
import threading
from pathlib import Path

# Add production bin directory to path
sys.path.insert(0, r'{self.production_dir}\\bin')

# Import the redundancy system
from redundancy_web_ui import app
from ebo_redundancy_manager import EBORedundancyManager

class EBORedundancyService:
    def __init__(self):
        self.running = False
        self.ebo_manager = None
        
    def start(self):
        self.running = True
        self.ebo_manager = EBORedundancyManager(r'{self.production_dir}\\config\\ebo_redundancy_config.yaml')
        
        # Start monitoring
        self.ebo_manager.start_ebo_monitoring()
        
        # Start web interface in a separate thread
        web_thread = threading.Thread(target=self.start_web_interface, daemon=True)
        web_thread.start()
        
        # Keep service running
        while self.running:
            time.sleep(1)
            
    def start_web_interface(self):
        app.run(host='0.0.0.0', port={self.deployment_config['network']['monitoring_port']}, debug=False)
        
    def stop(self):
        self.running = False
        if self.ebo_manager:
            self.ebo_manager.stop_ebo_monitoring()

if __name__ == '__main__':
    service = EBORedundancyService()
    try:
        service.start()
    except KeyboardInterrupt:
        service.stop()
"""
        
        service_file = self.production_dir / 'bin' / 'ebo_redundancy_service.py'
        with open(service_file, 'w') as f:
            f.write(service_script)
            
        print(f"  ✅ Created service script: {service_file}")
        
        # Create service installer batch file
        installer_script = f"""
@echo off
echo Installing EBO Redundancy Service...

sc create "{self.service_name}" binPath= "python.exe {service_file}" start= auto
sc description "{self.service_name}" "EcoStruxure Building Operation Redundancy Management Service"

echo Service installed successfully!
echo Use 'sc start {self.service_name}' to start the service
echo Use 'sc stop {self.service_name}' to stop the service
echo Use 'sc delete {self.service_name}' to uninstall the service

pause
"""
        
        installer_file = self.production_dir / 'scripts' / 'install_service.bat'
        with open(installer_file, 'w') as f:
            f.write(installer_script)
            
        print(f"  ✅ Created service installer: {installer_file}")
        
    def create_startup_scripts(self):
        """Create startup and management scripts"""
        print("📜 Creating management scripts...")
        
        # Start script
        start_script = f"""
@echo off
echo Starting EBO Redundancy System...
cd /d "{self.production_dir}\\bin"
python ebo_redundancy_service.py
"""
        
        start_file = self.production_dir / 'scripts' / 'start_ebo_redundancy.bat'
        with open(start_file, 'w') as f:
            f.write(start_script)
            
        # Stop script
        stop_script = f"""
@echo off
echo Stopping EBO Redundancy Service...
sc stop "{self.service_name}"
"""
        
        stop_file = self.production_dir / 'scripts' / 'stop_ebo_redundancy.bat'
        with open(stop_file, 'w') as f:
            f.write(stop_script)
            
        # Status script
        status_script = f"""
@echo off
echo EBO Redundancy Service Status:
sc query "{self.service_name}"
echo.
echo Web Interface: http://localhost:{self.deployment_config['network']['monitoring_port']}
"""
        
        status_file = self.production_dir / 'scripts' / 'status_ebo_redundancy.bat'
        with open(status_file, 'w') as f:
            f.write(status_script)
            
        print(f"  ✅ Created management scripts in: {self.production_dir / 'scripts'}")
        
    def deploy(self):
        """Execute full production deployment"""
        print("🚀 Starting EBO Redundancy Production Deployment")
        print("=" * 60)
        
        try:
            # Load configuration
            self.load_deployment_config()
            
            # Validate environment
            if not self.validate_environment():
                print("❌ Environment validation failed. Deployment aborted.")
                return False
                
            # Create production structure
            self.create_production_structure()
            
            # Copy application files
            self.copy_application_files()
            
            # Create production configuration
            self.create_production_config()
            
            # Create Windows service
            self.create_windows_service()
            
            # Create startup scripts
            self.create_startup_scripts()
            
            print("\n🎉 Production deployment completed successfully!")
            print("=" * 60)
            print(f"📁 Installation Directory: {self.production_dir}")
            print(f"🌐 Web Interface: http://localhost:{self.deployment_config['network']['monitoring_port']}")
            print(f"📋 Management Scripts: {self.production_dir / 'scripts'}")
            print("\n📝 Next Steps:")
            print("1. Edit production_config.yaml with your actual server details")
            print("2. Run install_service.bat as Administrator to install the Windows service")
            print("3. Start the service using start_ebo_redundancy.bat")
            print("4. Access the web interface to configure and monitor your EBO redundancy")
            
            return True
            
        except Exception as e:
            print(f"❌ Deployment failed: {e}")
            return False

if __name__ == '__main__':
    deployer = ProductionDeployment()
    deployer.deploy()
