@echo off
title SafeKit Redundancy System - Simple Installer
color 0A

echo ================================================================
echo    SafeKit-Style Redundancy Management System
echo    Simple Installation Script v1.0
echo ================================================================
echo.

echo [1/4] Checking Python installation...
echo.

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ Python is not installed or not in PATH
    echo.
    echo SOLUTION:
    echo 1. Download Python from: https://python.org/downloads/
    echo 2. During installation, CHECK "Add Python to PATH"
    echo 3. Restart this installer
    echo.
    echo Or try running: python -m pip install flask flask-cors psutil requests
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ Python found: %PYTHON_VERSION%

echo.
echo [2/4] Installing dependencies using python -m pip...
echo.

REM Try using python -m pip instead of just pip
python -m pip install --upgrade pip
python -m pip install flask==2.3.3
python -m pip install flask-cors==4.0.0
python -m pip install psutil==5.9.5
python -m pip install requests==2.31.0
python -m pip install werkzeug==2.3.7

if %errorlevel% neq 0 (
    echo.
    echo ⚠️  Some packages may have failed to install
    echo The system may still work with existing packages
    echo.
)

echo.
echo [3/4] Creating directories...
echo.

if not exist "logs" mkdir logs
if not exist "config" mkdir config
if not exist "data" mkdir data
if not exist "backup" mkdir backup

echo ✅ Directories created

echo.
echo [4/4] Testing installation...
echo.

python -c "import flask, flask_cors, psutil, requests; print('✅ All required packages are available')" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  Some packages may be missing, but trying to continue...
) else (
    echo ✅ All dependencies verified
)

echo.
echo ================================================================
echo    INSTALLATION COMPLETED!
echo ================================================================
echo.
echo Next Steps:
echo.
echo 1. START THE SYSTEM:
echo    Double-click: start_redundancy_system.bat
echo.
echo 2. ACCESS WEB INTERFACE:
echo    http://localhost:5002
echo    http://localhost:5002/safekit-console
echo.
echo 3. If you get errors, try:
echo    python multisite_web_interface.py
echo.
echo ================================================================
echo.
pause
