#!/usr/bin/env python3
"""
Site Replication Wizard
Replicate existing software installation from Site A to Site B
"""

import os
import sys
import yaml
import json
import subprocess
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

class SiteReplicationWizard:
    """Wizard to replicate existing site to new location"""
    
    def __init__(self):
        self.setup_logging()
        self.source_site = None
        self.target_site = None
        self.replication_plan = {}
        
    def setup_logging(self):
        """Setup logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('site_replication.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('SiteReplication')
    
    def scan_existing_site(self, site_path: str = None) -> Dict[str, Any]:
        """Scan existing site for installed software"""
        self.logger.info("Scanning existing site for software installations...")
        
        discovered_software = {
            'ebo_installation': self._detect_ebo_installation(),
            'sql_server': self._detect_sql_server(),
            'databases': self._detect_databases(),
            'services': self._detect_services(),
            'configuration_files': self._detect_config_files(),
            'data_directories': self._detect_data_directories(),
            'licenses': self._detect_licenses(),
            'network_config': self._detect_network_config()
        }
        
        return discovered_software
    
    def _detect_ebo_installation(self) -> Dict[str, Any]:
        """Detect EcoStruxure Building Operation installation"""
        ebo_paths = [
            "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation",
            "C:\\Program Files (x86)\\Schneider Electric\\EcoStruxure Building Operation",
            "C:\\Program Files\\TAC\\Vista",
            "C:\\Program Files (x86)\\TAC\\Vista"
        ]
        
        ebo_info = {
            'installed': False,
            'installation_path': None,
            'version': None,
            'services': [],
            'databases': [],
            'config_files': []
        }
        
        for path in ebo_paths:
            if os.path.exists(path):
                ebo_info['installed'] = True
                ebo_info['installation_path'] = path
                ebo_info['version'] = self._get_ebo_version(path)
                ebo_info['services'] = self._get_ebo_services()
                ebo_info['config_files'] = self._get_ebo_config_files(path)
                break
        
        return ebo_info
    
    def _detect_sql_server(self) -> Dict[str, Any]:
        """Detect SQL Server installation"""
        sql_info = {
            'installed': False,
            'version': None,
            'instance_name': None,
            'installation_path': None,
            'databases': []
        }
        
        try:
            # Check for SQL Server service
            result = subprocess.run(['sc', 'query', 'MSSQLSERVER'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                sql_info['installed'] = True
                sql_info['instance_name'] = 'MSSQLSERVER'
                sql_info['databases'] = self._get_sql_databases()
        except:
            pass
        
        return sql_info
    
    def _detect_databases(self) -> List[Dict[str, Any]]:
        """Detect databases that need replication"""
        databases = []
        
        # Common EBO database names
        ebo_db_names = [
            'SBO_Database',
            'EBO_Database', 
            'Vista_Database',
            'TAC_Database',
            'BuildingOperation'
        ]
        
        for db_name in ebo_db_names:
            if self._database_exists(db_name):
                databases.append({
                    'name': db_name,
                    'type': 'EBO_Database',
                    'size': self._get_database_size(db_name),
                    'backup_required': True
                })
        
        return databases
    
    def _detect_services(self) -> List[Dict[str, Any]]:
        """Detect Windows services that need replication"""
        services = []
        
        # Common EBO services
        ebo_services = [
            'EcoStruxure Building Operation Enterprise Server',
            'EcoStruxure Building Operation Automation Server',
            'EcoStruxure Building Operation WebStation',
            'TAC Vista Server',
            'TAC Vista Automation Server'
        ]
        
        for service_name in ebo_services:
            if self._service_exists(service_name):
                services.append({
                    'name': service_name,
                    'status': self._get_service_status(service_name),
                    'startup_type': self._get_service_startup_type(service_name),
                    'replicate': True
                })
        
        return services
    
    def _detect_config_files(self) -> List[str]:
        """Detect configuration files that need replication"""
        config_files = []
        
        # Common configuration locations
        config_paths = [
            "C:\\ProgramData\\Schneider Electric",
            "C:\\ProgramData\\TAC",
            "C:\\Windows\\System32\\config",
            "C:\\EBO_Config"
        ]
        
        for path in config_paths:
            if os.path.exists(path):
                config_files.extend(self._find_config_files(path))
        
        return config_files
    
    def _detect_data_directories(self) -> List[str]:
        """Detect data directories that need replication"""
        data_dirs = []
        
        # Common data locations
        data_paths = [
            "C:\\EBO_Data",
            "C:\\ProgramData\\Schneider Electric\\EcoStruxure Building Operation",
            "C:\\TAC_Data",
            "C:\\BuildingOperation_Data"
        ]
        
        for path in data_paths:
            if os.path.exists(path):
                data_dirs.append(path)
        
        return data_dirs
    
    def _detect_licenses(self) -> List[Dict[str, Any]]:
        """Detect license files and information"""
        licenses = []
        
        # Common license locations
        license_paths = [
            "C:\\ProgramData\\Schneider Electric\\Licenses",
            "C:\\Program Files\\Common Files\\Schneider Electric\\Licenses",
            "C:\\TAC\\Licenses"
        ]
        
        for path in license_paths:
            if os.path.exists(path):
                for file in os.listdir(path):
                    if file.endswith(('.lic', '.license', '.key')):
                        licenses.append({
                            'file': os.path.join(path, file),
                            'type': 'License File',
                            'replicate': True
                        })
        
        return licenses
    
    def _detect_network_config(self) -> Dict[str, Any]:
        """Detect network configuration"""
        network_config = {
            'ip_address': self._get_local_ip(),
            'hostname': self._get_hostname(),
            'domain': self._get_domain(),
            'dns_servers': self._get_dns_servers(),
            'firewall_rules': self._get_firewall_rules()
        }
        
        return network_config
    
    def create_replication_plan(self, source_scan: Dict[str, Any], target_site_info: Dict[str, Any]) -> Dict[str, Any]:
        """Create detailed replication plan"""
        plan = {
            'source_site': {
                'scan_results': source_scan,
                'scan_date': datetime.now().isoformat()
            },
            'target_site': target_site_info,
            'replication_steps': [],
            'estimated_time': '2-4 hours',
            'requirements': [],
            'risks': []
        }
        
        # Add replication steps based on discovered software
        if source_scan['ebo_installation']['installed']:
            plan['replication_steps'].extend(self._create_ebo_replication_steps(source_scan, target_site_info))
        
        if source_scan['sql_server']['installed']:
            plan['replication_steps'].extend(self._create_sql_replication_steps(source_scan, target_site_info))
        
        plan['replication_steps'].extend(self._create_config_replication_steps(source_scan, target_site_info))
        plan['replication_steps'].extend(self._create_data_replication_steps(source_scan, target_site_info))
        
        # Add requirements
        plan['requirements'] = [
            'Network connectivity between sites',
            'Administrative access to both sites',
            'Sufficient disk space on target site',
            'Compatible operating system versions',
            'Valid licenses for target site'
        ]
        
        # Add risks
        plan['risks'] = [
            'Service downtime during replication',
            'Data inconsistency if source changes during replication',
            'License compliance issues',
            'Network connectivity issues'
        ]
        
        return plan
    
    def _create_ebo_replication_steps(self, source_scan: Dict[str, Any], target_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create EBO-specific replication steps"""
        steps = []
        
        ebo_info = source_scan['ebo_installation']
        
        steps.append({
            'step': 1,
            'category': 'EBO Installation',
            'action': 'Install EcoStruxure Building Operation',
            'description': f'Install EBO on target site at {target_info.get("installation_path", "C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation")}',
            'estimated_time': '30 minutes',
            'requirements': ['EBO installation media', 'Valid license'],
            'automated': False
        })
        
        steps.append({
            'step': 2,
            'category': 'EBO Configuration',
            'action': 'Copy EBO configuration files',
            'description': 'Copy configuration files from source to target',
            'source_path': ebo_info['installation_path'],
            'target_path': target_info.get('installation_path'),
            'estimated_time': '15 minutes',
            'automated': True
        })
        
        steps.append({
            'step': 3,
            'category': 'EBO Services',
            'action': 'Configure EBO services',
            'description': 'Set up and configure EBO services on target site',
            'services': ebo_info['services'],
            'estimated_time': '20 minutes',
            'automated': True
        })
        
        return steps
    
    def _create_sql_replication_steps(self, source_scan: Dict[str, Any], target_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create SQL Server replication steps"""
        steps = []
        
        sql_info = source_scan['sql_server']
        
        steps.append({
            'step': 10,
            'category': 'SQL Server',
            'action': 'Install SQL Server',
            'description': 'Install SQL Server on target site',
            'version': sql_info['version'],
            'estimated_time': '45 minutes',
            'automated': False
        })
        
        for db in source_scan['databases']:
            steps.append({
                'step': 11,
                'category': 'Database Replication',
                'action': f'Replicate database {db["name"]}',
                'description': f'Backup and restore database {db["name"]} to target site',
                'database': db['name'],
                'size': db['size'],
                'estimated_time': '30 minutes',
                'automated': True
            })
        
        return steps
    
    def _create_config_replication_steps(self, source_scan: Dict[str, Any], target_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create configuration replication steps"""
        steps = []
        
        steps.append({
            'step': 20,
            'category': 'Configuration',
            'action': 'Copy configuration files',
            'description': 'Copy all configuration files to target site',
            'files': source_scan['configuration_files'],
            'estimated_time': '10 minutes',
            'automated': True
        })
        
        return steps
    
    def _create_data_replication_steps(self, source_scan: Dict[str, Any], target_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create data replication steps"""
        steps = []
        
        for data_dir in source_scan['data_directories']:
            steps.append({
                'step': 30,
                'category': 'Data Replication',
                'action': f'Copy data directory {data_dir}',
                'description': f'Copy data from {data_dir} to target site',
                'source_path': data_dir,
                'estimated_time': '20 minutes',
                'automated': True
            })
        
        return steps
    
    def execute_replication(self, plan: Dict[str, Any]) -> bool:
        """Execute the replication plan"""
        self.logger.info("Starting site replication execution...")
        
        try:
            for step in plan['replication_steps']:
                self.logger.info(f"Executing step {step['step']}: {step['action']}")
                
                if step['automated']:
                    success = self._execute_automated_step(step)
                else:
                    success = self._execute_manual_step(step)
                
                if not success:
                    self.logger.error(f"Step {step['step']} failed")
                    return False
            
            self.logger.info("Site replication completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Replication failed: {e}")
            return False
    
    def _execute_automated_step(self, step: Dict[str, Any]) -> bool:
        """Execute automated replication step"""
        try:
            if step['category'] == 'EBO Configuration':
                return self._copy_ebo_config(step)
            elif step['category'] == 'Database Replication':
                return self._replicate_database(step)
            elif step['category'] == 'Configuration':
                return self._copy_configuration_files(step)
            elif step['category'] == 'Data Replication':
                return self._copy_data_directory(step)
            else:
                self.logger.warning(f"Unknown automated step category: {step['category']}")
                return True
        except Exception as e:
            self.logger.error(f"Automated step failed: {e}")
            return False
    
    def _execute_manual_step(self, step: Dict[str, Any]) -> bool:
        """Execute manual step (user intervention required)"""
        self.logger.info(f"Manual step required: {step['description']}")
        self.logger.info(f"Estimated time: {step['estimated_time']}")
        
        if 'requirements' in step:
            self.logger.info("Requirements:")
            for req in step['requirements']:
                self.logger.info(f"  - {req}")
        
        # In a real implementation, this would wait for user confirmation
        # For now, we'll assume manual steps are completed
        return True
    
    # Helper methods (simplified implementations)
    def _get_ebo_version(self, path: str) -> str:
        return "Unknown"
    
    def _get_ebo_services(self) -> List[str]:
        return []
    
    def _get_ebo_config_files(self, path: str) -> List[str]:
        return []
    
    def _get_sql_databases(self) -> List[str]:
        return []
    
    def _database_exists(self, db_name: str) -> bool:
        return False
    
    def _get_database_size(self, db_name: str) -> str:
        return "Unknown"
    
    def _service_exists(self, service_name: str) -> bool:
        return False
    
    def _get_service_status(self, service_name: str) -> str:
        return "Unknown"
    
    def _get_service_startup_type(self, service_name: str) -> str:
        return "Unknown"
    
    def _find_config_files(self, path: str) -> List[str]:
        return []
    
    def _get_local_ip(self) -> str:
        return "Unknown"
    
    def _get_hostname(self) -> str:
        return "Unknown"
    
    def _get_domain(self) -> str:
        return "Unknown"
    
    def _get_dns_servers(self) -> List[str]:
        return []
    
    def _get_firewall_rules(self) -> List[str]:
        return []
    
    def _copy_ebo_config(self, step: Dict[str, Any]) -> bool:
        return True
    
    def _replicate_database(self, step: Dict[str, Any]) -> bool:
        return True
    
    def _copy_configuration_files(self, step: Dict[str, Any]) -> bool:
        return True
    
    def _copy_data_directory(self, step: Dict[str, Any]) -> bool:
        return True

if __name__ == '__main__':
    wizard = SiteReplicationWizard()
    
    # Example usage
    print("🔍 Scanning existing site...")
    scan_results = wizard.scan_existing_site()
    
    print("📋 Creating replication plan...")
    target_site = {
        'hostname': 'SITE-B-SERVER',
        'ip_address': '************',
        'installation_path': 'C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation'
    }
    
    plan = wizard.create_replication_plan(scan_results, target_site)
    
    print("🚀 Replication plan created!")
    print(f"Estimated time: {plan['estimated_time']}")
    print(f"Steps: {len(plan['replication_steps'])}")
