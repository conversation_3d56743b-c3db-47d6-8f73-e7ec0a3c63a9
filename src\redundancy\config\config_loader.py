# Configuration loading utilities will go here
import yaml
from src.redundancy.utils.logger import app_logger

def load_config(config_path="config.yaml"):
    """
    Loads the monitoring configuration from a YAML file.

    Args:
        config_path (str): The path to the configuration file.

    Returns:
        dict: The loaded configuration, or None if an error occurs.
    """
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
            app_logger.info(f"Configuration loaded successfully from {config_path}")
            return config
    except FileNotFoundError:
        app_logger.error(f"Configuration file not found: {config_path}")
        return None
    except yaml.YAMLError as e:
        app_logger.error(f"Error parsing YAML configuration file {config_path}: {e}")
        return None
    except Exception as e:
        app_logger.error(f"An unexpected error occurred while loading configuration from {config_path}: {e}")
        return None

if __name__ == '__main__':
    # Example usage:
    # Create a dummy config.yaml for testing if it doesn't exist
    # (In a real scenario, this file would be created by the user)
    sample_config_content = """
monitors:
  - name: TestMonitor1
    type: dummy
    setting1: value1
  - name: TestMonitor2
    type: dummy
    setting2: value2
"""
    try:
        with open("config.yaml", 'r') as f:
            pass # File exists
    except FileNotFoundError:
        with open("config.yaml", 'w') as f:
            f.write(sample_config_content)
        app_logger.info("Created a sample config.yaml for testing.")

    config_data = load_config()
    if config_data:
        app_logger.info(f"Loaded configuration: {config_data}")
        # Example of accessing configuration data
        for monitor_config in config_data.get('monitors', []):
            app_logger.info(f"Monitor Name: {monitor_config.get('name')}, Type: {monitor_config.get('type')}")
