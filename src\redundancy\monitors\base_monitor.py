\
import abc

class BaseMonitor(abc.ABC):
    """
    Abstract base class for all hardware monitors.
    """
    def __init__(self, config, logger):
        if not config or not isinstance(config, dict):
            # Logger might not be available if config is bad, so print as a fallback
            print("FATAL: Monitor configuration is missing or invalid.")
            raise ValueError("Monitor configuration is missing or invalid.")
        
        self.name = config.get("name", "UnnamedMonitor")
        self.logger = logger
        self.is_healthy = False # Default to not healthy until a check proves otherwise
        
        if not self.logger:
            # This case should ideally not happen if logger is passed correctly
            print(f"FATAL: Logger not provided for monitor '{self.name}'.")
            raise ValueError(f"Logger not provided for monitor '{self.name}'.")

        self.logger.info(f"BaseMonitor initialized for '{self.name}'.")

    @abc.abstractmethod
    def check_status(self):
        """
        Check the status of the hardware component.
        This method should be implemented by subclasses and set self.is_healthy.
        """
        pass

    @abc.abstractmethod
    def trigger_redundancy(self):
        """
        Trigger redundancy measures if the primary component fails.
        This method should be implemented by subclasses.
        """
        pass

    # get_name is no longer abstract, it's provided by the base class.
    def get_name(self):
        """
        Return the name of the monitor.
        """
        return self.name
