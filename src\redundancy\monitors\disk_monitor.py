
import psutil
from .base_monitor import BaseMonitor # Relative import

class DiskMonitor(BaseMonitor):
    """
    Monitors disk space usage.
    """

    def __init__(self, config, logger):
        super().__init__(config, logger)
        params = config.get("params", {})
        self.path = params.get("path")
        self.threshold_percentage = params.get("threshold_percentage")

        if not self.path:
            self.logger.error(f"Missing 'path' in disk monitor parameters for {self.name}")
            raise ValueError(f"Missing 'path' in disk monitor parameters for {self.name}")

        if self.threshold_percentage is None:
            self.logger.error(f"Missing 'threshold_percentage' in disk monitor parameters for {self.name}")
            raise ValueError(f"Missing 'threshold_percentage' in disk monitor parameters for {self.name}")

        if not isinstance(self.threshold_percentage, (int, float)) or self.threshold_percentage <= 0 or self.threshold_percentage > 100:
            self.logger.error(f"Invalid 'threshold_percentage' for {self.name}: '{self.threshold_percentage}'. Must be a number between 0 and 100.")
            raise ValueError(f"Invalid 'threshold_percentage' for {self.name}: '{self.threshold_percentage}'")

        self.logger.info(f"Initialized DiskMonitor '{self.name}' for path '{self.path}' with threshold {self.threshold_percentage}%.")

    def check_status(self):
        """
        Checks if the disk usage for the specified path exceeds the threshold.
        Updates self.is_healthy.
        """
        try:
            usage = psutil.disk_usage(self.path)
            used_percentage = usage.percent
            self.logger.debug(f"Disk '{self.path}': Total={usage.total}, Used={usage.used}, Free={usage.free}, Percent={used_percentage}%")
            if used_percentage > self.threshold_percentage:
                self.logger.warning(f"Disk usage for '{self.path}' ({used_percentage}%) exceeds threshold ({self.threshold_percentage}%).")
                self.is_healthy = False
            else:
                self.logger.info(f"Disk usage for '{self.path}' ({used_percentage}%) is within threshold.")
                self.is_healthy = True
        except FileNotFoundError:
            self.logger.error(f"Disk path '{self.path}' not found for monitor '{self.name}'.")
            self.is_healthy = False
        except Exception as e:
            self.logger.error(f"Error checking disk status for '{self.path}' on monitor '{self.name}': {e}")
            self.is_healthy = False

    def trigger_redundancy(self):
        """
        Logs redundancy action based on health status.
        """
        if self.is_healthy:
            self.logger.info(f"Redundancy action for {self.name}: Currently healthy.")
        else:
            self.logger.critical(f"CRITICAL: Triggering redundancy action for unhealthy disk monitor {self.name} ({self.path}).")
            # print(f"ALERT: Disk usage high on {self.path} for monitor {self.name}!")

if __name__ == '__main__':
    from ..utils.logger import setup_logger
    import logging
    main_test_logger = setup_logger('DiskMonitorTestMain', level=logging.DEBUG, log_to_file=False)

    monitor_name = "SystemDriveMonitor"
    drive_path = "/"  # Change to 'C:\' or other valid path if needed
    usage_threshold = 80 # Percentage

    main_test_logger.info(f"Starting DiskMonitor test for '{monitor_name}' on path '{drive_path}'.")

    config = {
        "name": monitor_name,
        "type": "disk",
        "params": {
            "path": drive_path,
            "threshold_percentage": usage_threshold
        }
    }
    disk_mon = DiskMonitor(config, main_test_logger)
    disk_mon.check_status()
    disk_mon.trigger_redundancy()

    # Test with a non-existent path
    non_existent_config = {
        "name": "NonExistentPathMonitor",
        "type": "disk",
        "params": {
            "path": "/nonexistentpath12345",
            "threshold_percentage": 90
        }
    }
    non_existent_monitor = DiskMonitor(non_existent_config, main_test_logger)
    non_existent_monitor.check_status()
    non_existent_monitor.trigger_redundancy()

    # Test invalid config - missing path
    invalid_config_no_path = {"name": "NoPathTest", "type": "disk", "params": {"threshold_percentage": 80}}
    try:
        DiskMonitor(invalid_config_no_path, main_test_logger)
    except ValueError as e:
        main_test_logger.error(f"Caught expected error for NoPathTest: {e}")

    # Test invalid config - missing threshold
    invalid_config_no_threshold = {"name": "NoThresholdTest", "type": "disk", "params": {"path": "/"}}
    try:
        DiskMonitor(invalid_config_no_threshold, main_test_logger)
    except ValueError as e:
        main_test_logger.error(f"Caught expected error for NoThresholdTest: {e}")

