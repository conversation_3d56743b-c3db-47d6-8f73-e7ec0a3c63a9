import requests
from .base_monitor import BaseMonitor # Relative import

class HTTPMonitor(BaseMonitor):
    """
    Monitors the health of an HTTP endpoint.
    """

    def __init__(self, config, logger):
        super().__init__(config, logger) # Correctly call BaseMonitor's init
        self.url = config["params"].get("url")
        self.expected_status_code = config["params"].get("expected_status_code", 200)
        self.timeout = config["params"].get("timeout_seconds", 10)

        if not self.url:
            self.logger.error(f"Missing 'url' in HTTP monitor parameters for {self.name}")
            raise ValueError(f"Missing 'url' in HTTP monitor parameters for {self.name}")
        
        if not isinstance(self.url, str) or not (self.url.startswith("http://") or self.url.startswith("https://")):
            self.logger.error(f"Invalid URL format for {self.name}: '{self.url}'. It must be a string starting with http:// or https://")
            raise ValueError(f"Invalid URL format for {self.name}: '{self.url}'")
        
        if not isinstance(self.expected_status_code, int):
            self.logger.error(f"Invalid 'expected_status_code' for {self.name}: '{self.expected_status_code}'. It must be an integer.")
            raise ValueError(f"Invalid 'expected_status_code' for {self.name}: '{self.expected_status_code}'")

        if not isinstance(self.timeout, (int, float)) or self.timeout <= 0:
            self.logger.error(f"Invalid 'timeout_seconds' for {self.name}: '{self.timeout}'. It must be a positive number.")
            raise ValueError(f"Invalid 'timeout_seconds' for {self.name}: '{self.timeout}'")

        self.logger.info(f"Initialized HTTPMonitor '{self.name}' for URL '{self.url}' expecting status {self.expected_status_code}, timeout {self.timeout}s.")

    def check_status(self):
        """
        Checks if the HTTP endpoint is reachable and returns the expected status code.
        Updates self.is_healthy.
        """
        try:
            response = requests.get(self.url, timeout=self.timeout)
            if response.status_code == self.expected_status_code:
                self.logger.info(f"HTTP check for '{self.name}' successful with status {response.status_code}.")
                self.is_healthy = True
            else:
                self.logger.warning(f"CRITICAL: HTTP check for '{self.name}' failed. Expected {self.expected_status_code}, got {response.status_code}.")
                self.is_healthy = False
        except requests.exceptions.Timeout:
            self.logger.error(f"ERROR: HTTP check for '{self.name}' timed out after {self.timeout} seconds.")
            self.is_healthy = False
        except requests.exceptions.RequestException as e:
            self.logger.error(f"ERROR: HTTP check for '{self.name}' failed: {e}")
            self.is_healthy = False
        except Exception as e:
            self.logger.error(f"ERROR: Unexpected error during HTTP check for '{self.name}': {e}")
            self.is_healthy = False

    def trigger_redundancy(self):
        """
        Logs redundancy action based on health status.
        """
        if self.is_healthy:
            self.logger.info(f"Redundancy action for '{self.name}': Currently healthy.")
        else:
            self.logger.critical(f"CRITICAL: Triggering redundancy action for unhealthy HTTP endpoint '{self.name}' ({self.url}).")
            # Placeholder for actual redundancy logic, e.g.:
            # print(f"ALERT: HTTP endpoint {self.url} for monitor {self.name} is unhealthy (expected {self.expected_status_code})!")

if __name__ == '__main__':
    from ..utils.logger import setup_logger 
    import logging
    main_test_logger = setup_logger('HTTPMonitorTestMain', level=logging.DEBUG, log_to_file=False)

    healthy_config = {
        "name": "GoogleHealthCheck", 
        "type": "http",
        "params": {"url": "https://www.google.com", "expected_status_code": 200, "timeout_seconds": 5}
    }
    healthy_monitor = HTTPMonitor(healthy_config, main_test_logger)
    main_test_logger.info(f"Testing '{healthy_monitor.name}'...")
    healthy_monitor.check_status()
    if not healthy_monitor.is_healthy:
        healthy_monitor.trigger_redundancy()
    else:
        main_test_logger.info(f"'{healthy_monitor.name}' check passed.")

    not_found_config = {
        "name": "NotFoundCheck", 
        "type": "http",
        "params": {"url": "http://example.com/nonexistentpage123", "expected_status_code": 200}
    }
    not_found_monitor = HTTPMonitor(not_found_config, main_test_logger)
    main_test_logger.info(f"Testing '{not_found_monitor.name}' (expecting failure due to 404 vs 200)...")
    not_found_monitor.check_status()
    if not not_found_monitor.is_healthy:
        not_found_monitor.trigger_redundancy()
    else:
        main_test_logger.info(f"'{not_found_monitor.name}' check unexpectedly passed. Review logic.")

    bad_host_config = {
        "name": "BadHostCheck",
        "type": "http",
        "params": {"url": "http://thisshouldnotexist12345abc.com"}
    }
    bad_host_monitor = HTTPMonitor(bad_host_config, main_test_logger)
    main_test_logger.info(f"Testing '{bad_host_monitor.name}' (expecting connection error)...")
    bad_host_monitor.check_status()
    if not bad_host_monitor.is_healthy:
        bad_host_monitor.trigger_redundancy()
    else:
        main_test_logger.info(f"'{bad_host_monitor.name}' check unexpectedly passed. Review logic.")

    timeout_config = {
        "name": "TimeoutCheck",
        "type": "http",
        "params": {"url": "http://httpbin.org/delay/5", "expected_status_code": 200, "timeout_seconds": 2} 
    }
    timeout_monitor = HTTPMonitor(timeout_config, main_test_logger)
    main_test_logger.info(f"Testing '{timeout_monitor.name}' (expecting timeout)...")
    timeout_monitor.check_status()
    if not timeout_monitor.is_healthy:
        timeout_monitor.trigger_redundancy()
    else:
        main_test_logger.info(f"'{timeout_monitor.name}' check did not time out as expected. Review logic or endpoint.")

    invalid_config_no_url = {"name": "NoUrlTest", "type": "http", "params": {}}
    try:
        HTTPMonitor(invalid_config_no_url, main_test_logger)
    except ValueError as e:
        main_test_logger.error(f"Caught expected error for NoUrlTest: {e}")

    invalid_config_bad_url = {"name": "BadUrlTest", "type": "http", "params": {"url": "ftp://example.com"}}
    try:
        HTTPMonitor(invalid_config_bad_url, main_test_logger)
    except ValueError as e:
        main_test_logger.error(f"Caught expected error for BadUrlTest: {e}")

