import psutil
from .base_monitor import BaseMonitor # Relative import

class NetworkMonitor(BaseMonitor):
    """
    Monitors network interface status.
    """

    def __init__(self, config, logger):
        super().__init__(config, logger)
        params = config.get("params", {})
        self.interface_name = params.get("interface_name")
        self.expected_status = params.get("expected_status", "up").lower() # Default to "up"

        if not self.interface_name:
            self.logger.error(f"Missing 'interface_name' in network monitor parameters for {self.name}")
            raise ValueError(f"Missing 'interface_name' in network monitor parameters for {self.name}")

        if self.expected_status not in ["up", "down"]:
            self.logger.error(f"Invalid 'expected_status' value '{self.expected_status}' for {self.name}. Must be 'up' or 'down'.")
            raise ValueError(f"Invalid 'expected_status' value '{self.expected_status}' for {self.name}. Must be 'up' or 'down'.")

        self.logger.info(f"Initialized NetworkMonitor '{self.name}' for interface '{self.interface_name}', expecting status '{self.expected_status}'.")

    def check_status(self):
        """
        Checks if the specified network interface status matches the expected status.
        Updates self.is_healthy.
        """
        try:
            stats = psutil.net_if_stats()
            if self.interface_name not in stats:
                self.logger.error(f"ERROR: Network interface {self.interface_name} not found.")
                self.is_healthy = False
                return

            interface_stat = stats[self.interface_name]
            current_status_is_up = interface_stat.isup

            if self.expected_status == "up":
                if current_status_is_up:
                    self.logger.info(f"Network interface {self.interface_name} is up as expected.")
                    self.is_healthy = True
                else:
                    self.logger.warning(f"CRITICAL: Network interface {self.interface_name} is down, but expected up.")
                    self.is_healthy = False
            elif self.expected_status == "down":
                if not current_status_is_up:
                    self.logger.info(f"Network interface {self.interface_name} is down as expected.")
                    self.is_healthy = True
                else:
                    self.logger.warning(f"CRITICAL: Network interface {self.interface_name} is up, but expected down.")
                    self.is_healthy = False
        except psutil.NoSuchProcess:
            self.logger.error(f"ERROR: psutil.NoSuchProcess encountered while checking {self.interface_name}. This can happen if the process providing net info is gone.")
            self.is_healthy = False
        except Exception as e:
            self.logger.error(f"ERROR: Error checking network interface {self.interface_name}: {e}")
            self.is_healthy = False

    def trigger_redundancy(self):
        """
        Logs redundancy action based on health status.
        """
        if self.is_healthy:
            self.logger.info(f"Redundancy action for {self.name}: Currently healthy.")
        else:
            self.logger.critical(f"CRITICAL: Triggering redundancy action for unhealthy network interface {self.name} ({self.interface_name}).")
            # print(f"ALERT: Network interface {self.interface_name} on monitor {self.name} is having issues!")

if __name__ == '__main__':
    from ..utils.logger import setup_logger # Corrected import
    import logging
    main_test_logger = setup_logger('NetworkMonitorTestMain', level=logging.DEBUG, log_to_file=False)

    available_interfaces = list(psutil.net_if_addrs().keys())
    if not available_interfaces:
        main_test_logger.error("No network interfaces found for testing.")
        exit()

    test_interface_name = "eth0" 
    if test_interface_name not in available_interfaces:
        if "Ethernet" in available_interfaces: 
            test_interface_name = "Ethernet"
        elif "Wi-Fi" in available_interfaces: 
            test_interface_name = "Wi-Fi"
        elif "en0" in available_interfaces: 
            test_interface_name = "en0"
        else:
            test_interface_name = available_interfaces[0]
    
    main_test_logger.info(f"Using interface: {test_interface_name} for testing.")

    # Test UP
    up_config = {
        "name": "PrimaryNIC_UP",
        "type": "network",
        "params": {"interface_name": test_interface_name, "expected_status": "up"}
    }
    net_mon_up = NetworkMonitor(up_config, main_test_logger)
    net_mon_up.check_status()
    net_mon_up.trigger_redundancy()

    # Test DOWN (will likely log CRITICAL unless interface is actually down)
    down_config = {
        "name": "PrimaryNIC_EXPECT_DOWN",
        "type": "network",
        "params": {"interface_name": test_interface_name, "expected_status": "down"}
    }
    net_mon_down = NetworkMonitor(down_config, main_test_logger)
    net_mon_down.check_status()
    net_mon_down.trigger_redundancy()

    # Test with a non-existent interface
    non_existent_config = {
        "name": "NonExistentNicMonitor",
        "type": "network",
        "params": {"interface_name": "nonexistentinterface12345", "expected_status": "up"}
    }
    non_existent_monitor = NetworkMonitor(non_existent_config, main_test_logger)
    non_existent_monitor.check_status()
    non_existent_monitor.trigger_redundancy()

    # Test invalid config - missing interface_name
    invalid_config_no_iface = {"name": "NoIfaceTest", "type": "network", "params": {"expected_status": "up"}}
    try:
        NetworkMonitor(invalid_config_no_iface, main_test_logger)
    except ValueError as e:
        main_test_logger.error(f"Caught expected error for NoIfaceTest: {e}")

    # Test invalid config - bad expected_status
    invalid_config_bad_status = {
        "name": "BadStatusTest", 
        "type": "network", 
        "params": {"interface_name": test_interface_name, "expected_status": "maybe"}
    }
    try:
        NetworkMonitor(invalid_config_bad_status, main_test_logger)
    except ValueError as e:
        main_test_logger.error(f"Caught expected error for BadStatusTest: {e}")
