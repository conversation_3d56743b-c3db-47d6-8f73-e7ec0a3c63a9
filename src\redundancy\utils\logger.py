\
import logging
import sys
import os # Added for path manipulation

DEFAULT_LOG_FILE = "hardware_redundancy_app.log"
DEFAULT_LOG_LEVEL = logging.INFO

def setup_logger(name, level=None, log_file=None, log_to_console=True, log_to_file=True):
    """
    Sets up a logger with console and optional file handlers.

    Args:
        name (str): The name for the logger.
        level (int, optional): The logging level. Defaults to DEFAULT_LOG_LEVEL.
        log_file (str, optional): Path to the log file. Defaults to DEFAULT_LOG_FILE.
        log_to_console (bool, optional): Whether to log to console. Defaults to True.
        log_to_file (bool, optional): Whether to log to file. Defaults to True.

    Returns:
        logging.Logger: Configured logger instance.
    """
    if level is None:
        level = DEFAULT_LOG_LEVEL
    if log_file is None:
        log_file = DEFAULT_LOG_FILE

    logger = logging.getLogger(name)
    # Prevent duplicate handlers if logger already exists and is configured
    if logger.hasHandlers():
        # Check if it's our specific app_logger, if so, reconfigure, otherwise return existing
        if name == 'HardwareRedundancyApp' and any(isinstance(h, logging.FileHandler) for h in logger.handlers):
            # Already configured by a previous call in the same session, perhaps for a different module
            # For simplicity, we'll let it be. More complex scenarios might need handler removal/update.
            pass 
        elif name != 'HardwareRedundancyApp': # For other loggers, if they exist, let them be.
             return logger
        # If it is app_logger but not fully configured (e.g. no file handler), clear and re-add
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

    logger.setLevel(level)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(lineno)d - %(message)s')

    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(level) # Set level for handler too
        logger.addHandler(console_handler)

    if log_to_file:
        try:
            # Ensure log directory exists if log_file path includes directories
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            file_handler = logging.FileHandler(log_file, mode='a') # Append mode
            file_handler.setFormatter(formatter)
            file_handler.setLevel(level) # Set level for handler too
            logger.addHandler(file_handler)
        except Exception as e:
            # Fallback to console logging if file logging setup fails
            print(f"Error setting up file logger for {log_file}: {e}. Logging to console only for this logger.", file=sys.stderr)
            if not log_to_console: # If console wasn't enabled, enable it as a fallback
                console_handler = logging.StreamHandler(sys.stdout)
                console_handler.setFormatter(formatter)
                console_handler.setLevel(level)
                logger.addHandler(console_handler)

    # logger.propagate = False # To avoid duplicate logs if root logger is also configured
    return logger

# Initialize the main application logger
# This will be reconfigured by main.py if settings are found in config.yaml
app_logger = setup_logger('HardwareRedundancyApp')

if __name__ == '__main__':
    # Example usage
    # Default app_logger (will log to console and 'hardware_redundancy_app.log')
    app_logger.info("This is an info message from app_logger (default setup).")

    # Custom logger setup
    test_logger_file = "test_logger.log"
    # Clean up old test log file for demonstration
    if os.path.exists(test_logger_file):
        os.remove(test_logger_file)
        
    custom_logger = setup_logger('MyCustomModule', level=logging.DEBUG, log_file=test_logger_file)
    custom_logger.debug(f"This is a debug message to console and {test_logger_file}.")
    custom_logger.info(f"This is an info message to console and {test_logger_file}.")
    custom_logger.warning(f"This is a warning message to console and {test_logger_file}.")

    # Logger only to file
    file_only_logger_file = "file_only.log"
    if os.path.exists(file_only_logger_file):
        os.remove(file_only_logger_file)
    file_only_logger = setup_logger('FileOnlyLogger', log_file=file_only_logger_file, log_to_console=False)
    file_only_logger.info("This message should only be in file_only.log")
    print(f"Check {file_only_logger_file} for the message above.")

    # Logger only to console (explicitly)
    console_only_logger = setup_logger('ConsoleOnlyLogger', log_to_file=False)
    console_only_logger.info("This message should only be in console (ConsoleOnlyLogger).")

    app_logger.error("This is an error from app_logger.")
    app_logger.critical("This is a critical message from app_logger.")
