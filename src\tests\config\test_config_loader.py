import pytest  # pytest can be kept for clarity even if only fixtures are used
import yaml
import logging  # Added for caplog.set_level
from src.redundancy.config.config_loader import load_config

# Test valid YAML configuration loading
def test_load_valid_config(tmp_path):
    config_content = {
        "global_settings": {"check_interval_seconds": 30},
        "logging": {"log_level": "DEBUG"},
        "monitors": [
            {"name": "TestDisk", "type": "disk", "path": "/", "threshold_percentage": 85},
            {"name": "TestNet", "type": "network", "interface": "eth0"}
        ]
    }
    config_file = tmp_path / "test_config.yaml"
    with open(config_file, 'w') as f:
        yaml.dump(config_content, f)
    
    loaded_config = load_config(str(config_file))
    assert loaded_config is not None
    assert loaded_config["global_settings"]["check_interval_seconds"] == 30
    assert len(loaded_config["monitors"]) == 2
    assert loaded_config["monitors"][0]["name"] == "TestDisk"

# Test loading a non-existent configuration file
def test_load_non_existent_config(tmp_path, caplog):
    caplog.set_level(logging.ERROR) # Ensure ERROR messages are captured
    config_file = tmp_path / "non_existent_config.yaml"
    loaded_config = load_config(str(config_file))
    assert loaded_config is None
    assert f"Configuration file not found: {config_file}" in caplog.text

# Test loading an invalid YAML file
def test_load_invalid_yaml_config(tmp_path, caplog):
    caplog.set_level(logging.ERROR) # Ensure ERROR messages are captured
    config_file = tmp_path / "invalid_config.yaml"
    with open(config_file, 'w') as f:
        f.write('monitors: [{"name": "Test", "type": "disk"}') # Corrected string quoting
    
    loaded_config = load_config(str(config_file))
    assert loaded_config is None
    assert f"Error parsing YAML configuration file {config_file}" in caplog.text

# Test loading an empty YAML file
def test_load_empty_yaml_config(tmp_path, caplog):
    caplog.set_level(logging.INFO) # To capture potential info logs if any, though not asserted here
    config_file = tmp_path / "empty_config.yaml"
    with open(config_file, 'w') as f:
        f.write("") # Empty file
    
    loaded_config = load_config(str(config_file))
    # An empty YAML file is often parsed as None by PyYAML, which is acceptable
    # The load_config function might log this or handle it as it currently does.
    # Depending on strictness, this could be None or an empty dict.
    # Current implementation of load_config returns the direct result of yaml.safe_load()
    # which is None for an empty file.
    assert loaded_config is None 
    # Check if a specific log message for empty but valid YAML is desired or if current behavior is fine.
    # For now, we just assert it's None as per PyYAML's behavior with safe_load.
    # app_logger.info(f"Configuration loaded successfully from {config_path}") would still be logged if it was None.
    # This might be an area to refine in load_config if empty should be treated as an error or a specific empty dict.

# Test loading a config file with missing 'monitors' key
def test_load_config_missing_monitors_key(tmp_path):
    config_content = {
        "global_settings": {"check_interval_seconds": 60},
        "logging": {"log_level": "INFO"}
        # 'monitors' key is missing
    }
    config_file = tmp_path / "missing_monitors_config.yaml"
    with open(config_file, 'w') as f:
        yaml.dump(config_content, f)
    
    loaded_config = load_config(str(config_file))
    assert loaded_config is not None
    assert "monitors" not in loaded_config # or loaded_config.get("monitors") is None

# Test loading a config where 'monitors' is not a list
def test_load_config_monitors_not_a_list(tmp_path, caplog):
    caplog.set_level(logging.INFO) # To capture potential info logs
    config_content = {
        "monitors": "this should be a list"
    }
    config_file = tmp_path / "monitors_not_list.yaml"
    with open(config_file, 'w') as f:
        yaml.dump(config_content, f)
    
    loaded_config = load_config(str(config_file))
    assert loaded_config is not None
    assert isinstance(loaded_config.get("monitors"), str) 
    # The main.py initialize_monitors function would then handle this gracefully.
    # load_config itself doesn't validate schema beyond basic YAML parsing.

