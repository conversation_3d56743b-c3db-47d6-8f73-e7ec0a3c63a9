import pytest
from unittest.mock import patch, MagicMock
from src.redundancy.monitors.disk_monitor import DiskMonitor
from src.redundancy.utils.logger import setup_logger
import logging # Added for caplog.set_level

# Setup a logger for tests
test_logger = setup_logger('TestDiskMonitor', level=logging.DEBUG, log_to_file=False)

@pytest.fixture
def disk_monitor_config():
    return {
        "name": "TestDisk",
        "type": "disk",
        "params": {
            "path": "/test_path",
            "threshold_percentage": 80
        }
    }

# Fixture for DiskMonitor instance
@pytest.fixture
def disk_monitor_instance(disk_monitor_config):
    return DiskMonitor(disk_monitor_config, test_logger)

# Test successful disk check (usage below threshold)
@patch('psutil.disk_usage') # Mock the psutil.disk_usage function
def test_disk_check_status_ok(mock_disk_usage, disk_monitor_instance, caplog):
    caplog.set_level(logging.INFO) # Capture INFO level logs
    # Configure the mock to return values indicating disk usage is OK
    mock_disk_usage.return_value = MagicMock(percent=70, total=100*1024*1024*1024, used=70*1024*1024*1024, free=30*1024*1024*1024)

    disk_monitor_instance.check_status()
    assert disk_monitor_instance.is_healthy
    assert "Disk usage for '/test_path' (70%) is within threshold." in caplog.text
    mock_disk_usage.assert_called_once_with("/test_path")

# Test disk check failure (usage above threshold)
@patch('psutil.disk_usage')
def test_disk_check_status_fail_above_threshold(mock_disk_usage, disk_monitor_instance, caplog):
    caplog.set_level(logging.WARNING) # Capture WARNING level logs
    mock_disk_usage.return_value = MagicMock(percent=85, total=100, used=85, free=15)

    disk_monitor_instance.check_status()
    assert not disk_monitor_instance.is_healthy
    assert "Disk usage for '/test_path' (85%) exceeds threshold (80%)." in caplog.text
    mock_disk_usage.assert_called_once_with("/test_path")

# Test disk check when path is not found
@patch('psutil.disk_usage', side_effect=FileNotFoundError("Path not found"))
def test_disk_check_status_path_not_found(mock_disk_usage, disk_monitor_instance, caplog):
    caplog.set_level(logging.ERROR) # Capture ERROR level logs
    disk_monitor_instance.check_status()
    assert not disk_monitor_instance.is_healthy
    assert "Disk path '/test_path' not found for monitor 'TestDisk'." in caplog.text
    mock_disk_usage.assert_called_once_with("/test_path")

# Test disk check with other psutil errors
@patch('psutil.disk_usage', side_effect=Exception("psutil generic error"))
def test_disk_check_status_psutil_error(mock_disk_usage, disk_monitor_instance, caplog):
    caplog.set_level(logging.ERROR) # Capture ERROR level logs
    disk_monitor_instance.check_status()
    assert not disk_monitor_instance.is_healthy
    assert "Error checking disk status for '/test_path' on monitor 'TestDisk': psutil generic error" in caplog.text
    mock_disk_usage.assert_called_once_with("/test_path")

# Test trigger_redundancy method when healthy
def test_disk_trigger_redundancy_healthy(disk_monitor_instance, caplog):
    caplog.set_level(logging.INFO) # Capture INFO level logs
    disk_monitor_instance.is_healthy = True
    disk_monitor_instance.trigger_redundancy()
    assert "Redundancy action for TestDisk: Currently healthy." in caplog.text

# Test trigger_redundancy method when unhealthy
def test_disk_trigger_redundancy_unhealthy(disk_monitor_instance, caplog):
    caplog.set_level(logging.CRITICAL) # Capture CRITICAL level logs
    disk_monitor_instance.is_healthy = False
    disk_monitor_instance.trigger_redundancy()
    assert "CRITICAL: Triggering redundancy action for unhealthy disk monitor TestDisk (/test_path)." in caplog.text

# Test get_name method
def test_disk_get_name(disk_monitor_instance):
    assert disk_monitor_instance.get_name() == "TestDisk"

# Test initialization logging
def test_disk_monitor_initialization(caplog):
    caplog.set_level(logging.INFO) # Capture INFO level logs
    config = {
        "name": "InitDiskTest",
        "type": "disk",
        "params": {
            "path": "/init_path",
            "threshold_percentage": 90
        }
    }
    DiskMonitor(config, test_logger)
    assert "Initialized DiskMonitor 'InitDiskTest' for path '/init_path' with threshold 90%." in caplog.text

