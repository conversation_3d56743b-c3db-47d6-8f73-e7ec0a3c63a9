import pytest
from unittest.mock import patch, MagicMock
from requests.exceptions import RequestException
from redundancy.monitors.http_monitor import HTTPMonitor
from redundancy.utils.logger import setup_logger # Restored import
import logging # Restored import

# Setup a logger for tests
test_logger = setup_logger('TestHTTPMonitor', level=logging.DEBUG, log_to_file=False) # Restored logger definition

@pytest.fixture
def http_monitor_config():
    return {
        "name": "TestWebService",
        "type": "http",
        "params": {
            "url": "http://example.com/health",
            "expected_status_code": 200,
            "timeout_seconds": 5
        }
    }

@pytest.fixture
def http_monitor(http_monitor_config):
    return HTTPMonitor(http_monitor_config, test_logger)

def test_http_monitor_creation(http_monitor, http_monitor_config):
    assert http_monitor.name == http_monitor_config["name"]
    assert http_monitor.url == http_monitor_config["params"]["url"]
    assert http_monitor.expected_status_code == http_monitor_config["params"]["expected_status_code"]
    assert http_monitor.timeout == http_monitor_config["params"]["timeout_seconds"]
    assert http_monitor.logger == test_logger

@patch('redundancy.monitors.http_monitor.requests.get')
def test_check_status_success(mock_requests_get, http_monitor, caplog):
    caplog.set_level(logging.INFO)
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_requests_get.return_value = mock_response
    
    http_monitor.check_status()
    assert http_monitor.is_healthy
    assert f"HTTP check for '{http_monitor.name}' successful with status 200." in caplog.text # Use f-string and monitor name
    mock_requests_get.assert_called_once_with(http_monitor.url, timeout=http_monitor.timeout)

@patch('redundancy.monitors.http_monitor.requests.get')
def test_check_status_unexpected_status_code(mock_requests_get, http_monitor, caplog):
    caplog.set_level(logging.WARNING)
    mock_response = MagicMock()
    mock_response.status_code = 500
    mock_requests_get.return_value = mock_response
    
    http_monitor.check_status()
    assert not http_monitor.is_healthy
    assert f"CRITICAL: HTTP check for '{http_monitor.name}' failed. Expected {http_monitor.expected_status_code}, got 500." in caplog.text # Use f-string

@patch('redundancy.monitors.http_monitor.requests.get')
def test_check_status_request_exception(mock_requests_get, http_monitor, caplog):
    caplog.set_level(logging.ERROR)
    mock_requests_get.side_effect = RequestException("Connection error")
    
    http_monitor.check_status()
    assert not http_monitor.is_healthy
    assert f"ERROR: HTTP check for '{http_monitor.name}' failed: Connection error" in caplog.text # Use f-string

@patch('redundancy.monitors.http_monitor.requests.get')
def test_check_status_general_exception(mock_requests_get, http_monitor, caplog):
    caplog.set_level(logging.ERROR)
    mock_requests_get.side_effect = Exception("Generic error")

    http_monitor.check_status()
    assert not http_monitor.is_healthy
    assert f"ERROR: Unexpected error during HTTP check for '{http_monitor.name}': Generic error" in caplog.text # Use f-string

def test_trigger_redundancy_healthy(http_monitor, caplog):
    caplog.set_level(logging.INFO)
    http_monitor.is_healthy = True
    http_monitor.trigger_redundancy()
    assert not any(record.levelno == logging.CRITICAL for record in caplog.records)
    assert f"Redundancy action for '{http_monitor.name}': Currently healthy." in caplog.text # Use f-string

def test_trigger_redundancy_unhealthy(http_monitor, caplog):
    caplog.set_level(logging.CRITICAL)
    http_monitor.is_healthy = False
    http_monitor.trigger_redundancy()
    assert f"CRITICAL: Triggering redundancy action for unhealthy HTTP endpoint '{http_monitor.name}' ({http_monitor.url})." in caplog.text # Use f-string

def test_http_monitor_invalid_config_missing_url(caplog):
    caplog.set_level(logging.ERROR)
    config = {
        "name": "TestMissingUrl",
        "type": "http",
        "params": {
            # "url": "http://example.com/health", # Missing
            "expected_status_code": 200 
        }
    }
    with pytest.raises(ValueError) as excinfo:
        HTTPMonitor(config, test_logger)
    assert "Missing 'url' in HTTP monitor parameters" in str(excinfo.value)
    assert f"Missing 'url' in HTTP monitor parameters for {config['name']}" in caplog.text # Use f-string and config name

def test_http_monitor_invalid_config_missing_status_code(caplog):
    caplog.set_level(logging.INFO) # Check for info log about default
    config = {
        "name": "TestMissingCode",
        "type": "http",
        "params": {
            "url": "http://example.com/health"
            # "expected_status_code": 200 # Missing, should default
        }
    }
    monitor = HTTPMonitor(config, test_logger)
    assert monitor.expected_status_code == 200 # Check default is applied
    assert f"Initialized HTTPMonitor '{config['name']}' for URL '{config['params']['url']}' expecting status 200, timeout {monitor.timeout}s." in caplog.text

def test_http_monitor_optional_timeout(http_monitor_config):
    config_no_timeout = http_monitor_config.copy()
    del config_no_timeout["params"]["timeout_seconds"]
    monitor = HTTPMonitor(config_no_timeout, test_logger)
    assert monitor.timeout == 10 # Default timeout in HTTPMonitor
