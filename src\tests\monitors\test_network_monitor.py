import pytest
from unittest.mock import patch, MagicMock
from redundancy.monitors.network_monitor import NetworkMonitor
from redundancy.utils.logger import setup_logger
import logging

# Setup a logger for tests
test_logger = setup_logger('TestNetworkMonitor', level=logging.DEBUG, log_to_file=False)

@pytest.fixture
def network_monitor_config():
    return {
        "name": "TestNetInterface",
        "type": "network",
        "params": {
            "interface_name": "eth0",
            "expected_status": "up"
        }
    }

@pytest.fixture
def network_monitor(network_monitor_config):
    return NetworkMonitor(network_monitor_config, test_logger)

def test_network_monitor_creation(network_monitor, network_monitor_config):
    assert network_monitor.name == network_monitor_config["name"]
    assert network_monitor.interface_name == network_monitor_config["params"]["interface_name"]
    assert network_monitor.expected_status == network_monitor_config["params"]["expected_status"]
    assert network_monitor.logger == test_logger

@patch('redundancy.monitors.network_monitor.psutil.net_if_stats')
def test_check_status_interface_up_and_expected(mock_net_if_stats, network_monitor, caplog):
    caplog.set_level(logging.INFO)
    mock_stats = MagicMock()
    mock_stats.isup = True
    mock_net_if_stats.return_value = {"eth0": mock_stats}
    
    network_monitor.check_status()
    assert network_monitor.is_healthy
    assert "Network interface eth0 is up as expected." in caplog.text

@patch('redundancy.monitors.network_monitor.psutil.net_if_stats')
def test_check_status_interface_down_and_expected(mock_net_if_stats, network_monitor_config, caplog):
    caplog.set_level(logging.INFO)
    config = network_monitor_config.copy()
    config["params"]["expected_status"] = "down"
    monitor = NetworkMonitor(config, test_logger)
    
    mock_stats = MagicMock()
    mock_stats.isup = False
    mock_net_if_stats.return_value = {"eth0": mock_stats}
    
    monitor.check_status()
    assert monitor.is_healthy
    assert "Network interface eth0 is down as expected." in caplog.text

@patch('redundancy.monitors.network_monitor.psutil.net_if_stats')
def test_check_status_interface_up_but_expected_down(mock_net_if_stats, network_monitor_config, caplog):
    caplog.set_level(logging.WARNING)
    config = network_monitor_config.copy()
    config["params"]["expected_status"] = "down"
    monitor = NetworkMonitor(config, test_logger)
    
    mock_stats = MagicMock()
    mock_stats.isup = True
    mock_net_if_stats.return_value = {"eth0": mock_stats}
    
    monitor.check_status()
    assert not monitor.is_healthy
    assert f"CRITICAL: Network interface eth0 is up, but expected down." in caplog.text

@patch('redundancy.monitors.network_monitor.psutil.net_if_stats')
def test_check_status_interface_down_but_expected_up(mock_net_if_stats, network_monitor, caplog):
    caplog.set_level(logging.WARNING)
    mock_stats = MagicMock()
    mock_stats.isup = False
    mock_net_if_stats.return_value = {"eth0": mock_stats}
    
    network_monitor.check_status()
    assert not network_monitor.is_healthy
    assert f"CRITICAL: Network interface eth0 is down, but expected up." in caplog.text

@patch('redundancy.monitors.network_monitor.psutil.net_if_stats')
def test_check_status_interface_not_found(mock_net_if_stats, network_monitor, caplog):
    caplog.set_level(logging.ERROR)
    mock_net_if_stats.return_value = {} # Interface not in stats
    
    network_monitor.check_status()
    assert not network_monitor.is_healthy
    assert f"ERROR: Network interface eth0 not found." in caplog.text

@patch('redundancy.monitors.network_monitor.psutil.net_if_stats')
def test_check_status_psutil_exception(mock_net_if_stats, network_monitor, caplog):
    caplog.set_level(logging.ERROR)
    mock_net_if_stats.side_effect = Exception("psutil error")
    
    network_monitor.check_status()
    assert not network_monitor.is_healthy
    assert "ERROR: Error checking network interface eth0: psutil error" in caplog.text

def test_trigger_redundancy_healthy(network_monitor, caplog):
    caplog.set_level(logging.INFO)
    network_monitor.is_healthy = True
    network_monitor.trigger_redundancy()
    # No critical log expected if healthy
    assert not any(record.levelno == logging.CRITICAL for record in caplog.records)
    assert "Redundancy action for TestNetInterface: Currently healthy." in caplog.text


def test_trigger_redundancy_unhealthy(network_monitor, caplog):
    caplog.set_level(logging.CRITICAL)
    network_monitor.is_healthy = False
    network_monitor.trigger_redundancy()
    assert f"CRITICAL: Triggering redundancy action for unhealthy network interface TestNetInterface (eth0)." in caplog.text

def test_network_monitor_invalid_config_missing_interface(caplog):
    caplog.set_level(logging.ERROR)
    config = {
        "name": "TestNetInterfaceMissing",
        "type": "network",
        "params": {
            # "interface_name": "eth0", # Missing
            "expected_status": "up"
        }
    }
    with pytest.raises(ValueError) as excinfo:
        NetworkMonitor(config, test_logger)
    assert "Missing 'interface_name' in network monitor parameters" in str(excinfo.value)
    # Check if the core message is part of any log record, rather than exact match of caplog.text
    assert any(f"Missing 'interface_name' in network monitor parameters for {config['name']}" in record.message for record in caplog.records if record.levelname == "ERROR")

def test_network_monitor_invalid_config_missing_status(caplog):
    caplog.set_level(logging.INFO) # Check for info log about default
    config = {
        "name": "TestNetInterfaceMissingStatus",
        "type": "network",
        "params": {
            "interface_name": "eth0"
            # "expected_status": "up" # Missing, should default to "up"
        }
    }
    monitor = NetworkMonitor(config, test_logger)
    assert monitor.expected_status == "up" # Check default is applied
    # Check that the initialization log reflects the default being used.
    assert any(f"Initialized NetworkMonitor '{config['name']}' for interface '{config['params']['interface_name']}', expecting status 'up'" in record.message for record in caplog.records if record.levelname == "INFO")

def test_network_monitor_invalid_config_bad_status_value(caplog):
    caplog.set_level(logging.ERROR)
    config = {
        "name": "TestNetInterfaceBadStatus",
        "type": "network",
        "params": {
            "interface_name": "eth0",
            "expected_status": "maybe" # Invalid value
        }
    }
    with pytest.raises(ValueError) as excinfo:
        NetworkMonitor(config, test_logger)
    assert "Invalid 'expected_status' value 'maybe'" in str(excinfo.value)
    # Check if the core message is part of any log record
    assert any(f"Invalid 'expected_status' value 'maybe' for {config['name']}. Must be 'up' or 'down'" in record.message for record in caplog.records if record.levelname == "ERROR")

