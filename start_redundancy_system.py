#!/usr/bin/env python3
"""
Professional Hardware Redundancy System Startup Script
Production-ready startup script for the redundancy management system
"""

import os
import sys
import argparse
import subprocess
import time
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Error: Python 3.7 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'flask', 'yaml', 'psutil', 'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - missing")
    
    if missing_packages:
        print(f"\n📦 Missing packages: {', '.join(missing_packages)}")
        print("   Run: pip install flask pyyaml psutil requests")
        return False
    
    return True

def create_directories():
    """Create necessary directories"""
    directories = [
        'logs',
        'templates',
        'static',
        'config'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 Created directory: {directory}")

def create_default_config():
    """Create default configuration if it doesn't exist"""
    config_file = "redundancy_config.yaml"
    
    if os.path.exists(config_file):
        print(f"✅ Configuration file exists: {config_file}")
        return True
    
    print(f"📝 Creating default configuration: {config_file}")
    
    default_config = """# Professional Hardware Redundancy System Configuration
# Configure your applications and their monitoring requirements

applications:
  # Example Primary Application
  primary_app:
    description: "Primary Application Server"
    priority: 1
    is_primary: true
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: "secondary_app"
    monitors: []
      # Example monitors (uncomment and configure as needed):
      # - name: "disk_monitor"
      #   type: "disk"
      #   params:
      #     path: "/"
      #     threshold_percentage: 90
      # - name: "network_monitor"
      #   type: "network"
      #   params:
      #     interface_name: "eth0"
      #     expected_status: "up"
      # - name: "http_monitor"
      #   type: "http"
      #   params:
      #     url: "http://localhost:8080/health"
      #     expected_status_code: 200
      #     timeout_seconds: 10

  # Example Secondary Application
  secondary_app:
    description: "Secondary Application Server (Backup)"
    priority: 2
    is_primary: false
    auto_failover: true
    failover_threshold: 3
    recovery_threshold: 2
    failover_target: null
    monitors: []
      # Configure monitors for your secondary application
"""
    
    try:
        with open(config_file, 'w') as f:
            f.write(default_config)
        print(f"✅ Default configuration created: {config_file}")
        return True
    except Exception as e:
        print(f"❌ Failed to create configuration: {e}")
        return False

def validate_configuration():
    """Validate the configuration file"""
    config_file = "redundancy_config.yaml"
    
    try:
        import yaml
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        if not config or 'applications' not in config:
            print("⚠️  Warning: Configuration file is empty or invalid")
            return False
        
        app_count = len(config['applications'])
        print(f"✅ Configuration valid: {app_count} applications configured")
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False

def start_web_interface(port=5001, debug=False):
    """Start the web interface"""
    print(f"🚀 Starting Professional Redundancy Management System")
    print("=" * 70)
    print(f"🌐 Web Interface: http://localhost:{port}")
    print(f"📊 Dashboard: http://localhost:{port}/")
    print(f"🔧 Applications: http://localhost:{port}/applications")
    print(f"📡 Monitors: http://localhost:{port}/monitors")
    print(f"📋 Events: http://localhost:{port}/events")
    print("=" * 70)
    print("Press Ctrl+C to stop the system")
    print()
    
    try:
        # Import and run the web interface
        from redundancy_web_ui import app, initialize_manager
        
        # Initialize the redundancy manager
        manager = initialize_manager()
        
        # Start the Flask app
        app.run(host='0.0.0.0', port=port, debug=debug)
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down Redundancy Management System...")
    except Exception as e:
        print(f"❌ Error starting web interface: {e}")
        return False
    
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    packages = [
        'flask>=2.3.0',
        'pyyaml>=6.0',
        'psutil>=5.9.0',
        'requests>=2.31.0'
    ]
    
    try:
        for package in packages:
            print(f"   Installing {package}...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', package
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        print("✅ All dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def show_system_info():
    """Show system information"""
    print("🖥️  System Information:")
    print("-" * 30)
    
    try:
        import psutil
        
        # CPU info
        cpu_count = psutil.cpu_count()
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"   CPU: {cpu_count} cores, {cpu_percent}% usage")
        
        # Memory info
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"   Memory: {memory_gb:.1f} GB total, {memory.percent}% used")
        
        # Disk info
        disk = psutil.disk_usage('/')
        disk_gb = disk.total / (1024**3)
        print(f"   Disk: {disk_gb:.1f} GB total, {disk.percent}% used")
        
        # Network interfaces
        interfaces = list(psutil.net_if_addrs().keys())
        print(f"   Network: {len(interfaces)} interfaces ({', '.join(interfaces[:3])}{'...' if len(interfaces) > 3 else ''})")
        
    except ImportError:
        print("   System info unavailable (psutil not installed)")
    except Exception as e:
        print(f"   Error getting system info: {e}")

def main():
    """Main startup function"""
    parser = argparse.ArgumentParser(
        description='Professional Hardware Redundancy Management System',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python start_redundancy_system.py                    # Start with default settings
  python start_redundancy_system.py --port 8080       # Start on custom port
  python start_redundancy_system.py --install-deps    # Install dependencies first
  python start_redundancy_system.py --debug           # Start in debug mode
  python start_redundancy_system.py --check-only      # Check system without starting
        """
    )
    
    parser.add_argument('--port', type=int, default=5001,
                       help='Port to run web interface on (default: 5001)')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode')
    parser.add_argument('--install-deps', action='store_true',
                       help='Install dependencies before starting')
    parser.add_argument('--check-only', action='store_true',
                       help='Check system requirements without starting')
    parser.add_argument('--show-info', action='store_true',
                       help='Show system information')
    
    args = parser.parse_args()
    
    print("🛡️  Professional Hardware Redundancy Management System")
    print("=" * 70)
    
    # Show system info if requested
    if args.show_info:
        show_system_info()
        print()
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install dependencies if requested
    if args.install_deps:
        if not install_dependencies():
            return 1
    
    # Check dependencies
    if not check_dependencies():
        print("\n💡 Tip: Run with --install-deps to automatically install missing packages")
        return 1
    
    # Create directories
    create_directories()
    
    # Create default configuration
    if not create_default_config():
        return 1
    
    # Validate configuration
    if not validate_configuration():
        print("⚠️  Warning: Please check your configuration file")
    
    # If check-only mode, exit here
    if args.check_only:
        print("\n✅ System check completed successfully")
        return 0
    
    # Start the web interface
    if not start_web_interface(port=args.port, debug=args.debug):
        return 1
    
    return 0

if __name__ == '__main__':
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n👋 Startup interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
