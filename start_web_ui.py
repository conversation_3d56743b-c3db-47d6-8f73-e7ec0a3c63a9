#!/usr/bin/env python3
"""
Startup script for the Hardware Redundancy Web UI
"""

import os
import sys
import subprocess
import argparse

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import flask
        import yaml
        import psutil
        import requests
        print("✓ All dependencies are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        print("Please install dependencies using: pip install -r web_ui/requirements.txt")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        'web_ui/templates',
        'web_ui/static',
        'logs'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")

def main():
    parser = argparse.ArgumentParser(description='Start Hardware Redundancy Web UI')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to (default: 5000)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--install-deps', action='store_true', help='Install dependencies before starting')
    
    args = parser.parse_args()
    
    print("🚀 Starting Hardware Redundancy Web UI...")
    print("=" * 50)
    
    # Install dependencies if requested
    if args.install_deps:
        print("📦 Installing dependencies...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'web_ui/requirements.txt'])
            print("✓ Dependencies installed successfully")
        except subprocess.CalledProcessError:
            print("✗ Failed to install dependencies")
            return 1
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Create directories
    create_directories()
    
    # Set environment variables
    os.environ['FLASK_APP'] = 'web_ui/app.py'
    if args.debug:
        os.environ['FLASK_ENV'] = 'development'
        os.environ['FLASK_DEBUG'] = '1'
    
    print(f"🌐 Starting web server on http://{args.host}:{args.port}")
    print("📊 Dashboard will be available at: http://localhost:5000")
    print("⚙️  Configuration page: http://localhost:5000/config")
    print("=" * 50)
    print("Press Ctrl+C to stop the server")
    print()
    
    try:
        # Import and run the Flask app
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from web_ui.app import app
        app.run(host=args.host, port=args.port, debug=args.debug)
    except KeyboardInterrupt:
        print("\n👋 Shutting down web server...")
    except Exception as e:
        print(f"✗ Error starting web server: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
