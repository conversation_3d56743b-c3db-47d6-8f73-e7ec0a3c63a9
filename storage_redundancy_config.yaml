storage_groups:
  application_data:
    auto_failover: true
    compression_enabled: true
    description: Application Data Storage Redundancy
    encryption_enabled: false
    exclude_patterns:
    - '*.tmp'
    - '*.log'
    - temp/*
    locations:
    - capacity_gb: 100.0
      host: localhost
      is_active: true
      is_primary: true
      name: primary_app_storage
      path: C:\MyApp\Data
      storage_type: application_data
    - capacity_gb: 100.0
      host: localhost
      is_active: true
      is_primary: false
      name: backup_app_storage
      path: D:\Backup\MyApp\Data
      storage_type: application_data
    - capacity_gb: 200.0
      host: backup-server
      is_active: true
      is_primary: false
      name: remote_app_storage
      path: \\backup-server\MyApp\Data
      storage_type: application_data
    replication_mode: real_time
    retention_days: 30
    storage_type: application_data
    sync_interval: 60
  configuration_backup:
    auto_failover: false
    compression_enabled: true
    description: Configuration Files Backup
    encryption_enabled: true
    exclude_patterns: []
    locations:
    - capacity_gb: 1.0
      host: localhost
      is_active: true
      is_primary: true
      name: primary_config
      path: C:\MyApp\Config
      storage_type: configuration_files
    - capacity_gb: 1.0
      host: localhost
      is_active: true
      is_primary: false
      name: backup_config
      path: D:\Backup\Config
      storage_type: configuration_files
    - capacity_gb: 5.0
      host: config-server
      is_active: true
      is_primary: false
      name: remote_config
      path: \\config-backup\Config
      storage_type: configuration_files
    replication_mode: on_change
    retention_days: 365
    storage_type: configuration_files
    sync_interval: 30
  database_storage:
    auto_failover: true
    compression_enabled: true
    description: Database Files Storage Redundancy
    encryption_enabled: true
    exclude_patterns:
    - '*.log'
    - '*.tmp'
    locations:
    - capacity_gb: 500.0
      host: localhost
      is_active: true
      is_primary: true
      name: primary_db_storage
      path: C:\Database\Data
      storage_type: database_files
    - capacity_gb: 500.0
      host: localhost
      is_active: true
      is_primary: false
      name: secondary_db_storage
      path: D:\Database\Backup
      storage_type: database_files
    - capacity_gb: 1000.0
      host: db-backup-server
      is_active: true
      is_primary: false
      name: remote_db_storage
      path: \\db-backup\Database\Data
      storage_type: database_files
    replication_mode: scheduled
    retention_days: 90
    storage_type: database_files
    sync_interval: 300
