#!/usr/bin/env python3
"""
Storage Redundancy Manager for Professional Redundancy System
Provides comprehensive storage redundancy for applications and databases
"""

import os
import sys
import json
import yaml
import time
import threading
import subprocess
import shutil
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import logging

class StorageType(Enum):
    LOCAL_DRIVE = "local_drive"
    NETWORK_SHARE = "network_share"
    DATABASE_FILES = "database_files"
    APPLICATION_DATA = "application_data"
    CONFIGURATION_FILES = "configuration_files"
    LOG_FILES = "log_files"

class ReplicationMode(Enum):
    REAL_TIME = "real_time"
    SCHEDULED = "scheduled"
    ON_CHANGE = "on_change"
    MANUAL = "manual"

class StorageState(Enum):
    HEALTHY = "healthy"
    SYNCING = "syncing"
    OUT_OF_SYNC = "out_of_sync"
    FAILED = "failed"
    OFFLINE = "offline"

@dataclass
class StorageLocation:
    """Storage location configuration"""
    name: str
    path: str
    host: str
    storage_type: StorageType
    is_primary: bool = True
    is_active: bool = True
    capacity_gb: Optional[float] = None
    used_gb: Optional[float] = None
    last_sync: Optional[datetime] = None
    sync_status: StorageState = StorageState.HEALTHY

@dataclass
class StorageGroup:
    """Storage group for redundancy"""
    name: str
    description: str
    storage_type: StorageType
    locations: List[StorageLocation]
    replication_mode: ReplicationMode
    sync_interval: int = 300  # seconds
    auto_failover: bool = True
    compression_enabled: bool = False
    encryption_enabled: bool = False
    retention_days: int = 30
    exclude_patterns: Optional[List[str]] = None

class StorageRedundancyManager:
    """Main storage redundancy management system"""

    def __init__(self, config_file: str = "storage_redundancy_config.yaml"):
        self.config_file = config_file
        self.storage_groups: Dict[str, StorageGroup] = {}
        self.sync_threads: Dict[str, threading.Thread] = {}
        self.monitoring_active = False
        self.lock = threading.Lock()

        # Setup logging
        self.logger = logging.getLogger('StorageRedundancyManager')
        handler = logging.FileHandler('logs/storage_redundancy.log')
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

        # Load configuration
        self.load_configuration()

    def load_configuration(self) -> bool:
        """Load storage redundancy configuration"""
        try:
            if not os.path.exists(self.config_file):
                self.logger.warning(f"Configuration file {self.config_file} not found. Creating default.")
                self.create_default_config()
                return True

            with open(self.config_file, 'r') as f:
                config_data = yaml.safe_load(f)

            self.storage_groups.clear()
            for group_name, group_config in config_data.get('storage_groups', {}).items():
                locations = []
                for loc_config in group_config.get('locations', []):
                    location = StorageLocation(
                        name=loc_config['name'],
                        path=loc_config['path'],
                        host=loc_config['host'],
                        storage_type=StorageType(loc_config['storage_type']),
                        is_primary=loc_config.get('is_primary', False),
                        is_active=loc_config.get('is_active', True),
                        capacity_gb=loc_config.get('capacity_gb'),
                        used_gb=loc_config.get('used_gb')
                    )
                    locations.append(location)

                storage_group = StorageGroup(
                    name=group_name,
                    description=group_config.get('description', ''),
                    storage_type=StorageType(group_config['storage_type']),
                    locations=locations,
                    replication_mode=ReplicationMode(group_config.get('replication_mode', 'scheduled')),
                    sync_interval=group_config.get('sync_interval', 300),
                    auto_failover=group_config.get('auto_failover', True),
                    compression_enabled=group_config.get('compression_enabled', False),
                    encryption_enabled=group_config.get('encryption_enabled', False),
                    retention_days=group_config.get('retention_days', 30),
                    exclude_patterns=group_config.get('exclude_patterns', [])
                )

                self.storage_groups[group_name] = storage_group

            self.logger.info(f"Loaded configuration for {len(self.storage_groups)} storage groups")
            return True

        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            return False

    def create_default_config(self):
        """Create default storage redundancy configuration"""
        default_config = {
            'storage_groups': {
                'application_data': {
                    'description': 'Application Data Storage Redundancy',
                    'storage_type': 'application_data',
                    'replication_mode': 'real_time',
                    'sync_interval': 60,
                    'auto_failover': True,
                    'compression_enabled': True,
                    'encryption_enabled': False,
                    'retention_days': 30,
                    'exclude_patterns': ['*.tmp', '*.log', 'temp/*'],
                    'locations': [
                        {
                            'name': 'primary_app_storage',
                            'path': 'C:\\MyApp\\Data',
                            'host': 'localhost',
                            'storage_type': 'application_data',
                            'is_primary': True,
                            'is_active': True,
                            'capacity_gb': 100.0
                        },
                        {
                            'name': 'backup_app_storage',
                            'path': 'D:\\Backup\\MyApp\\Data',
                            'host': 'localhost',
                            'storage_type': 'application_data',
                            'is_primary': False,
                            'is_active': True,
                            'capacity_gb': 100.0
                        },
                        {
                            'name': 'remote_app_storage',
                            'path': '\\\\backup-server\\MyApp\\Data',
                            'host': 'backup-server',
                            'storage_type': 'application_data',
                            'is_primary': False,
                            'is_active': True,
                            'capacity_gb': 200.0
                        }
                    ]
                },
                'database_storage': {
                    'description': 'Database Files Storage Redundancy',
                    'storage_type': 'database_files',
                    'replication_mode': 'scheduled',
                    'sync_interval': 300,
                    'auto_failover': True,
                    'compression_enabled': True,
                    'encryption_enabled': True,
                    'retention_days': 90,
                    'exclude_patterns': ['*.log', '*.tmp'],
                    'locations': [
                        {
                            'name': 'primary_db_storage',
                            'path': 'C:\\Database\\Data',
                            'host': 'localhost',
                            'storage_type': 'database_files',
                            'is_primary': True,
                            'is_active': True,
                            'capacity_gb': 500.0
                        },
                        {
                            'name': 'secondary_db_storage',
                            'path': 'D:\\Database\\Backup',
                            'host': 'localhost',
                            'storage_type': 'database_files',
                            'is_primary': False,
                            'is_active': True,
                            'capacity_gb': 500.0
                        },
                        {
                            'name': 'remote_db_storage',
                            'path': '\\\\db-backup\\Database\\Data',
                            'host': 'db-backup-server',
                            'storage_type': 'database_files',
                            'is_primary': False,
                            'is_active': True,
                            'capacity_gb': 1000.0
                        }
                    ]
                },
                'configuration_backup': {
                    'description': 'Configuration Files Backup',
                    'storage_type': 'configuration_files',
                    'replication_mode': 'on_change',
                    'sync_interval': 30,
                    'auto_failover': False,
                    'compression_enabled': True,
                    'encryption_enabled': True,
                    'retention_days': 365,
                    'exclude_patterns': [],
                    'locations': [
                        {
                            'name': 'primary_config',
                            'path': 'C:\\MyApp\\Config',
                            'host': 'localhost',
                            'storage_type': 'configuration_files',
                            'is_primary': True,
                            'is_active': True,
                            'capacity_gb': 1.0
                        },
                        {
                            'name': 'backup_config',
                            'path': 'D:\\Backup\\Config',
                            'host': 'localhost',
                            'storage_type': 'configuration_files',
                            'is_primary': False,
                            'is_active': True,
                            'capacity_gb': 1.0
                        },
                        {
                            'name': 'remote_config',
                            'path': '\\\\config-backup\\Config',
                            'host': 'config-server',
                            'storage_type': 'configuration_files',
                            'is_primary': False,
                            'is_active': True,
                            'capacity_gb': 5.0
                        }
                    ]
                }
            }
        }

        with open(self.config_file, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False, indent=2)

    def start_storage_monitoring(self, group_name: str) -> bool:
        """Start monitoring and syncing for a storage group"""
        if group_name not in self.storage_groups:
            return False

        if group_name in self.sync_threads and self.sync_threads[group_name].is_alive():
            return False  # Already running

        self.monitoring_active = True
        thread = threading.Thread(target=self._storage_sync_loop, args=(group_name,), daemon=True)
        thread.start()
        self.sync_threads[group_name] = thread

        self.logger.info(f"Started storage monitoring for group: {group_name}")
        return True

    def stop_storage_monitoring(self, group_name: str) -> bool:
        """Stop monitoring for a storage group"""
        if group_name in self.sync_threads:
            # Signal to stop
            self.monitoring_active = False
            # Wait for thread to finish
            if self.sync_threads[group_name].is_alive():
                self.sync_threads[group_name].join(timeout=5)
            del self.sync_threads[group_name]

        self.logger.info(f"Stopped storage monitoring for group: {group_name}")
        return True

    def _storage_sync_loop(self, group_name: str):
        """Main storage synchronization loop"""
        storage_group = self.storage_groups[group_name]

        while self.monitoring_active:
            try:
                # Check storage health
                self._check_storage_health(storage_group)

                # Perform synchronization based on mode
                if storage_group.replication_mode == ReplicationMode.REAL_TIME:
                    self._real_time_sync(storage_group)
                elif storage_group.replication_mode == ReplicationMode.SCHEDULED:
                    self._scheduled_sync(storage_group)
                elif storage_group.replication_mode == ReplicationMode.ON_CHANGE:
                    self._on_change_sync(storage_group)

                # Wait for next sync interval
                time.sleep(storage_group.sync_interval)

            except Exception as e:
                self.logger.error(f"Error in sync loop for {group_name}: {e}")
                time.sleep(30)  # Wait before retrying

    def _check_storage_health(self, storage_group: StorageGroup):
        """Check health of all storage locations in a group"""
        for location in storage_group.locations:
            try:
                # Check if path exists and is accessible
                if self._is_path_accessible(location.path, location.host):
                    location.sync_status = StorageState.HEALTHY

                    # Update capacity information
                    if location.host == 'localhost':
                        usage = shutil.disk_usage(location.path)
                        location.capacity_gb = usage.total / (1024**3)
                        location.used_gb = usage.used / (1024**3)
                else:
                    location.sync_status = StorageState.OFFLINE

            except Exception as e:
                self.logger.warning(f"Health check failed for {location.name}: {e}")
                location.sync_status = StorageState.FAILED

    def _is_path_accessible(self, path: str, host: str) -> bool:
        """Check if a storage path is accessible"""
        try:
            if host == 'localhost':
                return os.path.exists(path) and os.access(path, os.R_OK | os.W_OK)
            else:
                # For remote hosts, try to access the network path
                return os.path.exists(path)
        except Exception:
            return False

    def _real_time_sync(self, storage_group: StorageGroup):
        """Perform real-time synchronization"""
        primary_location = self._get_primary_location(storage_group)
        if not primary_location:
            return

        for location in storage_group.locations:
            if location != primary_location and location.is_active:
                try:
                    location.sync_status = StorageState.SYNCING

                    # Perform file synchronization
                    self._sync_files(primary_location, location, storage_group)

                    location.sync_status = StorageState.HEALTHY
                    location.last_sync = datetime.now()

                except Exception as e:
                    self.logger.error(f"Real-time sync failed from {primary_location.name} to {location.name}: {e}")
                    location.sync_status = StorageState.OUT_OF_SYNC

    def _scheduled_sync(self, storage_group: StorageGroup):
        """Perform scheduled synchronization"""
        primary_location = self._get_primary_location(storage_group)
        if not primary_location:
            return

        # Check if it's time for scheduled sync
        for location in storage_group.locations:
            if location != primary_location and location.is_active:
                if (not location.last_sync or
                    (datetime.now() - location.last_sync).seconds >= storage_group.sync_interval):

                    try:
                        location.sync_status = StorageState.SYNCING

                        # Perform incremental backup
                        self._incremental_sync(primary_location, location, storage_group)

                        location.sync_status = StorageState.HEALTHY
                        location.last_sync = datetime.now()

                    except Exception as e:
                        self.logger.error(f"Scheduled sync failed from {primary_location.name} to {location.name}: {e}")
                        location.sync_status = StorageState.OUT_OF_SYNC

    def _on_change_sync(self, storage_group: StorageGroup):
        """Perform synchronization when changes are detected"""
        primary_location = self._get_primary_location(storage_group)
        if not primary_location:
            return

        # Check for file changes using modification times
        if self._detect_changes(primary_location, storage_group):
            for location in storage_group.locations:
                if location != primary_location and location.is_active:
                    try:
                        location.sync_status = StorageState.SYNCING

                        # Sync only changed files
                        self._sync_changed_files(primary_location, location, storage_group)

                        location.sync_status = StorageState.HEALTHY
                        location.last_sync = datetime.now()

                    except Exception as e:
                        self.logger.error(f"Change-based sync failed from {primary_location.name} to {location.name}: {e}")
                        location.sync_status = StorageState.OUT_OF_SYNC

    def _get_primary_location(self, storage_group: StorageGroup) -> Optional[StorageLocation]:
        """Get the primary storage location for a group"""
        for location in storage_group.locations:
            if location.is_primary and location.is_active:
                return location
        return None

    def _sync_files(self, source: StorageLocation, target: StorageLocation, storage_group: StorageGroup):
        """Synchronize files between storage locations"""
        try:
            # Create target directory if it doesn't exist
            os.makedirs(target.path, exist_ok=True)

            # Use robocopy for Windows or rsync for Linux
            if os.name == 'nt':  # Windows
                self._windows_sync(source.path, target.path, storage_group)
            else:  # Linux/Unix
                self._linux_sync(source.path, target.path, storage_group)

        except Exception as e:
            self.logger.error(f"File sync failed: {e}")
            raise

    def _windows_sync(self, source_path: str, target_path: str, storage_group: StorageGroup):
        """Windows file synchronization using robocopy"""
        cmd = ['robocopy', source_path, target_path, '/MIR', '/R:3', '/W:5', '/MT:8']

        # Add exclusions
        if storage_group.exclude_patterns:
            for pattern in storage_group.exclude_patterns:
                cmd.extend(['/XF', pattern])

        # Add compression if enabled
        if storage_group.compression_enabled:
            cmd.append('/COMPRESS')

        # Execute robocopy
        result = subprocess.run(cmd, capture_output=True, text=True)

        # Robocopy exit codes 0-7 are success
        if result.returncode > 7:
            raise Exception(f"Robocopy failed with exit code {result.returncode}: {result.stderr}")

    def _linux_sync(self, source_path: str, target_path: str, storage_group: StorageGroup):
        """Linux file synchronization using rsync"""
        cmd = ['rsync', '-avz', '--delete']

        # Add exclusions
        if storage_group.exclude_patterns:
            for pattern in storage_group.exclude_patterns:
                cmd.extend(['--exclude', pattern])

        # Add compression if enabled
        if storage_group.compression_enabled:
            cmd.append('--compress')

        # Add source and target
        cmd.extend([source_path + '/', target_path + '/'])

        # Execute rsync
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            raise Exception(f"Rsync failed with exit code {result.returncode}: {result.stderr}")

    def _incremental_sync(self, source: StorageLocation, target: StorageLocation, storage_group: StorageGroup):
        """Perform incremental synchronization"""
        try:
            # Create incremental backup with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(target.path, f"backup_{timestamp}")

            # Perform incremental backup
            if os.name == 'nt':  # Windows
                cmd = ['robocopy', source.path, backup_path, '/MIR', '/R:3', '/W:5']
                if storage_group.exclude_patterns:
                    for pattern in storage_group.exclude_patterns:
                        cmd.extend(['/XF', pattern])
                subprocess.run(cmd, check=False)  # Robocopy has special exit codes
            else:  # Linux
                cmd = ['rsync', '-av', '--link-dest=' + target.path, source.path + '/', backup_path + '/']
                if storage_group.exclude_patterns:
                    for pattern in storage_group.exclude_patterns:
                        cmd.extend(['--exclude', pattern])
                subprocess.run(cmd, check=True)

            # Clean up old backups based on retention policy
            self._cleanup_old_backups(target.path, storage_group.retention_days)

        except Exception as e:
            self.logger.error(f"Incremental sync failed: {e}")
            raise

    def _sync_changed_files(self, source: StorageLocation, target: StorageLocation, storage_group: StorageGroup):
        """Synchronize only changed files"""
        try:
            # Use modification time comparison
            if os.name == 'nt':  # Windows
                cmd = ['robocopy', source.path, target.path, '/XO', '/R:3', '/W:5']
                if storage_group.exclude_patterns:
                    for pattern in storage_group.exclude_patterns:
                        cmd.extend(['/XF', pattern])
                subprocess.run(cmd, check=False)
            else:  # Linux
                cmd = ['rsync', '-av', '--update']
                if storage_group.exclude_patterns:
                    for pattern in storage_group.exclude_patterns:
                        cmd.extend(['--exclude', pattern])
                cmd.extend([source.path + '/', target.path + '/'])
                subprocess.run(cmd, check=True)

        except Exception as e:
            self.logger.error(f"Changed files sync failed: {e}")
            raise

    def _detect_changes(self, location: StorageLocation, storage_group: StorageGroup) -> bool:
        """Detect if files have changed since last sync"""
        try:
            # Simple change detection based on directory modification time
            if not location.last_sync:
                return True

            for root, dirs, files in os.walk(location.path):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.getmtime(file_path) > location.last_sync.timestamp():
                        return True

            return False

        except Exception as e:
            self.logger.error(f"Change detection failed: {e}")
            return True  # Assume changes if detection fails

    def _cleanup_old_backups(self, backup_path: str, retention_days: int):
        """Clean up old backup files based on retention policy"""
        try:
            cutoff_date = datetime.now() - timedelta(days=retention_days)

            for item in os.listdir(backup_path):
                item_path = os.path.join(backup_path, item)
                if os.path.isdir(item_path) and item.startswith('backup_'):
                    # Extract timestamp from backup folder name
                    try:
                        timestamp_str = item.replace('backup_', '')
                        backup_date = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")

                        if backup_date < cutoff_date:
                            shutil.rmtree(item_path)
                            self.logger.info(f"Cleaned up old backup: {item_path}")

                    except ValueError:
                        # Skip items that don't match the expected format
                        continue

        except Exception as e:
            self.logger.error(f"Backup cleanup failed: {e}")

    def manual_sync(self, group_name: str, source_location: str, target_location: str) -> bool:
        """Manually trigger synchronization between specific locations"""
        try:
            if group_name not in self.storage_groups:
                return False

            storage_group = self.storage_groups[group_name]

            # Find source and target locations
            source = None
            target = None

            for location in storage_group.locations:
                if location.name == source_location:
                    source = location
                elif location.name == target_location:
                    target = location

            if not source or not target:
                return False

            # Perform manual sync
            target.sync_status = StorageState.SYNCING
            self._sync_files(source, target, storage_group)
            target.sync_status = StorageState.HEALTHY
            target.last_sync = datetime.now()

            self.logger.info(f"Manual sync completed from {source_location} to {target_location}")
            return True

        except Exception as e:
            self.logger.error(f"Manual sync failed: {e}")
            return False

    def storage_failover(self, group_name: str, failed_location: str, target_location: str) -> bool:
        """Perform storage failover to a different location"""
        try:
            if group_name not in self.storage_groups:
                return False

            storage_group = self.storage_groups[group_name]

            # Find locations
            failed_loc = None
            target_loc = None

            for location in storage_group.locations:
                if location.name == failed_location:
                    failed_loc = location
                elif location.name == target_location:
                    target_loc = location

            if not failed_loc or not target_loc:
                return False

            # Mark failed location as offline
            failed_loc.is_active = False
            failed_loc.sync_status = StorageState.OFFLINE

            # Promote target location to primary if needed
            if failed_loc.is_primary:
                failed_loc.is_primary = False
                target_loc.is_primary = True

            # Ensure target location is active
            target_loc.is_active = True
            target_loc.sync_status = StorageState.HEALTHY

            self.logger.info(f"Storage failover completed from {failed_location} to {target_location}")
            return True

        except Exception as e:
            self.logger.error(f"Storage failover failed: {e}")
            return False

    def get_storage_status(self) -> Dict[str, Any]:
        """Get comprehensive storage status"""
        with self.lock:
            status = {
                'storage_groups': {},
                'monitoring_active': self.monitoring_active,
                'timestamp': datetime.now().isoformat()
            }

            for group_name, storage_group in self.storage_groups.items():
                group_status = {
                    'name': group_name,
                    'description': storage_group.description,
                    'storage_type': storage_group.storage_type.value,
                    'replication_mode': storage_group.replication_mode.value,
                    'sync_interval': storage_group.sync_interval,
                    'auto_failover': storage_group.auto_failover,
                    'compression_enabled': storage_group.compression_enabled,
                    'encryption_enabled': storage_group.encryption_enabled,
                    'locations': []
                }

                for location in storage_group.locations:
                    location_status = {
                        'name': location.name,
                        'path': location.path,
                        'host': location.host,
                        'storage_type': location.storage_type.value,
                        'is_primary': location.is_primary,
                        'is_active': location.is_active,
                        'capacity_gb': location.capacity_gb,
                        'used_gb': location.used_gb,
                        'sync_status': location.sync_status.value,
                        'last_sync': location.last_sync.isoformat() if location.last_sync else None
                    }
                    group_status['locations'].append(location_status)

                status['storage_groups'][group_name] = group_status

            return status

    def add_storage_group(self, group_config: Dict[str, Any]) -> bool:
        """Add a new storage group"""
        try:
            group_name = group_config['name']

            # Create storage locations
            locations = []
            for loc_config in group_config.get('locations', []):
                location = StorageLocation(
                    name=loc_config['name'],
                    path=loc_config['path'],
                    host=loc_config.get('host', 'localhost'),
                    storage_type=StorageType(loc_config['storage_type']),
                    is_primary=loc_config.get('is_primary', False),
                    is_active=loc_config.get('is_active', True),
                    capacity_gb=loc_config.get('capacity_gb')
                )
                locations.append(location)

            # Create storage group
            storage_group = StorageGroup(
                name=group_name,
                description=group_config.get('description', ''),
                storage_type=StorageType(group_config['storage_type']),
                locations=locations,
                replication_mode=ReplicationMode(group_config.get('replication_mode', 'scheduled')),
                sync_interval=group_config.get('sync_interval', 300),
                auto_failover=group_config.get('auto_failover', True),
                compression_enabled=group_config.get('compression_enabled', False),
                encryption_enabled=group_config.get('encryption_enabled', False),
                retention_days=group_config.get('retention_days', 30),
                exclude_patterns=group_config.get('exclude_patterns', [])
            )

            # Add to storage groups
            self.storage_groups[group_name] = storage_group

            # Save configuration
            self.save_configuration()

            self.logger.info(f"Added storage group: {group_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add storage group: {e}")
            return False

    def save_configuration(self) -> bool:
        """Save current configuration to file"""
        try:
            config_data = {'storage_groups': {}}

            for group_name, storage_group in self.storage_groups.items():
                locations_config = []
                for location in storage_group.locations:
                    location_config = {
                        'name': location.name,
                        'path': location.path,
                        'host': location.host,
                        'storage_type': location.storage_type.value,
                        'is_primary': location.is_primary,
                        'is_active': location.is_active,
                        'capacity_gb': location.capacity_gb
                    }
                    locations_config.append(location_config)

                group_config = {
                    'description': storage_group.description,
                    'storage_type': storage_group.storage_type.value,
                    'replication_mode': storage_group.replication_mode.value,
                    'sync_interval': storage_group.sync_interval,
                    'auto_failover': storage_group.auto_failover,
                    'compression_enabled': storage_group.compression_enabled,
                    'encryption_enabled': storage_group.encryption_enabled,
                    'retention_days': storage_group.retention_days,
                    'exclude_patterns': storage_group.exclude_patterns or [],
                    'locations': locations_config
                }

                config_data['storage_groups'][group_name] = group_config

            with open(self.config_file, 'w') as f:
                yaml.dump(config_data, f, default_flow_style=False, indent=2)

            self.logger.info("Configuration saved successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
            return False
