<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EBO Multi-Site Redundancy Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --success-color: #22c55e;
            --info-color: #06b6d4;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            zoom: 0.9;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 2rem;
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 100%;
            margin: 0;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: var(--text-primary);
            font-size: 1.75rem;
            font-weight: 700;
            margin-left: -0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .header h1 i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .header .subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            margin-top: 0.5rem;
            font-weight: 400;
        }

        .header-nav {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-right: -0.5rem;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: var(--radius-lg);
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-md);
        }

        .nav-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .nav-btn.secondary {
            background: var(--secondary-color);
        }

        .nav-btn.secondary:hover {
            background: #059669;
        }

        .container {
            max-width: 100%;
            margin: 2rem 0;
            padding: 0 2rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .card:hover::before {
            opacity: 1;
        }

        .card h3 {
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card h3 i {
            color: var(--primary-color);
            font-size: 1.25rem;
        }

        .status-overview {
            grid-column: 1 / -1;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .status-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
            padding: 2rem 1.5rem;
            border-radius: var(--radius-lg);
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .status-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--border-color);
            transition: all 0.3s ease;
        }

        .status-item.primary::before {
            background: linear-gradient(90deg, var(--success-color), #16a34a);
        }

        .status-item.healthy::before {
            background: linear-gradient(90deg, var(--info-color), #0891b2);
        }

        .status-item.unhealthy::before {
            background: linear-gradient(90deg, var(--danger-color), #dc2626);
        }

        .status-item:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .status-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--text-primary);
            display: block;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .sites-list {
            max-height: 450px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) transparent;
        }

        .sites-list::-webkit-scrollbar {
            width: 6px;
        }

        .sites-list::-webkit-scrollbar-track {
            background: transparent;
        }

        .sites-list::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .site-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.25rem;
            margin-bottom: 0.75rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: var(--radius-lg);
            border-left: 4px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            backdrop-filter: blur(10px);
        }

        .site-item:hover {
            transform: translateX(8px) scale(1.02);
            box-shadow: var(--shadow-lg);
            background: rgba(255, 255, 255, 0.98);
            z-index: 10;
        }

        .site-item.expanded {
            transform: scale(1.05);
            z-index: 20;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            background: rgba(255, 255, 255, 0.98);
        }

        .site-item.draggable {
            cursor: grab;
            position: relative;
            user-select: none;
        }

        .site-item.dragging {
            opacity: 0.8;
            transform: rotate(5deg) scale(1.1);
            z-index: 1000;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
            cursor: grabbing;
        }

        .site-item.drop-target {
            border: 2px dashed var(--primary-color);
            background: rgba(59, 130, 246, 0.1);
        }

        .site-item.primary {
            border-left-color: var(--success-color);
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.05));
        }

        .site-item.primary::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--success-color);
            animation: pulse-primary 2s ease-in-out infinite;
        }

        .site-item.primary .site-status i {
            animation: heartbeat 2s ease-in-out infinite;
        }

        .site-item.healthy {
            border-left-color: var(--info-color);
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(8, 145, 178, 0.05));
        }

        .site-item.healthy::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--info-color);
            animation: pulse-healthy 3s ease-in-out infinite;
        }

        .site-item.healthy .site-status i {
            animation: heartbeat 3s ease-in-out infinite;
        }

        .site-item.unhealthy {
            border-left-color: var(--danger-color);
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.05));
        }

        .site-item.unhealthy::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--danger-color);
            animation: pulse-error 1s ease-in-out infinite;
        }

        .site-item.unhealthy .site-status i {
            animation: heartbeat 1s ease-in-out infinite;
        }

        .site-info {
            flex: 1;
        }

        .site-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
        }

        .site-location {
            color: var(--text-secondary);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .site-location i {
            color: var(--primary-color);
        }

        .site-status {
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .site-status.primary {
            background: var(--success-color);
            color: white;
        }

        .site-status.healthy {
            background: var(--info-color);
            color: white;
        }

        .site-status.unhealthy {
            background: var(--danger-color);
            color: white;
        }

        .site-status.unknown {
            background: var(--text-secondary);
            color: white;
        }

        .site-status i {
            font-size: 0.75rem;
        }

        .actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.875rem 1.75rem;
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, var(--primary-dark), #1e40af);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary-color), #059669);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, #059669, #047857);
        }

        .btn-outline {
            background: rgba(255, 255, 255, 0.9);
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-3px);
        }

        .refresh-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-left: 1rem;
            color: var(--success-color);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .refresh-indicator i {
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Real-Time Live Status Animations */
        @keyframes pulse-primary {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
                background: var(--success-color);
            }
            50% {
                box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
                background: #16a34a;
            }
        }

        @keyframes pulse-healthy {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(6, 182, 212, 0.7);
                background: var(--info-color);
            }
            50% {
                box-shadow: 0 0 0 8px rgba(6, 182, 212, 0);
                background: #0891b2;
            }
        }

        @keyframes pulse-error {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
                background: var(--danger-color);
            }
            50% {
                box-shadow: 0 0 0 6px rgba(239, 68, 68, 0);
                background: #dc2626;
            }
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            25% { transform: scale(1.1); }
            50% { transform: scale(1); }
            75% { transform: scale(1.05); }
        }

        @keyframes slideInNotification {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutNotification {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        @keyframes connectionPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }

        .loading {
            animation: pulse 1.5s infinite;
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        .network-map {
            height: 320px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9));
            border-radius: var(--radius-lg);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-style: italic;
            border: 2px dashed var(--border-color);
            transition: all 0.3s ease;
        }

        .network-map:hover {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(16, 185, 129, 0.05));
        }

        .network-map i {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            animation: connectionPulse 2s ease-in-out infinite;
        }

        /* Real-Time Notifications */
        .notification-container {
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 9999;
            max-width: 400px;
        }

        .notification {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-lg);
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
            box-shadow: var(--shadow-xl);
            border-left: 4px solid var(--primary-color);
            animation: slideInNotification 0.5s ease-out;
            position: relative;
            overflow: hidden;
        }

        .notification.success {
            border-left-color: var(--success-color);
        }

        .notification.warning {
            border-left-color: var(--warning-color);
        }

        .notification.error {
            border-left-color: var(--danger-color);
        }

        .notification.info {
            border-left-color: var(--info-color);
        }

        .notification-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .notification-title {
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .notification-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 1.25rem;
            padding: 0;
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .notification-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: var(--text-primary);
        }

        .notification-message {
            color: var(--text-secondary);
            font-size: 0.875rem;
            line-height: 1.4;
        }

        .notification-timestamp {
            color: var(--text-muted);
            font-size: 0.75rem;
            margin-top: 0.5rem;
        }

        /* Interactive Elements */
        .interactive-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .interactive-card:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: var(--shadow-xl);
        }

        .interactive-card:active {
            transform: translateY(0) scale(0.98);
        }

        /* Live Performance Charts */
        .performance-chart {
            height: 100px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
            border-radius: var(--radius-md);
            position: relative;
            overflow: hidden;
            margin: 1rem 0;
        }

        .performance-chart::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            opacity: 0.3;
            animation: chartPulse 3s ease-in-out infinite;
        }

        @keyframes chartPulse {
            0%, 100% { height: 60%; opacity: 0.3; }
            50% { height: 80%; opacity: 0.5; }
        }

        /* Drag and Drop Styles */
        .drag-handle {
            cursor: grab;
            color: var(--text-muted);
            margin-right: 0.5rem;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .site-item:hover .drag-handle {
            opacity: 1;
        }

        .site-item.dragging .drag-handle {
            cursor: grabbing;
        }

        /* Site Details Expansion */
        .site-details {
            margin-top: 1.5rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(20px);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .site-details * {
            color: var(--text-primary) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .site-details .btn {
            background: var(--primary-color) !important;
            color: white !important;
            border: none !important;
            font-weight: 600 !important;
            text-shadow: none !important;
        }

        .site-details .btn:hover {
            background: var(--primary-dark) !important;
            transform: translateY(-1px);
        }

        .site-details .btn-outline {
            background: transparent !important;
            color: var(--primary-color) !important;
            border: 2px solid var(--primary-color) !important;
        }

        .site-details .btn-outline:hover {
            background: var(--primary-color) !important;
            color: white !important;
        }

        /* Priority Indicators */
        .priority-indicator {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            background: var(--text-muted);
        }

        .priority-indicator.priority-1 {
            background: var(--danger-color);
            animation: heartbeat 1s ease-in-out infinite;
        }

        .priority-indicator.priority-2 {
            background: var(--warning-color);
            animation: heartbeat 2s ease-in-out infinite;
        }

        .priority-indicator.priority-3 {
            background: var(--info-color);
            animation: heartbeat 3s ease-in-out infinite;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }

            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .header h1 {
                font-size: 1.875rem;
            }

            .container {
                padding: 0 1rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .status-grid {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 1rem;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                justify-content: center;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .card {
                background: rgba(31, 41, 55, 0.95);
                border: 1px solid rgba(75, 85, 99, 0.3);
            }

            .status-item {
                background: linear-gradient(135deg, rgba(31, 41, 55, 0.9) 0%, rgba(17, 24, 39, 0.9) 100%);
            }

            .site-item {
                background: rgba(31, 41, 55, 0.8);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div>
                <h1><i class="fas fa-globe-americas"></i>EBO Multi-Site Redundancy</h1>
                <div class="subtitle">Enterprise-Grade Multi-Site EcoStruxure Building Operation Redundancy Management</div>
            </div>
            <div class="header-nav">
                <div class="user-info" id="userInfo" style="display: none; margin-right: 1rem; padding: 0.5rem 1rem; background: rgba(59, 130, 246, 0.1); border-radius: var(--radius-lg); border: 1px solid rgba(59, 130, 246, 0.2);">
                    <span style="color: var(--text-primary); font-weight: 600;" id="userName">Loading...</span>
                    <span style="color: var(--text-secondary); font-size: 0.875rem; margin-left: 0.5rem;" id="userRole">Loading...</span>
                    <button onclick="logout()" style="margin-left: 1rem; padding: 0.25rem 0.5rem; background: var(--danger-color); color: white; border: none; border-radius: var(--radius-sm); cursor: pointer; font-size: 0.75rem;">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </div>
                <a href="/users" class="nav-btn" id="userManagementBtn" style="display: none;"><i class="fas fa-users-cog"></i>Users</a>
                <a href="/sites" class="nav-btn"><i class="fas fa-building"></i>Sites</a>
                <a href="/replicate-site" class="nav-btn secondary"><i class="fas fa-copy"></i>Replicate</a>
            </div>
        </div>
    </div>

    <!-- Real-Time Notifications -->
    <div class="notification-container" id="notificationContainer"></div>

    <div class="container">
        <!-- Status Overview -->
        <div class="dashboard-grid">
            <div class="card status-overview fade-in">
                <h3><i class="fas fa-chart-line"></i>Multi-Site Status Overview</h3>
                <div class="status-grid">
                    <div class="status-item" id="total-sites">
                        <span class="status-number" id="total-sites-count">-</span>
                        <div class="status-label">Total Sites</div>
                    </div>
                    <div class="status-item" id="healthy-sites">
                        <span class="status-number" id="healthy-sites-count">-</span>
                        <div class="status-label">Healthy Sites</div>
                    </div>
                    <div class="status-item" id="primary-site-status">
                        <span class="status-number" id="primary-site-name">-</span>
                        <div class="status-label">Primary Site</div>
                    </div>
                    <div class="status-item" id="monitoring-status">
                        <span class="status-number" id="monitoring-active">-</span>
                        <div class="status-label">Monitoring</div>
                    </div>
                </div>
                <div class="refresh-indicator" id="last-update">
                    <i class="fas fa-sync-alt"></i>
                    Last updated: <span id="update-time">-</span>
                </div>
            </div>

            <!-- Sites List -->
            <div class="card fade-in">
                <h3><i class="fas fa-building"></i>Sites Status</h3>
                <div class="sites-list" id="sites-list">
                    <div class="loading">Loading sites...</div>
                </div>
                <div class="actions">
                    <a href="/sites" class="btn btn-primary"><i class="fas fa-eye"></i>View All Sites</a>
                    <button class="btn btn-outline" onclick="enhancedRefreshData()"><i class="fas fa-sync-alt"></i>Refresh</button>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card fade-in">
                <h3><i class="fas fa-bolt"></i>Quick Actions</h3>
                <div class="actions" style="margin-top: 1rem; flex-direction: column;">
                    <a href="/sites" class="btn btn-primary"><i class="fas fa-cogs"></i>Manage Sites</a>
                    <a href="/replicate-site" class="btn btn-secondary"><i class="fas fa-copy"></i>Replicate Site</a>
                    <a href="/safekit-console" class="btn btn-success"><i class="fas fa-shield-alt"></i>SafeKit Console</a>
                    <a href="/safekit-cluster" class="btn btn-success" style="opacity: 0.8;"><i class="fas fa-tachometer-alt"></i>SafeKit Dashboard</a>
                    <a href="/failover-cluster" class="btn btn-primary"><i class="fas fa-server"></i>Advanced Cluster</a>
                    <a href="/replication" class="btn btn-primary"><i class="fas fa-sync"></i>Replication Status</a>
                    <a href="/network" class="btn btn-primary"><i class="fas fa-network-wired"></i>Network Topology</a>
                    <button class="btn btn-outline" onclick="testFailover()"><i class="fas fa-exchange-alt"></i>Test Failover</button>
                </div>
            </div>

            <!-- Network Overview -->
            <div class="card fade-in">
                <h3><i class="fas fa-network-wired"></i>Network Overview</h3>
                <div class="network-map">
                    <i class="fas fa-project-diagram"></i>
                    <div>Network topology visualization</div>
                    <small>Click "Network Topology" for detailed view</small>
                </div>
                <div class="actions">
                    <a href="/network" class="btn btn-primary"><i class="fas fa-eye"></i>View Network</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let refreshInterval;
        let draggedElement = null;
        let expandedSite = null;
        let lastStatusData = null;

        // Real-Time Notification System
        function showNotification(type, title, message, duration = 5000) {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            const iconMap = {
                success: 'fas fa-check-circle',
                warning: 'fas fa-exclamation-triangle',
                error: 'fas fa-times-circle',
                info: 'fas fa-info-circle'
            };

            notification.innerHTML = `
                <div class="notification-header">
                    <div class="notification-title">
                        <i class="${iconMap[type]}"></i>
                        ${title}
                    </div>
                    <button class="notification-close" onclick="closeNotification(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="notification-message">${message}</div>
                <div class="notification-timestamp">${new Date().toLocaleTimeString()}</div>
            `;

            container.appendChild(notification);

            // Auto-remove after duration
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideOutNotification 0.5s ease-out';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 500);
                }
            }, duration);
        }

        function closeNotification(button) {
            const notification = button.closest('.notification');
            notification.style.animation = 'slideOutNotification 0.5s ease-out';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 500);
        }

        // Drag and Drop Functionality
        function makeSiteDraggable(siteElement, siteId) {
            siteElement.draggable = true;
            siteElement.classList.add('draggable');

            // Add drag handle
            const dragHandle = document.createElement('i');
            dragHandle.className = 'fas fa-grip-vertical drag-handle';
            siteElement.insertBefore(dragHandle, siteElement.firstChild);

            siteElement.addEventListener('dragstart', (e) => {
                draggedElement = siteElement;
                siteElement.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', siteElement.outerHTML);
                e.dataTransfer.setData('text/plain', siteId);

                showNotification('info', 'Drag Started', `Dragging ${siteId} to change priority`);
            });

            siteElement.addEventListener('dragend', (e) => {
                siteElement.classList.remove('dragging');
                document.querySelectorAll('.site-item').forEach(item => {
                    item.classList.remove('drop-target');
                });
                draggedElement = null;
            });

            siteElement.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                if (siteElement !== draggedElement) {
                    siteElement.classList.add('drop-target');
                }
            });

            siteElement.addEventListener('dragleave', (e) => {
                siteElement.classList.remove('drop-target');
            });

            siteElement.addEventListener('drop', (e) => {
                e.preventDefault();
                siteElement.classList.remove('drop-target');

                if (draggedElement && siteElement !== draggedElement) {
                    const draggedId = e.dataTransfer.getData('text/plain');
                    const targetId = siteElement.dataset.siteId;

                    // Swap positions
                    const parent = siteElement.parentNode;
                    const draggedIndex = Array.from(parent.children).indexOf(draggedElement);
                    const targetIndex = Array.from(parent.children).indexOf(siteElement);

                    if (draggedIndex < targetIndex) {
                        parent.insertBefore(draggedElement, siteElement.nextSibling);
                    } else {
                        parent.insertBefore(draggedElement, siteElement);
                    }

                    showNotification('success', 'Priority Changed', `${draggedId} priority updated`);

                    // Here you would typically send an API call to update the priority
                    updateSitePriority(draggedId, targetId);
                }
            });
        }

        // Site Expansion Functionality
        function toggleSiteExpansion(siteElement, siteId) {
            if (expandedSite === siteId) {
                // Collapse
                siteElement.classList.remove('expanded');
                expandedSite = null;
                removeSiteDetails(siteElement);
            } else {
                // Collapse any other expanded site
                if (expandedSite) {
                    const prevExpanded = document.querySelector(`[data-site-id="${expandedSite}"]`);
                    if (prevExpanded) {
                        prevExpanded.classList.remove('expanded');
                        removeSiteDetails(prevExpanded);
                    }
                }

                // Expand this site
                siteElement.classList.add('expanded');
                expandedSite = siteId;
                addSiteDetails(siteElement, siteId);

                showNotification('info', 'Site Expanded', `Viewing detailed information for ${siteId}`);
            }
        }

        function addSiteDetails(siteElement, siteId) {
            const detailsDiv = document.createElement('div');
            detailsDiv.className = 'site-details fade-in';
            detailsDiv.innerHTML = `
                <div class="performance-chart">
                    <div style="position: absolute; top: 0.5rem; left: 0.5rem; color: var(--text-primary); font-weight: 600; font-size: 0.875rem; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                        Live Performance
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 1rem; margin-top: 1rem; background: rgba(255, 255, 255, 0.9); padding: 1rem; border-radius: var(--radius-md); backdrop-filter: blur(10px);">
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: 700; color: var(--success-color); text-shadow: 0 1px 2px rgba(0,0,0,0.1);">98%</div>
                        <div style="font-size: 0.875rem; color: var(--text-primary); font-weight: 500;">Uptime</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: 700; color: var(--info-color); text-shadow: 0 1px 2px rgba(0,0,0,0.1);">45ms</div>
                        <div style="font-size: 0.875rem; color: var(--text-primary); font-weight: 500;">Latency</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: 700; color: var(--warning-color); text-shadow: 0 1px 2px rgba(0,0,0,0.1);">67%</div>
                        <div style="font-size: 0.875rem; color: var(--text-primary); font-weight: 500;">CPU</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: 700; color: var(--primary-color); text-shadow: 0 1px 2px rgba(0,0,0,0.1);">5.2GB</div>
                        <div style="font-size: 0.875rem; color: var(--text-primary); font-weight: 500;">Memory</div>
                    </div>
                </div>
                <div style="margin-top: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap; background: rgba(255, 255, 255, 0.8); padding: 1rem; border-radius: var(--radius-md); backdrop-filter: blur(10px);">
                    <button class="btn btn-primary" style="padding: 0.5rem 1rem; font-size: 0.875rem; font-weight: 600;" onclick="viewSiteDetails('${siteId}')">
                        <i class="fas fa-eye"></i> View Details
                    </button>
                    <button class="btn btn-outline" style="padding: 0.5rem 1rem; font-size: 0.875rem; font-weight: 600;" onclick="testSiteConnection('${siteId}')">
                        <i class="fas fa-wifi"></i> Test Connection
                    </button>
                </div>
            `;

            siteElement.appendChild(detailsDiv);
        }

        function removeSiteDetails(siteElement) {
            const details = siteElement.querySelector('.site-details');
            if (details) {
                details.remove();
            }
        }

        function refreshData() {
            // Update status overview
            fetch('/api/multisite/status')
                .then(response => response.json())
                .then(data => {
                    updateStatusOverview(data);
                })
                .catch(error => {
                    console.error('Error fetching status:', error);
                });

            // Update sites list
            fetch('/api/multisite/sites')
                .then(response => response.json())
                .then(data => {
                    updateSitesList(data);
                })
                .catch(error => {
                    console.error('Error fetching sites:', error);
                });
        }

        function updateStatusOverview(data) {
            document.getElementById('total-sites-count').textContent = data.total_sites || 0;
            document.getElementById('healthy-sites-count').textContent = data.healthy_sites || 0;
            document.getElementById('primary-site-name').textContent = data.current_primary_site || 'None';
            document.getElementById('monitoring-active').textContent = data.monitoring_active ? 'Active' : 'Inactive';
            document.getElementById('update-time').textContent = new Date().toLocaleTimeString();

            // Update status item classes
            const totalSitesItem = document.getElementById('total-sites');
            const healthySitesItem = document.getElementById('healthy-sites');
            const primarySiteItem = document.getElementById('primary-site-status');
            const monitoringItem = document.getElementById('monitoring-status');

            // Reset classes
            [totalSitesItem, healthySitesItem, primarySiteItem, monitoringItem].forEach(item => {
                item.className = 'status-item';
            });

            // Add appropriate classes
            if (data.healthy_sites === data.total_sites && data.total_sites > 0) {
                healthySitesItem.classList.add('healthy');
            } else if (data.healthy_sites < data.total_sites) {
                healthySitesItem.classList.add('unhealthy');
            }

            if (data.current_primary_site) {
                primarySiteItem.classList.add('primary');
            }

            if (data.monitoring_active) {
                monitoringItem.classList.add('healthy');
            } else {
                monitoringItem.classList.add('unhealthy');
            }
        }

        function updateSitesList(sites) {
            const sitesList = document.getElementById('sites-list');
            sitesList.innerHTML = '';

            Object.entries(sites).forEach(([siteId, site], index) => {
                const siteItem = document.createElement('div');
                siteItem.className = `site-item ${site.status} fade-in interactive-card`;
                siteItem.style.animationDelay = `${index * 0.1}s`;
                siteItem.dataset.siteId = siteId;

                const statusIcon = getStatusIcon(site.status);
                const priority = site.priority || 999;

                siteItem.innerHTML = `
                    <div class="priority-indicator priority-${priority}"></div>
                    <div class="site-info">
                        <div class="site-name">${site.description}</div>
                        <div class="site-location">
                            <i class="fas fa-map-marker-alt"></i>
                            ${site.location?.city || 'Unknown'}, ${site.location?.country || 'Unknown'}
                        </div>
                    </div>
                    <div class="site-status ${site.status}">
                        <i class="${statusIcon}"></i>
                        ${site.status}
                    </div>
                `;

                // Add click handler for expansion
                siteItem.addEventListener('click', (e) => {
                    // Don't expand if clicking on drag handle
                    if (e.target.classList.contains('drag-handle')) {
                        return;
                    }

                    // Double-click to navigate, single-click to expand
                    if (e.detail === 1) {
                        setTimeout(() => {
                            if (e.detail === 1) {
                                toggleSiteExpansion(siteItem, siteId);
                            }
                        }, 200);
                    } else if (e.detail === 2) {
                        window.location.href = `/sites/${siteId}`;
                    }
                });

                // Make draggable for priority management
                makeSiteDraggable(siteItem, siteId);

                sitesList.appendChild(siteItem);
            });

            // Check for status changes and show notifications
            if (lastStatusData) {
                Object.entries(sites).forEach(([siteId, site]) => {
                    const lastSite = lastStatusData[siteId];
                    if (lastSite && lastSite.status !== site.status) {
                        const statusChange = `${lastSite.status} → ${site.status}`;
                        const notificationType = site.status === 'healthy' || site.status === 'primary' ? 'success' :
                                               site.status === 'unhealthy' ? 'error' : 'warning';

                        showNotification(
                            notificationType,
                            'Site Status Changed',
                            `${site.description} status changed: ${statusChange}`,
                            8000
                        );
                    }
                });
            }

            lastStatusData = sites;
        }

        function getStatusIcon(status) {
            switch(status) {
                case 'primary': return 'fas fa-crown';
                case 'healthy': return 'fas fa-check-circle';
                case 'unhealthy': return 'fas fa-exclamation-triangle';
                default: return 'fas fa-question-circle';
            }
        }

        function testFailover() {
            if (confirm('This will test the failover functionality. Continue?')) {
                showNotification('info', 'Failover Test', 'Initiating failover test...', 3000);

                // Simulate failover test
                setTimeout(() => {
                    showNotification('success', 'Failover Test Complete', 'Failover test completed successfully', 5000);
                }, 3000);
            }
        }

        // Utility Functions
        function updateSitePriority(draggedId, targetId) {
            // Simulate API call to update site priority
            showNotification('info', 'Updating Priority', 'Sending priority update to server...', 2000);

            setTimeout(() => {
                showNotification('success', 'Priority Updated', `Site priority order updated successfully`, 3000);
            }, 1000);
        }

        function viewSiteDetails(siteId) {
            showNotification('info', 'Navigation', `Opening detailed view for ${siteId}...`, 2000);
            setTimeout(() => {
                window.location.href = `/sites/${siteId}`;
            }, 500);
        }

        function testSiteConnection(siteId) {
            showNotification('info', 'Connection Test', `Testing connection to ${siteId}...`, 3000);

            // Simulate connection test
            setTimeout(() => {
                const success = Math.random() > 0.2; // 80% success rate
                if (success) {
                    showNotification('success', 'Connection Test', `Connection to ${siteId} successful (45ms)`, 4000);
                } else {
                    showNotification('error', 'Connection Test', `Connection to ${siteId} failed - timeout`, 6000);
                }
            }, 2000);
        }

        // Enhanced refresh with live updates
        function enhancedRefreshData() {
            // Show refresh indicator
            const refreshIcon = document.querySelector('.refresh-indicator i');
            if (refreshIcon) {
                refreshIcon.style.animation = 'spin 1s linear infinite';
            }

            refreshData();

            // Hide refresh indicator after a delay
            setTimeout(() => {
                if (refreshIcon) {
                    refreshIcon.style.animation = '';
                }
            }, 1000);
        }

        // Force refresh function for when site changes occur
        function forceRefreshDashboard() {
            // Clear any cached data
            localStorage.removeItem('dashboard_cache');

            // Show refresh indicator
            const refreshIcon = document.querySelector('.refresh-indicator i');
            if (refreshIcon) {
                refreshIcon.style.animation = 'spin 1s linear infinite';
            }

            // Refresh both status and sites data
            refreshData();

            // Hide refresh indicator after a delay
            setTimeout(() => {
                if (refreshIcon) {
                    refreshIcon.style.animation = '';
                }
            }, 1000);

            // Show notification that data is being refreshed
            showNotification('info', 'Dashboard Updated', 'Dashboard refreshed to reflect latest changes', 3000);
        }

        // Simulate real-time events
        function simulateRealTimeEvents() {
            const events = [
                { type: 'info', title: 'Heartbeat', message: 'All sites responding normally' },
                { type: 'success', title: 'Backup Complete', message: 'Scheduled backup completed successfully' },
                { type: 'warning', title: 'High CPU Usage', message: 'Site A CPU usage at 85%' },
                { type: 'info', title: 'Replication Sync', message: 'Data synchronization in progress' }
            ];

            setInterval(() => {
                if (Math.random() > 0.7) { // 30% chance every interval
                    const event = events[Math.floor(Math.random() * events.length)];
                    showNotification(event.type, event.title, event.message, 4000);
                }
            }, 15000); // Every 15 seconds
        }

        function loadCurrentUser() {
            fetch('/api/auth/current-user')
                .then(response => response.json())
                .then(data => {
                    if (data.user) {
                        document.getElementById('userName').textContent = data.user.full_name;
                        document.getElementById('userRole').textContent = data.user.role.toUpperCase();
                        document.getElementById('userInfo').style.display = 'block';

                        // Show user management button for admins
                        if (data.user.role === 'admin') {
                            document.getElementById('userManagementBtn').style.display = 'block';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading current user:', error);
                    // Redirect to login if not authenticated
                    window.location.href = '/login';
                });
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('/api/auth/logout', {
                    method: 'POST'
                })
                .then(() => {
                    window.location.href = '/login';
                })
                .catch(error => {
                    console.error('Error during logout:', error);
                    window.location.href = '/login';
                });
            }
        }

        // Make forceRefreshDashboard available globally
        window.forceRefreshDashboard = forceRefreshDashboard;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentUser();
            refreshData();

            // Show welcome notification
            setTimeout(() => {
                showNotification('success', 'Dashboard Loaded', 'Real-time monitoring active with live updates', 4000);
            }, 1000);

            // Auto-refresh every 30 seconds with enhanced features
            refreshInterval = setInterval(enhancedRefreshData, 30000);

            // Start real-time event simulation
            setTimeout(simulateRealTimeEvents, 5000);

            // Add keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'r':
                            e.preventDefault();
                            enhancedRefreshData();
                            showNotification('info', 'Manual Refresh', 'Dashboard data refreshed', 2000);
                            break;
                        case 'n':
                            e.preventDefault();
                            window.location.href = '/sites';
                            break;
                    }
                }

                if (e.key === 'Escape') {
                    // Close expanded site if any
                    if (expandedSite) {
                        const expandedElement = document.querySelector(`[data-site-id="${expandedSite}"]`);
                        if (expandedElement) {
                            toggleSiteExpansion(expandedElement, expandedSite);
                        }
                    }
                }
            });

            // Add visual feedback for interactive elements
            document.addEventListener('mouseover', (e) => {
                if (e.target.classList.contains('interactive-card')) {
                    e.target.style.transform = 'translateY(-2px) scale(1.02)';
                }
            });

            document.addEventListener('mouseout', (e) => {
                if (e.target.classList.contains('interactive-card') && !e.target.classList.contains('expanded')) {
                    e.target.style.transform = '';
                }
            });
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
