{% extends "redundancy_base.html" %}

{% block title %}Dashboard - Professional Redundancy Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                System Dashboard
            </h1>
            <div>
                <button id="startMonitoring" class="btn btn-success me-2" {% if status.monitoring_active %}disabled{% endif %}>
                    <i class="fas fa-play me-1"></i>Start Monitoring
                </button>
                <button id="stopMonitoring" class="btn btn-danger me-2" {% if not status.monitoring_active %}disabled{% endif %}>
                    <i class="fas fa-stop me-1"></i>Stop Monitoring
                </button>
                <button id="refreshDashboard" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- System Metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-server fa-2x text-primary mb-2"></i>
                <div class="metric-value text-primary" id="totalApplications">{{ status.applications|length }}</div>
                <div class="metric-label">Total Applications</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <div class="metric-value text-success" id="healthyApplications">-</div>
                <div class="metric-label">Healthy Applications</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                <div class="metric-value text-warning" id="unhealthyApplications">-</div>
                <div class="metric-label">Unhealthy Applications</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-heartbeat fa-2x text-info mb-2"></i>
                <div class="metric-value text-info" id="totalMonitors">-</div>
                <div class="metric-label">Active Monitors</div>
            </div>
        </div>
    </div>
</div>

<!-- Application Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server me-2"></i>
                    Application Status Overview
                </h5>
            </div>
            <div class="card-body">
                <div id="applicationStatus">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">Loading application status...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Redundancy State & Recent Events -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Redundancy States
                </h5>
            </div>
            <div class="card-body">
                <div id="redundancyStates">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">Loading redundancy states...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bell me-2"></i>
                    Recent Events
                </h5>
            </div>
            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                <div id="recentEvents">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">Loading events...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Manual Failover Modal -->
<div class="modal fade" id="failoverModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Manual Failover</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="failoverForm">
                    <div class="mb-3">
                        <label for="sourceApp" class="form-label">Source Application</label>
                        <select class="form-select" id="sourceApp" required>
                            <option value="">Select application to failover from</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="targetApp" class="form-label">Target Application</label>
                        <select class="form-select" id="targetApp" required>
                            <option value="">Select failover target</option>
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This will immediately trigger a failover. Make sure you understand the implications.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="executeFailover">Execute Failover</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadDashboardData();
    
    $('#startMonitoring').click(startMonitoring);
    $('#stopMonitoring').click(stopMonitoring);
    $('#refreshDashboard').click(loadDashboardData);
    $('#executeFailover').click(executeFailover);
});

function loadDashboardData() {
    $.get('/api/status')
        .done(function(response) {
            if (response.success) {
                updateDashboard(response.data);
            } else {
                showAlert('danger', 'Failed to load dashboard data: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to connect to server');
        });
}

function updateDashboard(data) {
    updateMetrics(data);
    updateApplicationStatus(data.applications);
    updateRedundancyStates(data.applications);
    updateRecentEvents(data.recent_events);
    populateFailoverOptions(data.applications);
}

function updateMetrics(data) {
    const applications = data.applications;
    const appNames = Object.keys(applications);
    
    let healthyCount = 0;
    let unhealthyCount = 0;
    let totalMonitors = 0;
    
    appNames.forEach(appName => {
        const app = applications[appName];
        totalMonitors += app.config.monitors.length;
        
        if (app.overall_health === 'healthy') {
            healthyCount++;
        } else if (app.overall_health === 'unhealthy' || app.overall_health === 'critical') {
            unhealthyCount++;
        }
    });
    
    $('#totalApplications').text(appNames.length);
    $('#healthyApplications').text(healthyCount);
    $('#unhealthyApplications').text(unhealthyCount);
    $('#totalMonitors').text(totalMonitors);
}

function updateApplicationStatus(applications) {
    let html = '';
    
    if (Object.keys(applications).length === 0) {
        html = '<div class="text-center py-4"><p class="text-muted">No applications configured</p></div>';
    } else {
        html = '<div class="table-responsive"><table class="table table-hover">';
        html += '<thead><tr><th>Application</th><th>Status</th><th>State</th><th>Monitors</th><th>Last Check</th><th>Actions</th></tr></thead><tbody>';
        
        Object.entries(applications).forEach(([appName, app]) => {
            const statusClass = getStatusClass(app.overall_health);
            const stateClass = getRedundancyStateClass(app.current_state);
            const lastCheck = app.recent_results.length > 0 ? 
                formatTimestamp(app.recent_results[app.recent_results.length - 1].timestamp) : 'Never';
            
            html += `
                <tr>
                    <td>
                        <strong>${appName}</strong>
                        <br><small class="text-muted">${app.config.description}</small>
                        ${app.config.is_primary ? '<span class="badge bg-primary ms-2">Primary</span>' : '<span class="badge bg-secondary ms-2">Secondary</span>'}
                    </td>
                    <td>
                        <span class="status-indicator ${statusClass}"></span>
                        <span class="text-capitalize">${app.overall_health}</span>
                    </td>
                    <td>
                        <span class="redundancy-state ${stateClass}">${app.current_state.replace(/_/g, ' ')}</span>
                    </td>
                    <td>
                        <span class="badge bg-info">${app.config.monitors.length} monitors</span>
                    </td>
                    <td>${lastCheck}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="testApplication('${appName}')">
                            <i class="fas fa-vial"></i> Test
                        </button>
                        ${app.config.failover_target ? 
                            `<button class="btn btn-sm btn-outline-warning ms-1" onclick="showFailoverModal('${appName}', '${app.config.failover_target}')">
                                <i class="fas fa-exchange-alt"></i> Failover
                            </button>` : ''
                        }
                    </td>
                </tr>
            `;
        });
        html += '</tbody></table></div>';
    }
    
    $('#applicationStatus').html(html);
}

function updateRedundancyStates(applications) {
    let html = '';
    
    Object.entries(applications).forEach(([appName, app]) => {
        const stateClass = getRedundancyStateClass(app.current_state);
        const failureCount = app.failure_count;
        const successCount = app.success_count;
        
        html += `
            <div class="row mb-3">
                <div class="col-md-6">
                    <h6>${appName}</h6>
                    <span class="redundancy-state ${stateClass}">${app.current_state.replace(/_/g, ' ')}</span>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">
                        Failures: ${failureCount} | Successes: ${successCount}
                        <br>Threshold: ${app.config.failover_threshold} failures
                    </small>
                </div>
            </div>
        `;
    });
    
    if (html === '') {
        html = '<p class="text-muted">No applications configured</p>';
    }
    
    $('#redundancyStates').html(html);
}

function updateRecentEvents(events) {
    let html = '';
    
    if (events.length === 0) {
        html = '<p class="text-muted">No recent events</p>';
    } else {
        events.forEach(event => {
            const severityClass = event.severity.toLowerCase();
            html += `
                <div class="event-item event-${severityClass}">
                    <div class="d-flex justify-content-between">
                        <strong>${event.event_type.replace(/_/g, ' ')}</strong>
                        <small class="text-muted">${formatTimestamp(event.timestamp)}</small>
                    </div>
                    <div class="mt-1">
                        <small><strong>${event.application}:</strong> ${event.description}</small>
                    </div>
                </div>
            `;
        });
    }
    
    $('#recentEvents').html(html);
}

function populateFailoverOptions(applications) {
    const sourceSelect = $('#sourceApp');
    const targetSelect = $('#targetApp');
    
    sourceSelect.empty().append('<option value="">Select application to failover from</option>');
    targetSelect.empty().append('<option value="">Select failover target</option>');
    
    Object.entries(applications).forEach(([appName, app]) => {
        sourceSelect.append(`<option value="${appName}">${appName}</option>`);
        targetSelect.append(`<option value="${appName}">${appName}</option>`);
    });
}

function startMonitoring() {
    $.post('/api/monitoring/start')
        .done(function(response) {
            if (response.success) {
                showAlert('success', 'Monitoring started successfully');
                $('#startMonitoring').prop('disabled', true);
                $('#stopMonitoring').prop('disabled', false);
                loadDashboardData();
            } else {
                showAlert('danger', 'Failed to start monitoring: ' + response.message);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to start monitoring');
        });
}

function stopMonitoring() {
    $.post('/api/monitoring/stop')
        .done(function(response) {
            if (response.success) {
                showAlert('success', 'Monitoring stopped successfully');
                $('#startMonitoring').prop('disabled', false);
                $('#stopMonitoring').prop('disabled', true);
                loadDashboardData();
            } else {
                showAlert('danger', 'Failed to stop monitoring: ' + response.message);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to stop monitoring');
        });
}

function testApplication(appName) {
    showAlert('info', `Testing application: ${appName}`);
    // Implementation for testing specific application
}

function showFailoverModal(sourceApp, targetApp) {
    $('#sourceApp').val(sourceApp);
    $('#targetApp').val(targetApp);
    $('#failoverModal').modal('show');
}

function executeFailover() {
    const sourceApp = $('#sourceApp').val();
    const targetApp = $('#targetApp').val();
    
    if (!sourceApp || !targetApp) {
        showAlert('warning', 'Please select both source and target applications');
        return;
    }
    
    if (sourceApp === targetApp) {
        showAlert('warning', 'Source and target applications cannot be the same');
        return;
    }
    
    $.post('/api/failover', {
        app_name: sourceApp,
        target_app: targetApp
    })
    .done(function(response) {
        if (response.success) {
            showAlert('success', 'Failover initiated successfully');
            $('#failoverModal').modal('hide');
            loadDashboardData();
        } else {
            showAlert('danger', 'Failover failed: ' + response.message);
        }
    })
    .fail(function() {
        showAlert('danger', 'Failed to execute failover');
    });
}
</script>
{% endblock %}
