{% extends "redundancy_base.html" %}

{% block title %}Events - Professional Redundancy Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="fas fa-list-alt me-2 text-primary"></i>
                Event Log & History
            </h1>
            <div>
                <button id="refreshEvents" class="btn btn-primary me-2">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
                <button id="clearEvents" class="btn btn-outline-warning">
                    <i class="fas fa-trash me-1"></i>Clear Log
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Event Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-list fa-2x text-primary mb-2"></i>
                <div class="metric-value text-primary" id="totalEvents">-</div>
                <div class="metric-label">Total Events</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-exclamation-circle fa-2x text-danger mb-2"></i>
                <div class="metric-value text-danger" id="criticalEvents">-</div>
                <div class="metric-label">Critical Events</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                <div class="metric-value text-warning" id="warningEvents">-</div>
                <div class="metric-label">Warning Events</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body metric-card">
                <i class="fas fa-info-circle fa-2x text-info mb-2"></i>
                <div class="metric-value text-info" id="infoEvents">-</div>
                <div class="metric-label">Info Events</div>
            </div>
        </div>
    </div>
</div>

<!-- Event Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    Event Filters
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="filterApplication" class="form-label">Application</label>
                            <select class="form-select" id="filterApplication">
                                <option value="">All Applications</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="filterSeverity" class="form-label">Severity</label>
                            <select class="form-select" id="filterSeverity">
                                <option value="">All Severities</option>
                                <option value="CRITICAL">Critical</option>
                                <option value="WARNING">Warning</option>
                                <option value="INFO">Info</option>
                                <option value="DEBUG">Debug</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="filterEventType" class="form-label">Event Type</label>
                            <select class="form-select" id="filterEventType">
                                <option value="">All Types</option>
                                <option value="FAILOVER_INITIATED">Failover Initiated</option>
                                <option value="FAILOVER_COMPLETED">Failover Completed</option>
                                <option value="RECOVERY_INITIATED">Recovery Initiated</option>
                                <option value="RECOVERY_COMPLETED">Recovery Completed</option>
                                <option value="MONITORING_STARTED">Monitoring Started</option>
                                <option value="MONITORING_STOPPED">Monitoring Stopped</option>
                                <option value="APPLICATION_ADDED">Application Added</option>
                                <option value="MONITOR_ADDED">Monitor Added</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="filterTimeRange" class="form-label">Time Range</label>
                            <select class="form-select" id="filterTimeRange">
                                <option value="">All Time</option>
                                <option value="1h">Last Hour</option>
                                <option value="24h">Last 24 Hours</option>
                                <option value="7d">Last 7 Days</option>
                                <option value="30d">Last 30 Days</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="searchEvents" class="form-label">Search Events</label>
                            <input type="text" class="form-control" id="searchEvents" placeholder="Search event descriptions...">
                        </div>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <div class="mb-3">
                            <button id="applyFilters" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>Apply Filters
                            </button>
                            <button id="resetFilters" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-1"></i>Reset
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Event Timeline -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Event Timeline
                </h5>
            </div>
            <div class="card-body">
                <div id="eventTimeline" style="max-height: 600px; overflow-y: auto;">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">Loading events...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allEvents = [];
let filteredEvents = [];

$(document).ready(function() {
    loadEvents();
    loadApplications();
    
    $('#refreshEvents').click(loadEvents);
    $('#clearEvents').click(clearEvents);
    $('#applyFilters').click(applyFilters);
    $('#resetFilters').click(resetFilters);
    
    // Auto-refresh events every 30 seconds
    setInterval(loadEvents, 30000);
});

function loadEvents() {
    $.get('/api/status')
        .done(function(response) {
            if (response.success) {
                // In a real implementation, you'd have a dedicated events API
                // For now, we'll use the recent events from the status
                allEvents = response.data.recent_events || [];
                filteredEvents = [...allEvents];
                displayEvents(filteredEvents);
                updateEventMetrics(allEvents);
            } else {
                showAlert('danger', 'Failed to load events: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to connect to server');
        });
}

function displayEvents(events) {
    let html = '';
    
    if (events.length === 0) {
        html = '<div class="text-center py-5"><p class="text-muted">No events found.</p></div>';
    } else {
        events.forEach(event => {
            const severityClass = event.severity.toLowerCase();
            const eventTypeFormatted = event.event_type.replace(/_/g, ' ');
            const timestamp = new Date(event.timestamp).toLocaleString();
            
            html += `
                <div class="event-item event-${severityClass} mb-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-${getSeverityBadgeClass(event.severity)} me-2">${event.severity}</span>
                                <strong>${eventTypeFormatted}</strong>
                                <span class="text-muted ms-2">• ${event.application}</span>
                            </div>
                            <p class="mb-1">${event.description}</p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>${timestamp}
                            </small>
                        </div>
                        <div class="ms-3">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewEventDetails('${event.timestamp}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    $('#eventTimeline').html(html);
}

function updateEventMetrics(events) {
    const total = events.length;
    const critical = events.filter(e => e.severity === 'CRITICAL').length;
    const warning = events.filter(e => e.severity === 'WARNING').length;
    const info = events.filter(e => e.severity === 'INFO').length;
    
    $('#totalEvents').text(total);
    $('#criticalEvents').text(critical);
    $('#warningEvents').text(warning);
    $('#infoEvents').text(info);
}

function loadApplications() {
    $.get('/api/applications')
        .done(function(response) {
            if (response.success) {
                const select = $('#filterApplication');
                select.empty().append('<option value="">All Applications</option>');
                
                Object.keys(response.data).forEach(appName => {
                    select.append(`<option value="${appName}">${appName}</option>`);
                });
            }
        });
}

function applyFilters() {
    const applicationFilter = $('#filterApplication').val();
    const severityFilter = $('#filterSeverity').val();
    const eventTypeFilter = $('#filterEventType').val();
    const timeRangeFilter = $('#filterTimeRange').val();
    const searchFilter = $('#searchEvents').val().toLowerCase();
    
    filteredEvents = allEvents.filter(event => {
        // Application filter
        if (applicationFilter && event.application !== applicationFilter) {
            return false;
        }
        
        // Severity filter
        if (severityFilter && event.severity !== severityFilter) {
            return false;
        }
        
        // Event type filter
        if (eventTypeFilter && event.event_type !== eventTypeFilter) {
            return false;
        }
        
        // Time range filter
        if (timeRangeFilter) {
            const eventTime = new Date(event.timestamp);
            const now = new Date();
            const cutoff = new Date();
            
            switch(timeRangeFilter) {
                case '1h':
                    cutoff.setHours(now.getHours() - 1);
                    break;
                case '24h':
                    cutoff.setDate(now.getDate() - 1);
                    break;
                case '7d':
                    cutoff.setDate(now.getDate() - 7);
                    break;
                case '30d':
                    cutoff.setDate(now.getDate() - 30);
                    break;
            }
            
            if (eventTime < cutoff) {
                return false;
            }
        }
        
        // Search filter
        if (searchFilter && !event.description.toLowerCase().includes(searchFilter)) {
            return false;
        }
        
        return true;
    });
    
    displayEvents(filteredEvents);
    showAlert('info', `Showing ${filteredEvents.length} of ${allEvents.length} events`);
}

function resetFilters() {
    $('#filterApplication').val('');
    $('#filterSeverity').val('');
    $('#filterEventType').val('');
    $('#filterTimeRange').val('');
    $('#searchEvents').val('');
    
    filteredEvents = [...allEvents];
    displayEvents(filteredEvents);
    showAlert('info', 'Filters reset');
}

function clearEvents() {
    if (!confirm('Are you sure you want to clear all events? This action cannot be undone.')) {
        return;
    }
    
    // In a real implementation, you'd call an API to clear events
    showAlert('warning', 'Clear events functionality not implemented yet');
}

function viewEventDetails(timestamp) {
    const event = allEvents.find(e => e.timestamp === timestamp);
    if (event) {
        alert(`Event Details:\n\nType: ${event.event_type}\nApplication: ${event.application}\nSeverity: ${event.severity}\nTime: ${new Date(event.timestamp).toLocaleString()}\nDescription: ${event.description}`);
    }
}

function getSeverityBadgeClass(severity) {
    switch(severity) {
        case 'CRITICAL': return 'danger';
        case 'WARNING': return 'warning';
        case 'INFO': return 'info';
        case 'DEBUG': return 'secondary';
        default: return 'secondary';
    }
}
</script>
{% endblock %}
