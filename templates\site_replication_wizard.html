<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site Replication Wizard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --success-color: #22c55e;
            --info-color: #06b6d4;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            margin-bottom: 2rem;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: var(--text-primary);
            font-size: 2.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header h1 i {
            color: var(--secondary-color);
            font-size: 2.25rem;
        }

        .header .subtitle {
            color: var(--text-secondary);
            font-size: 1.125rem;
            margin-top: 0.5rem;
            font-weight: 400;
        }

        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.875rem 1.75rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-md);
        }

        .nav-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .wizard-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--radius-xl);
            padding: 3rem;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            margin-bottom: 2rem;
        }

        .wizard-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3rem;
            position: relative;
        }

        .wizard-steps::before {
            content: '';
            position: absolute;
            top: 1.5rem;
            left: 2rem;
            right: 2rem;
            height: 2px;
            background: var(--border-color);
            z-index: 1;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .step-circle {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
        }

        .step.active .step-circle {
            background: var(--primary-color);
            color: white;
        }

        .step.completed .step-circle {
            background: var(--success-color);
            color: white;
        }

        .step-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
            text-align: center;
        }

        .step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
            animation: fadeInUp 0.5s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.875rem 1.75rem;
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            text-align: center;
            box-shadow: var(--shadow-md);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, var(--primary-dark), #1e40af);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary-color), #059669);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, #059669, #047857);
        }

        .btn-outline {
            background: rgba(255, 255, 255, 0.9);
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-3px);
        }

        .wizard-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }

        .scan-results {
            background: var(--light-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            margin-top: 1rem;
            border: 1px solid var(--border-color);
        }

        .scan-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .scan-item:last-child {
            border-bottom: none;
        }

        .scan-item-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .scan-item-status {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .scan-item-status.found {
            background: var(--success-color);
            color: white;
        }

        .scan-item-status.not-found {
            background: var(--danger-color);
            color: white;
        }

        .progress-bar {
            width: 100%;
            height: 0.5rem;
            background: var(--border-color);
            border-radius: 9999px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 9999px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .alert {
            padding: 1rem 1.5rem;
            border-radius: var(--radius-lg);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .alert-success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            color: #15803d;
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.2);
            color: #d97706;
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div>
                <h1><i class="fas fa-copy"></i>Site Replication Wizard</h1>
                <div class="subtitle">Replicate your existing EBO installation to create redundancy at a new site</div>
            </div>
            <a href="/" class="nav-btn"><i class="fas fa-home"></i>Dashboard</a>
        </div>
    </div>

    <div class="container">
        <div class="wizard-container">
            <!-- Wizard Steps -->
            <div class="wizard-steps">
                <div class="step active" id="step-1">
                    <div class="step-circle">1</div>
                    <div class="step-label">Scan Source Site</div>
                </div>
                <div class="step" id="step-2">
                    <div class="step-circle">2</div>
                    <div class="step-label">Configure Target</div>
                </div>
                <div class="step" id="step-3">
                    <div class="step-circle">3</div>
                    <div class="step-label">Review Plan</div>
                </div>
                <div class="step" id="step-4">
                    <div class="step-circle">4</div>
                    <div class="step-label">Execute</div>
                </div>
            </div>

            <!-- Step 1: Scan Source Site -->
            <div class="step-content active" id="content-1">
                <h2>Step 1: Scan Source Site (Site A)</h2>
                <p>First, we need to discover what's installed on your existing site to replicate it.</p>

                <div class="form-group">
                    <label class="form-label">Source Site Server IP/Hostname</label>
                    <input type="text" class="form-input" id="source-server" placeholder="************ or SERVER-NAME" value="************">
                </div>

                <button class="btn btn-secondary" onclick="scanSourceSite()">
                    <i class="fas fa-search"></i>Scan Source Site
                </button>

                <div id="scan-results" style="display: none;">
                    <div class="scan-results">
                        <h3>Scan Results</h3>
                        <div id="scan-items"></div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Configure Target Site -->
            <div class="step-content" id="content-2">
                <h2>Step 2: Configure Target Site (Site B)</h2>
                <p>Configure the target site where you want to replicate the EBO installation.</p>

                <div class="form-group">
                    <label class="form-label">Target Site Name</label>
                    <input type="text" class="form-input" id="target-name" placeholder="Site B - Backup Location" value="Site B - Backup Location">
                </div>

                <div class="form-group">
                    <label class="form-label">Target Server IP Address</label>
                    <input type="text" class="form-input" id="target-ip" placeholder="************" value="************">
                </div>

                <div class="form-group">
                    <label class="form-label">Target Server Hostname</label>
                    <input type="text" class="form-input" id="target-hostname" placeholder="SITE-B-SERVER" value="SITE-B-SERVER">
                </div>

                <div class="form-group">
                    <label class="form-label">Network Path to Target</label>
                    <input type="text" class="form-input" id="target-path" placeholder="\\************\c$" value="\\************\c$">
                </div>

                <button class="btn btn-secondary" onclick="validateTarget()">
                    <i class="fas fa-check"></i>Validate Target
                </button>

                <div id="validation-results" style="display: none;">
                    <div class="scan-results">
                        <h3>Validation Results</h3>
                        <div id="validation-items"></div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Review Replication Plan -->
            <div class="step-content" id="content-3">
                <h2>Step 3: Review Replication Plan</h2>
                <p>Review the complete replication plan before execution.</p>

                <div class="scan-results">
                    <h3>Replication Summary</h3>
                    <div class="scan-item">
                        <div class="scan-item-name">Source Site</div>
                        <div id="plan-source">************</div>
                    </div>
                    <div class="scan-item">
                        <div class="scan-item-name">Target Site</div>
                        <div id="plan-target">Site B - Backup Location (************)</div>
                    </div>
                    <div class="scan-item">
                        <div class="scan-item-name">Estimated Time</div>
                        <div>2 hours 15 minutes</div>
                    </div>
                    <div class="scan-item">
                        <div class="scan-item-name">Data to Transfer</div>
                        <div>3.7 GB</div>
                    </div>
                </div>

                <div class="scan-results" style="margin-top: 1.5rem;">
                    <h3>Replication Steps</h3>
                    <div class="scan-item">
                        <div class="scan-item-name">1. Install EBO on Target</div>
                        <div>45 minutes</div>
                    </div>
                    <div class="scan-item">
                        <div class="scan-item-name">2. Install SQL Server</div>
                        <div>30 minutes</div>
                    </div>
                    <div class="scan-item">
                        <div class="scan-item-name">3. Copy Database</div>
                        <div>20 minutes</div>
                    </div>
                    <div class="scan-item">
                        <div class="scan-item-name">4. Copy Configuration Files</div>
                        <div>10 minutes</div>
                    </div>
                    <div class="scan-item">
                        <div class="scan-item-name">5. Copy Data Directories</div>
                        <div>15 minutes</div>
                    </div>
                    <div class="scan-item">
                        <div class="scan-item-name">6. Configure Services</div>
                        <div>15 minutes</div>
                    </div>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Make sure the target server is accessible and has sufficient disk space (minimum 10 GB free).
                </div>
            </div>

            <!-- Step 4: Execute Replication -->
            <div class="step-content" id="content-4">
                <h2>Step 4: Execute Replication</h2>
                <p>The replication process is now running. Please wait for completion.</p>

                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>

                <div id="execution-status">
                    <div class="alert alert-warning">
                        <i class="fas fa-clock"></i>
                        Replication not started. Click "Start Replication" to begin.
                    </div>
                </div>

                <button class="btn btn-secondary" onclick="startReplication()" id="start-replication-btn">
                    <i class="fas fa-play"></i>Start Replication
                </button>

                <div id="replication-log" style="display: none;">
                    <div class="scan-results">
                        <h3>Replication Log</h3>
                        <div id="log-items"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="wizard-actions">
                <button class="btn btn-outline" onclick="previousStep()" id="prev-btn" style="display: none;">
                    <i class="fas fa-arrow-left"></i>Previous
                </button>
                <button class="btn btn-primary" onclick="nextStep()" id="next-btn" disabled>
                    Next<i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let scanData = null;

        function nextStep() {
            if (currentStep < 4) {
                // Hide current step
                document.getElementById(`step-${currentStep}`).classList.remove('active');
                document.getElementById(`content-${currentStep}`).classList.remove('active');

                // Mark current step as completed
                document.getElementById(`step-${currentStep}`).classList.add('completed');

                currentStep++;

                // Show next step
                document.getElementById(`step-${currentStep}`).classList.add('active');
                document.getElementById(`content-${currentStep}`).classList.add('active');

                updateNavigation();
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                // Hide current step
                document.getElementById(`step-${currentStep}`).classList.remove('active');
                document.getElementById(`content-${currentStep}`).classList.remove('active');

                currentStep--;

                // Show previous step
                document.getElementById(`step-${currentStep}`).classList.remove('completed');
                document.getElementById(`step-${currentStep}`).classList.add('active');
                document.getElementById(`content-${currentStep}`).classList.add('active');

                updateNavigation();
            }
        }

        function updateNavigation() {
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');

            prevBtn.style.display = currentStep > 1 ? 'inline-flex' : 'none';

            if (currentStep === 4) {
                nextBtn.textContent = 'Complete';
                nextBtn.innerHTML = '<i class="fas fa-check"></i>Complete';
            } else {
                nextBtn.innerHTML = 'Next<i class="fas fa-arrow-right"></i>';
            }
        }

        function scanSourceSite() {
            const sourceServer = document.getElementById('source-server').value;
            const scanResults = document.getElementById('scan-results');
            const scanItems = document.getElementById('scan-items');
            const nextBtn = document.getElementById('next-btn');

            // Show loading
            scanItems.innerHTML = '<div class="alert alert-warning"><i class="fas fa-spinner fa-spin"></i>Scanning source site...</div>';
            scanResults.style.display = 'block';

            // Simulate scan (in real implementation, this would call the API)
            setTimeout(() => {
                const mockScanResults = [
                    { name: 'EcoStruxure Building Operation', status: 'found', details: 'Version 3.2, Installation Path: C:\\Program Files\\Schneider Electric\\EcoStruxure Building Operation' },
                    { name: 'SQL Server Database', status: 'found', details: 'SQL Server 2019, Database: EBO_Database (2.5GB)' },
                    { name: 'EBO Configuration Files', status: 'found', details: '47 configuration files found' },
                    { name: 'EBO Data Directories', status: 'found', details: '3 data directories (1.2GB total)' },
                    { name: 'EBO Services', status: 'found', details: '8 Windows services configured' },
                    { name: 'License Server', status: 'found', details: 'Schneider Electric License Manager' }
                ];

                let html = '';
                mockScanResults.forEach(item => {
                    html += `
                        <div class="scan-item">
                            <div>
                                <div class="scan-item-name">${item.name}</div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary); margin-top: 0.25rem;">${item.details}</div>
                            </div>
                            <div class="scan-item-status ${item.status}">${item.status === 'found' ? 'Found' : 'Not Found'}</div>
                        </div>
                    `;
                });

                html += '<div class="alert alert-success"><i class="fas fa-check-circle"></i>Source site scan completed successfully! Ready to proceed to target configuration.</div>';

                scanItems.innerHTML = html;
                nextBtn.disabled = false;

                scanData = mockScanResults;
            }, 2000);
        }

        function validateTarget() {
            const validationResults = document.getElementById('validation-results');
            const validationItems = document.getElementById('validation-items');
            const nextBtn = document.getElementById('next-btn');

            // Show loading
            validationItems.innerHTML = '<div class="alert alert-warning"><i class="fas fa-spinner fa-spin"></i>Validating target site...</div>';
            validationResults.style.display = 'block';

            // Simulate validation
            setTimeout(() => {
                const mockValidationResults = [
                    { name: 'Network Connectivity', status: 'found', details: 'Target server is reachable' },
                    { name: 'Disk Space', status: 'found', details: '50 GB available (10 GB required)' },
                    { name: 'Administrator Access', status: 'found', details: 'Administrative privileges confirmed' },
                    { name: 'Windows Version', status: 'found', details: 'Windows Server 2019 (Compatible)' },
                    { name: 'Prerequisites', status: 'found', details: '.NET Framework 4.8 installed' }
                ];

                let html = '';
                mockValidationResults.forEach(item => {
                    html += `
                        <div class="scan-item">
                            <div>
                                <div class="scan-item-name">${item.name}</div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary); margin-top: 0.25rem;">${item.details}</div>
                            </div>
                            <div class="scan-item-status ${item.status}">${item.status === 'found' ? 'Valid' : 'Invalid'}</div>
                        </div>
                    `;
                });

                html += '<div class="alert alert-success"><i class="fas fa-check-circle"></i>Target site validation completed successfully! Ready to proceed to plan review.</div>';

                validationItems.innerHTML = html;
                nextBtn.disabled = false;

                // Update plan summary
                document.getElementById('plan-source').textContent = document.getElementById('source-server').value;
                document.getElementById('plan-target').textContent = `${document.getElementById('target-name').value} (${document.getElementById('target-ip').value})`;
            }, 1500);
        }

        function startReplication() {
            const progressFill = document.getElementById('progress-fill');
            const executionStatus = document.getElementById('execution-status');
            const replicationLog = document.getElementById('replication-log');
            const logItems = document.getElementById('log-items');
            const startBtn = document.getElementById('start-replication-btn');
            const nextBtn = document.getElementById('next-btn');

            startBtn.style.display = 'none';
            replicationLog.style.display = 'block';
            nextBtn.disabled = true;

            const steps = [
                { name: 'Installing EBO on Target', duration: 3000 },
                { name: 'Installing SQL Server', duration: 2500 },
                { name: 'Copying Database', duration: 2000 },
                { name: 'Copying Configuration Files', duration: 1500 },
                { name: 'Copying Data Directories', duration: 2000 },
                { name: 'Configuring Services', duration: 1500 }
            ];

            let currentStepIndex = 0;
            let totalProgress = 0;

            function executeStep() {
                if (currentStepIndex < steps.length) {
                    const step = steps[currentStepIndex];

                    // Add log entry
                    const logEntry = document.createElement('div');
                    logEntry.className = 'scan-item';
                    logEntry.innerHTML = `
                        <div class="scan-item-name">${step.name}</div>
                        <div class="scan-item-status found">In Progress</div>
                    `;
                    logItems.appendChild(logEntry);

                    // Update status
                    executionStatus.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-spinner fa-spin"></i>
                            ${step.name}...
                        </div>
                    `;

                    setTimeout(() => {
                        // Mark step as completed
                        logEntry.querySelector('.scan-item-status').textContent = 'Completed';
                        logEntry.querySelector('.scan-item-status').className = 'scan-item-status found';

                        currentStepIndex++;
                        totalProgress = (currentStepIndex / steps.length) * 100;
                        progressFill.style.width = totalProgress + '%';

                        if (currentStepIndex < steps.length) {
                            executeStep();
                        } else {
                            // Replication completed
                            executionStatus.innerHTML = `
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    Site replication completed successfully! Site B is now ready and synchronized with Site A.
                                </div>
                            `;

                            nextBtn.disabled = false;
                            nextBtn.innerHTML = '<i class="fas fa-check"></i>Complete';
                            nextBtn.onclick = () => {
                                window.location.href = '/';
                            };
                        }
                    }, step.duration);
                }
            }

            executeStep();
        }
    </script>
</body>
</html>
