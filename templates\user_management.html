<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - EBO Multi-Site Redundancy</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            zoom: 0.9;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            box-shadow: var(--shadow-lg);
            margin-bottom: 2rem;
        }

        .header-content {
            max-width: 100%;
            margin: 0;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: var(--text-primary);
            font-size: 1.75rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-left: -0.5rem;
        }

        .header h1 i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            margin-top: 0.5rem;
            font-weight: 400;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            margin-right: -0.5rem;
        }

        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.875rem 1.75rem;
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            text-align: center;
            box-shadow: var(--shadow-md);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background: #059669;
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .container {
            max-width: 100%;
            margin: 0;
            padding: 0 2rem;
        }

        .management-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .management-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .management-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card-title i {
            color: var(--primary-color);
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .users-table th,
        .users-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .users-table th {
            background: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-primary);
        }

        .role-badge {
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .role-admin {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .role-operator {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .role-viewer {
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .status-badge {
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .status-active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-inactive {
            background: rgba(107, 114, 128, 0.1);
            color: var(--text-muted);
            border: 1px solid rgba(107, 114, 128, 0.2);
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: var(--text-muted);
            font-size: 1.125rem;
        }

        .loading i {
            margin-right: 0.5rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .user-info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: var(--radius-lg);
            padding: 1rem;
            margin-bottom: 2rem;
            color: var(--primary-color);
        }

        .user-info strong {
            color: var(--text-primary);
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .tab {
            padding: 1rem 2rem;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }

        .tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .audit-table {
            width: 100%;
            border-collapse: collapse;
        }

        .audit-table th,
        .audit-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.875rem;
        }

        .audit-table th {
            background: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-primary);
        }

        .action-badge {
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .action-login {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .action-logout {
            background: rgba(107, 114, 128, 0.1);
            color: var(--text-muted);
        }

        .action-create {
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary-color);
        }

        .action-failed {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div>
                <h1><i class="fas fa-users-cog"></i>User Management</h1>
                <div class="subtitle">Manage users, roles, and access permissions</div>
            </div>
            <div class="header-actions">
                <button class="nav-btn btn-primary" onclick="showCreateUserModal()">
                    <i class="fas fa-user-plus"></i>Add User
                </button>
                <a href="/" class="nav-btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>Dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="user-info fade-in">
            <strong>Current User:</strong> <span id="currentUser">Loading...</span> |
            <strong>Role:</strong> <span id="currentRole">Loading...</span> |
            <strong>Organization:</strong> <span id="currentOrg">Loading...</span>
        </div>

        <div class="management-grid fade-in">
            <div class="management-card">
                <div class="tabs">
                    <button class="tab active" onclick="showTab('users')">
                        <i class="fas fa-users"></i> Users
                    </button>
                    <button class="tab" onclick="showTab('audit')">
                        <i class="fas fa-clipboard-list"></i> Audit Logs
                    </button>
                </div>

                <div id="users-tab" class="tab-content active">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-users"></i>
                            System Users
                        </div>
                    </div>
                    
                    <div id="users-content">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            Loading users...
                        </div>
                    </div>
                </div>

                <div id="audit-tab" class="tab-content">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-clipboard-list"></i>
                            Audit Trail
                        </div>
                    </div>
                    
                    <div id="audit-content">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            Loading audit logs...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentUser = null;

        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Show selected tab
            document.querySelector(`[onclick="showTab('${tabName}')"]`).classList.add('active');
            document.getElementById(`${tabName}-tab`).classList.add('active');
            
            if (tabName === 'audit') {
                loadAuditLogs();
            }
        }

        function loadCurrentUser() {
            fetch('/api/auth/current-user')
                .then(response => response.json())
                .then(data => {
                    if (data.user) {
                        currentUser = data.user;
                        document.getElementById('currentUser').textContent = data.user.full_name;
                        document.getElementById('currentRole').textContent = data.user.role.toUpperCase();
                        document.getElementById('currentOrg').textContent = data.user.organization;
                    }
                })
                .catch(error => console.error('Error loading current user:', error));
        }

        function loadUsers() {
            fetch('/api/users')
                .then(response => response.json())
                .then(data => {
                    if (data.users) {
                        renderUsersTable(data.users);
                    }
                })
                .catch(error => {
                    document.getElementById('users-content').innerHTML = `
                        <div class="loading">
                            <i class="fas fa-exclamation-triangle"></i>
                            Error loading users
                        </div>
                    `;
                });
        }

        function renderUsersTable(users) {
            const html = `
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Organization</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map(user => `
                            <tr>
                                <td>
                                    <strong>${user.full_name}</strong><br>
                                    <small style="color: var(--text-secondary);">${user.username}</small>
                                </td>
                                <td>${user.email}</td>
                                <td><span class="role-badge role-${user.role}">${user.role.toUpperCase()}</span></td>
                                <td>${user.organization}</td>
                                <td><span class="status-badge status-${user.is_active ? 'active' : 'inactive'}">${user.is_active ? 'Active' : 'Inactive'}</span></td>
                                <td>${user.last_login ? new Date(user.last_login).toLocaleString() : 'Never'}</td>
                                <td>
                                    ${user.username !== 'admin' ? `
                                        <button class="nav-btn btn-danger btn-sm" onclick="deleteUser(${user.id}, '${user.username}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    ` : '<span style="color: var(--text-muted);">Protected</span>'}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            document.getElementById('users-content').innerHTML = html;
        }

        function loadAuditLogs() {
            fetch('/api/audit-logs')
                .then(response => response.json())
                .then(data => {
                    if (data.logs) {
                        renderAuditTable(data.logs);
                    }
                })
                .catch(error => {
                    document.getElementById('audit-content').innerHTML = `
                        <div class="loading">
                            <i class="fas fa-exclamation-triangle"></i>
                            Error loading audit logs
                        </div>
                    `;
                });
        }

        function renderAuditTable(logs) {
            const html = `
                <table class="audit-table">
                    <thead>
                        <tr>
                            <th>Timestamp</th>
                            <th>User</th>
                            <th>Action</th>
                            <th>Resource</th>
                            <th>Details</th>
                            <th>IP Address</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${logs.map(log => `
                            <tr>
                                <td>${new Date(log.timestamp).toLocaleString()}</td>
                                <td>${log.username}</td>
                                <td><span class="action-badge action-${log.action.toLowerCase().includes('login') ? (log.action.includes('FAILED') ? 'failed' : 'login') : log.action.toLowerCase().includes('logout') ? 'logout' : 'create'}">${log.action}</span></td>
                                <td>${log.resource}</td>
                                <td>${log.details || '-'}</td>
                                <td>${log.ip_address || '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            document.getElementById('audit-content').innerHTML = html;
        }

        function showCreateUserModal() {
            // This would open a modal for creating users
            alert('Create User functionality would be implemented here');
        }

        function deleteUser(userId, username) {
            if (confirm(`Are you sure you want to delete user "${username}"?`)) {
                fetch(`/api/users/${userId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadUsers(); // Reload users table
                    } else {
                        alert('Error deleting user: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error deleting user: ' + error);
                });
            }
        }

        // Load data on page load
        loadCurrentUser();
        loadUsers();
    </script>
</body>
</html>
