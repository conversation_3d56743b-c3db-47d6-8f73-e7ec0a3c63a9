"""
Test script for authentication system
"""

import requests
import json

# Test the authentication system
def test_authentication():
    base_url = "http://localhost:5002"
    
    print("🔐 Testing EBO Multi-Site Authentication System")
    print("=" * 60)
    
    # Test 1: Try to access protected endpoint without authentication
    print("\n1. Testing protected endpoint without authentication...")
    try:
        response = requests.get(f"{base_url}/api/multisite/status")
        if response.status_code == 401:
            print("✅ Protected endpoint correctly requires authentication")
        else:
            print(f"❌ Expected 401, got {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Test login with default admin credentials
    print("\n2. Testing login with default admin credentials...")
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        session = requests.Session()
        response = session.post(f"{base_url}/api/auth/login", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"   User: {data['user']['full_name']}")
            print(f"   Role: {data['user']['role']}")
            print(f"   Organization: {data['user']['organization']}")
            print(f"   Permissions: {len(data['user']['permissions'])} permissions")
            
            # Test 3: Access protected endpoint with authentication
            print("\n3. Testing protected endpoint with authentication...")
            response = session.get(f"{base_url}/api/multisite/status")
            if response.status_code == 200:
                print("✅ Protected endpoint accessible after authentication")
            else:
                print(f"❌ Expected 200, got {response.status_code}")
            
            # Test 4: Test current user endpoint
            print("\n4. Testing current user endpoint...")
            response = session.get(f"{base_url}/api/auth/current-user")
            if response.status_code == 200:
                user_data = response.json()
                print("✅ Current user endpoint working")
                print(f"   Current user: {user_data['user']['username']}")
            else:
                print(f"❌ Expected 200, got {response.status_code}")
            
            # Test 5: Test admin-only endpoint (users)
            print("\n5. Testing admin-only endpoint...")
            response = session.get(f"{base_url}/api/users")
            if response.status_code == 200:
                users_data = response.json()
                print("✅ Admin endpoint accessible")
                print(f"   Found {len(users_data['users'])} users")
            else:
                print(f"❌ Expected 200, got {response.status_code}")
            
            # Test 6: Test logout
            print("\n6. Testing logout...")
            response = session.post(f"{base_url}/api/auth/logout")
            if response.status_code == 200:
                print("✅ Logout successful")
                
                # Test 7: Verify logout worked
                print("\n7. Verifying logout worked...")
                response = session.get(f"{base_url}/api/multisite/status")
                if response.status_code == 401:
                    print("✅ Protected endpoint correctly requires authentication after logout")
                else:
                    print(f"❌ Expected 401, got {response.status_code}")
            else:
                print(f"❌ Logout failed: {response.status_code}")
                
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error during login test: {e}")
    
    # Test 8: Test invalid credentials
    print("\n8. Testing invalid credentials...")
    try:
        invalid_login = {
            "username": "admin",
            "password": "wrongpassword"
        }
        
        response = requests.post(f"{base_url}/api/auth/login", json=invalid_login)
        if response.status_code == 401:
            print("✅ Invalid credentials correctly rejected")
        else:
            print(f"❌ Expected 401, got {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Authentication system test completed!")
    print("\nDefault Admin Credentials:")
    print("Username: admin")
    print("Password: admin123")
    print("\nYou can now login at: http://localhost:5002/login")

if __name__ == "__main__":
    test_authentication()
