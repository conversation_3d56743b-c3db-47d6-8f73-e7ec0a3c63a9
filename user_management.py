"""
Enterprise User Management System
Provides role-based access control, authentication, and audit trails
"""

import hashlib
import secrets
import sqlite3
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional
import logging

class UserRole(Enum):
    """User roles with different permission levels"""
    ADMIN = "admin"
    OPERATOR = "operator"
    VIEWER = "viewer"

class Permission(Enum):
    """System permissions"""
    VIEW_DASHBOARD = "view_dashboard"
    VIEW_SITES = "view_sites"
    EDIT_SITES = "edit_sites"
    DELETE_SITES = "delete_sites"
    MANAGE_REPLICATION = "manage_replication"
    VIEW_NETWORK = "view_network"
    MANAGE_USERS = "manage_users"
    VIEW_AUDIT_LOGS = "view_audit_logs"
    SYSTEM_ADMIN = "system_admin"

class UserManager:
    """Manages users, authentication, and permissions"""

    def __init__(self, db_path: str = "ebo_users.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.init_database()
        self.create_default_admin()

        # Role permissions mapping
        self.role_permissions = {
            UserRole.ADMIN: [
                Permission.VIEW_DASHBOARD,
                Permission.VIEW_SITES,
                Permission.EDIT_SITES,
                Permission.DELETE_SITES,
                Permission.MANAGE_REPLICATION,
                Permission.VIEW_NETWORK,
                Permission.MANAGE_USERS,
                Permission.VIEW_AUDIT_LOGS,
                Permission.SYSTEM_ADMIN
            ],
            UserRole.OPERATOR: [
                Permission.VIEW_DASHBOARD,
                Permission.VIEW_SITES,
                Permission.EDIT_SITES,
                Permission.MANAGE_REPLICATION,
                Permission.VIEW_NETWORK
            ],
            UserRole.VIEWER: [
                Permission.VIEW_DASHBOARD,
                Permission.VIEW_SITES,
                Permission.VIEW_NETWORK
            ]
        }

    def init_database(self):
        """Initialize the user database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Users table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    salt TEXT NOT NULL,
                    role TEXT NOT NULL,
                    organization TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    failed_login_attempts INTEGER DEFAULT 0,
                    locked_until TIMESTAMP
                )
            ''')

            # Sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_token TEXT UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # Audit logs table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    username TEXT,
                    action TEXT NOT NULL,
                    resource TEXT,
                    details TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    organization TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # Organizations table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS organizations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')

            conn.commit()

    def create_default_admin(self):
        """Create default admin user if none exists"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Check if any admin users exist
            cursor.execute("SELECT COUNT(*) FROM users WHERE role = ?", (UserRole.ADMIN.value,))
            admin_count = cursor.fetchone()[0]

            if admin_count == 0:
                # Create default organization
                cursor.execute(
                    "INSERT OR IGNORE INTO organizations (name, description) VALUES (?, ?)",
                    ("Default Organization", "Default organization for EBO system")
                )

                # Create default admin user
                salt = secrets.token_hex(32)
                password_hash = self._hash_password("admin123", salt)

                cursor.execute('''
                    INSERT INTO users (username, email, password_hash, salt, role, organization, full_name)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    "admin",
                    "<EMAIL>",
                    password_hash,
                    salt,
                    UserRole.ADMIN.value,
                    "Default Organization",
                    "System Administrator"
                ))

                conn.commit()
                self.logger.info("Created default admin user: admin/admin123")

    def _hash_password(self, password: str, salt: str) -> str:
        """Hash password with salt"""
        return hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()

    def authenticate_user(self, username: str, password: str, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> Optional[Dict]:
        """Authenticate user and return user info if successful"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Get user info
            cursor.execute('''
                SELECT id, username, email, password_hash, salt, role, organization, full_name,
                       is_active, failed_login_attempts, locked_until
                FROM users WHERE username = ?
            ''', (username,))

            user_data = cursor.fetchone()
            if not user_data:
                self.log_audit_event(None, username, "LOGIN_FAILED", "authentication",
                                   "User not found", ip_address, user_agent)
                return None

            user_id, username, email, stored_hash, salt, role, org, full_name, is_active, failed_attempts, locked_until = user_data

            # Check if account is locked
            if locked_until and datetime.fromisoformat(locked_until) > datetime.now():
                self.log_audit_event(user_id, username, "LOGIN_FAILED", "authentication",
                                   "Account locked", ip_address, user_agent, org)
                return None

            # Check if account is active
            if not is_active:
                self.log_audit_event(user_id, username, "LOGIN_FAILED", "authentication",
                                   "Account disabled", ip_address, user_agent, org)
                return None

            # Verify password
            password_hash = self._hash_password(password, salt)
            if password_hash != stored_hash:
                # Increment failed attempts
                failed_attempts += 1
                locked_until = None

                # Lock account after 5 failed attempts
                if failed_attempts >= 5:
                    locked_until = (datetime.now() + timedelta(minutes=30)).isoformat()

                cursor.execute('''
                    UPDATE users SET failed_login_attempts = ?, locked_until = ?
                    WHERE id = ?
                ''', (failed_attempts, locked_until, user_id))
                conn.commit()

                self.log_audit_event(user_id, username, "LOGIN_FAILED", "authentication",
                                   f"Invalid password (attempt {failed_attempts})", ip_address, user_agent, org)
                return None

            # Reset failed attempts on successful login
            cursor.execute('''
                UPDATE users SET failed_login_attempts = 0, locked_until = NULL, last_login = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (user_id,))
            conn.commit()

            # Create session
            session_token = secrets.token_urlsafe(32)
            expires_at = (datetime.now() + timedelta(hours=8)).isoformat()

            cursor.execute('''
                INSERT INTO sessions (user_id, session_token, expires_at, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, session_token, expires_at, ip_address, user_agent))
            conn.commit()

            self.log_audit_event(user_id, username, "LOGIN_SUCCESS", "authentication",
                               "User logged in", ip_address, user_agent, org)

            return {
                'user_id': user_id,
                'username': username,
                'email': email,
                'role': role,
                'organization': org,
                'full_name': full_name,
                'session_token': session_token,
                'permissions': [p.value for p in self.role_permissions[UserRole(role)]]
            }

    def validate_session(self, session_token: str) -> Optional[Dict]:
        """Validate session token and return user info"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT s.user_id, u.username, u.email, u.role, u.organization, u.full_name, u.is_active
                FROM sessions s
                JOIN users u ON s.user_id = u.id
                WHERE s.session_token = ? AND s.is_active = 1 AND s.expires_at > CURRENT_TIMESTAMP
            ''', (session_token,))

            session_data = cursor.fetchone()
            if not session_data:
                return None

            user_id, username, email, role, org, full_name, is_active = session_data

            if not is_active:
                return None

            return {
                'user_id': user_id,
                'username': username,
                'email': email,
                'role': role,
                'organization': org,
                'full_name': full_name,
                'permissions': [p.value for p in self.role_permissions[UserRole(role)]]
            }

    def logout_user(self, session_token: str, ip_address: Optional[str] = None, user_agent: Optional[str] = None):
        """Logout user by invalidating session"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Get user info before logout
            cursor.execute('''
                SELECT s.user_id, u.username, u.organization
                FROM sessions s
                JOIN users u ON s.user_id = u.id
                WHERE s.session_token = ?
            ''', (session_token,))

            session_data = cursor.fetchone()
            if session_data:
                user_id, username, org = session_data

                # Invalidate session
                cursor.execute('''
                    UPDATE sessions SET is_active = 0 WHERE session_token = ?
                ''', (session_token,))
                conn.commit()

                self.log_audit_event(user_id, username, "LOGOUT", "authentication",
                                   "User logged out", ip_address, user_agent, org)

    def has_permission(self, user_role: str, permission: Permission) -> bool:
        """Check if user role has specific permission"""
        try:
            role = UserRole(user_role)
            return permission in self.role_permissions[role]
        except ValueError:
            return False

    def log_audit_event(self, user_id: Optional[int], username: str, action: str,
                       resource: str, details: Optional[str] = None, ip_address: Optional[str] = None,
                       user_agent: Optional[str] = None, organization: Optional[str] = None):
        """Log audit event"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO audit_logs (user_id, username, action, resource, details,
                                      ip_address, user_agent, organization)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, username, action, resource, details, ip_address, user_agent, organization))
            conn.commit()

    def create_user(self, username: str, email: str, password: str, role: UserRole,
                   organization: str, full_name: str, created_by_user_id: int) -> bool:
        """Create new user"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                salt = secrets.token_hex(32)
                password_hash = self._hash_password(password, salt)

                cursor.execute('''
                    INSERT INTO users (username, email, password_hash, salt, role, organization, full_name)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (username, email, password_hash, salt, role.value, organization, full_name))

                conn.commit()

                # Log audit event
                cursor.execute("SELECT username FROM users WHERE id = ?", (created_by_user_id,))
                creator_username = cursor.fetchone()[0]

                self.log_audit_event(created_by_user_id, creator_username, "USER_CREATED", "user_management",
                                   f"Created user: {username} with role: {role.value}", organization=organization)

                return True
        except sqlite3.IntegrityError:
            return False

    def get_users(self, organization: Optional[str] = None) -> List[Dict]:
        """Get all users, optionally filtered by organization"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            if organization:
                cursor.execute('''
                    SELECT id, username, email, role, organization, full_name, is_active,
                           created_at, last_login
                    FROM users WHERE organization = ?
                    ORDER BY created_at DESC
                ''', (organization,))
            else:
                cursor.execute('''
                    SELECT id, username, email, role, organization, full_name, is_active,
                           created_at, last_login
                    FROM users
                    ORDER BY created_at DESC
                ''')

            users = []
            for row in cursor.fetchall():
                users.append({
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'role': row[3],
                    'organization': row[4],
                    'full_name': row[5],
                    'is_active': bool(row[6]),
                    'created_at': row[7],
                    'last_login': row[8]
                })

            return users

    def get_audit_logs(self, limit: int = 100, organization: Optional[str] = None) -> List[Dict]:
        """Get audit logs"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            if organization:
                cursor.execute('''
                    SELECT username, action, resource, details, ip_address, timestamp, organization
                    FROM audit_logs WHERE organization = ?
                    ORDER BY timestamp DESC LIMIT ?
                ''', (organization, limit))
            else:
                cursor.execute('''
                    SELECT username, action, resource, details, ip_address, timestamp, organization
                    FROM audit_logs
                    ORDER BY timestamp DESC LIMIT ?
                ''', (limit,))

            logs = []
            for row in cursor.fetchall():
                logs.append({
                    'username': row[0],
                    'action': row[1],
                    'resource': row[2],
                    'details': row[3],
                    'ip_address': row[4],
                    'timestamp': row[5],
                    'organization': row[6]
                })

            return logs

# Global user manager instance
user_manager = UserManager()
