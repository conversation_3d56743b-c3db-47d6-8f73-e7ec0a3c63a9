#!/usr/bin/env python3
"""
Web UI for Hardware Redundancy Configuration System
Provides a web interface for configuring and monitoring hardware redundancy
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import yaml
import os
import sys
import json
import psutil
import requests
from datetime import datetime
import threading
import time

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.redundancy.config.config_loader import load_config
from src.redundancy.monitors import DiskMonitor, NetworkMonitor, HTTPMonitor
from src.redundancy.utils.logger import setup_logger

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# Global variables for monitoring
monitoring_thread = None
monitoring_active = False
monitor_instances = []
monitor_status = {}
config_file_path = "config.yaml"

# Setup logger for web UI
web_logger = setup_logger('WebUI', log_to_file=False)

class MonitoringService:
    """Service to handle background monitoring"""
    
    def __init__(self):
        self.active = False
        self.monitors = []
        self.status = {}
        
    def load_monitors(self):
        """Load monitors from configuration"""
        try:
            config = load_config(config_file_path)
            if not config or 'monitors' not in config:
                return False
                
            self.monitors = []
            for monitor_conf in config['monitors']:
                monitor_type = monitor_conf.get('type')
                monitor_name = monitor_conf.get('name', 'UnnamedMonitor')
                
                try:
                    if monitor_type == "disk":
                        config_dict = {
                            "name": monitor_name,
                            "type": monitor_type,
                            "params": {
                                "path": monitor_conf.get("path"),
                                "threshold_percentage": int(monitor_conf.get("threshold_percentage", 90))
                            }
                        }
                        monitor_instance = DiskMonitor(config_dict, web_logger)
                    elif monitor_type == "network":
                        config_dict = {
                            "name": monitor_name,
                            "type": monitor_type,
                            "params": {
                                "interface_name": monitor_conf.get("interface"),
                                "expected_status": monitor_conf.get("expected_status", "up")
                            }
                        }
                        monitor_instance = NetworkMonitor(config_dict, web_logger)
                    elif monitor_type == "http":
                        config_dict = {
                            "name": monitor_name,
                            "type": monitor_type,
                            "params": {
                                "url": monitor_conf.get("url"),
                                "expected_status_code": int(monitor_conf.get("expected_status", 200)),
                                "timeout_seconds": int(monitor_conf.get("timeout_seconds", 10))
                            }
                        }
                        monitor_instance = HTTPMonitor(config_dict, web_logger)
                    else:
                        continue
                        
                    self.monitors.append(monitor_instance)
                    self.status[monitor_name] = {
                        'healthy': False,
                        'last_check': None,
                        'message': 'Not checked yet'
                    }
                except Exception as e:
                    web_logger.error(f"Failed to initialize monitor {monitor_name}: {e}")
                    
            return True
        except Exception as e:
            web_logger.error(f"Failed to load monitors: {e}")
            return False
    
    def start_monitoring(self):
        """Start the monitoring loop"""
        self.active = True
        while self.active:
            for monitor in self.monitors:
                try:
                    monitor.check_status()
                    self.status[monitor.name] = {
                        'healthy': monitor.is_healthy,
                        'last_check': datetime.now().isoformat(),
                        'message': 'Healthy' if monitor.is_healthy else 'Unhealthy'
                    }
                except Exception as e:
                    self.status[monitor.name] = {
                        'healthy': False,
                        'last_check': datetime.now().isoformat(),
                        'message': f'Error: {str(e)}'
                    }
            time.sleep(30)  # Check every 30 seconds
    
    def stop_monitoring(self):
        """Stop the monitoring loop"""
        self.active = False

# Global monitoring service
monitoring_service = MonitoringService()

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/config')
def config_page():
    """Configuration management page"""
    config = load_config(config_file_path)
    return render_template('config.html', config=config)

@app.route('/api/config', methods=['GET'])
def get_config():
    """API endpoint to get current configuration"""
    try:
        config = load_config(config_file_path)
        return jsonify({'success': True, 'config': config})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/config', methods=['POST'])
def save_config():
    """API endpoint to save configuration"""
    try:
        config_data = request.json
        
        # Validate configuration
        if not config_data or 'monitors' not in config_data:
            return jsonify({'success': False, 'error': 'Invalid configuration format'})
        
        # Save to file
        with open(config_file_path, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False)
        
        # Reload monitors
        monitoring_service.load_monitors()
        
        return jsonify({'success': True, 'message': 'Configuration saved successfully'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/monitors/status', methods=['GET'])
def get_monitor_status():
    """API endpoint to get current monitor status"""
    return jsonify({'success': True, 'status': monitoring_service.status})

@app.route('/api/monitors/start', methods=['POST'])
def start_monitoring():
    """API endpoint to start monitoring"""
    global monitoring_thread
    try:
        if not monitoring_service.active:
            monitoring_service.load_monitors()
            monitoring_thread = threading.Thread(target=monitoring_service.start_monitoring)
            monitoring_thread.daemon = True
            monitoring_thread.start()
            return jsonify({'success': True, 'message': 'Monitoring started'})
        else:
            return jsonify({'success': False, 'error': 'Monitoring already active'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/monitors/stop', methods=['POST'])
def stop_monitoring():
    """API endpoint to stop monitoring"""
    try:
        monitoring_service.stop_monitoring()
        return jsonify({'success': True, 'message': 'Monitoring stopped'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/system/info', methods=['GET'])
def get_system_info():
    """API endpoint to get system information"""
    try:
        # Get available network interfaces
        interfaces = list(psutil.net_if_addrs().keys())
        
        # Get disk information
        disk_partitions = []
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_partitions.append({
                    'device': partition.device,
                    'mountpoint': partition.mountpoint,
                    'fstype': partition.fstype,
                    'total': usage.total,
                    'used': usage.used,
                    'free': usage.free,
                    'percent': usage.percent
                })
            except:
                pass
        
        return jsonify({
            'success': True,
            'system_info': {
                'network_interfaces': interfaces,
                'disk_partitions': disk_partitions,
                'cpu_count': psutil.cpu_count(),
                'memory': dict(psutil.virtual_memory()._asdict())
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    # Ensure config file exists
    if not os.path.exists(config_file_path):
        # Create default config
        default_config = {
            'global_settings': {
                'check_interval_seconds': 30
            },
            'logging': {
                'log_file': 'logs/redundancy_app.log',
                'log_level': 'INFO',
                'log_to_console': True,
                'log_to_file': True
            },
            'monitors': []
        }
        with open(config_file_path, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False)
    
    # Create templates directory if it doesn't exist
    os.makedirs('web_ui/templates', exist_ok=True)
    os.makedirs('web_ui/static', exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
