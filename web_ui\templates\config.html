{% extends "base.html" %}

{% block title %}Configuration - Hardware Redundancy System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="fas fa-cog me-2"></i>
                System Configuration
            </h1>
            <div>
                <button id="addMonitor" class="btn btn-primary me-2">
                    <i class="fas fa-plus me-1"></i>Add Monitor
                </button>
                <button id="saveConfig" class="btn btn-success">
                    <i class="fas fa-save me-1"></i>Save Configuration
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Global Settings -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-globe me-2"></i>
                    Global Settings
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="checkInterval" class="form-label">Check Interval (seconds)</label>
                    <input type="number" class="form-control" id="checkInterval" value="30" min="5" max="3600">
                    <div class="form-text">How often to check monitor status</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    Logging Settings
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="logLevel" class="form-label">Log Level</label>
                    <select class="form-select" id="logLevel">
                        <option value="DEBUG">DEBUG</option>
                        <option value="INFO" selected>INFO</option>
                        <option value="WARNING">WARNING</option>
                        <option value="ERROR">ERROR</option>
                        <option value="CRITICAL">CRITICAL</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="logFile" class="form-label">Log File Path</label>
                    <input type="text" class="form-control" id="logFile" value="logs/redundancy_app.log">
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="logToConsole" checked>
                    <label class="form-check-label" for="logToConsole">Log to Console</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="logToFile" checked>
                    <label class="form-check-label" for="logToFile">Log to File</label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monitors Configuration -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Monitors Configuration
                </h5>
            </div>
            <div class="card-body">
                <div id="monitorsContainer">
                    <!-- Monitors will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Monitor Modal -->
<div class="modal fade" id="addMonitorModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Monitor</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addMonitorForm">
                    <div class="mb-3">
                        <label for="monitorName" class="form-label">Monitor Name</label>
                        <input type="text" class="form-control" id="monitorName" required>
                    </div>
                    <div class="mb-3">
                        <label for="monitorType" class="form-label">Monitor Type</label>
                        <select class="form-select" id="monitorType" required>
                            <option value="">Select Type</option>
                            <option value="disk">Disk Monitor</option>
                            <option value="network">Network Monitor</option>
                            <option value="http">HTTP Monitor</option>
                        </select>
                    </div>
                    
                    <!-- Disk Monitor Fields -->
                    <div id="diskFields" class="monitor-fields" style="display: none;">
                        <div class="mb-3">
                            <label for="diskPath" class="form-label">Disk Path</label>
                            <input type="text" class="form-control" id="diskPath" placeholder="e.g., / or C:\">
                        </div>
                        <div class="mb-3">
                            <label for="diskThreshold" class="form-label">Threshold Percentage</label>
                            <input type="number" class="form-control" id="diskThreshold" min="1" max="100" value="90">
                        </div>
                    </div>
                    
                    <!-- Network Monitor Fields -->
                    <div id="networkFields" class="monitor-fields" style="display: none;">
                        <div class="mb-3">
                            <label for="networkInterface" class="form-label">Network Interface</label>
                            <select class="form-select" id="networkInterface">
                                <option value="">Select Interface</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="networkStatus" class="form-label">Expected Status</label>
                            <select class="form-select" id="networkStatus">
                                <option value="up">Up</option>
                                <option value="down">Down</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- HTTP Monitor Fields -->
                    <div id="httpFields" class="monitor-fields" style="display: none;">
                        <div class="mb-3">
                            <label for="httpUrl" class="form-label">URL</label>
                            <input type="url" class="form-control" id="httpUrl" placeholder="https://example.com/health">
                        </div>
                        <div class="mb-3">
                            <label for="httpStatus" class="form-label">Expected Status Code</label>
                            <input type="number" class="form-control" id="httpStatus" value="200" min="100" max="599">
                        </div>
                        <div class="mb-3">
                            <label for="httpTimeout" class="form-label">Timeout (seconds)</label>
                            <input type="number" class="form-control" id="httpTimeout" value="10" min="1" max="300">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveMonitor">Add Monitor</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentConfig = {};
let availableInterfaces = [];

$(document).ready(function() {
    loadConfiguration();
    loadSystemInfo();
    
    $('#addMonitor').click(function() {
        $('#addMonitorModal').modal('show');
    });
    
    $('#monitorType').change(function() {
        showMonitorFields($(this).val());
    });
    
    $('#saveMonitor').click(function() {
        addNewMonitor();
    });
    
    $('#saveConfig').click(function() {
        saveConfiguration();
    });
});

function loadConfiguration() {
    $.get('/api/config')
        .done(function(response) {
            if (response.success) {
                currentConfig = response.config;
                populateConfigForm();
                displayMonitors();
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to load configuration');
        });
}

function loadSystemInfo() {
    $.get('/api/system/info')
        .done(function(response) {
            if (response.success) {
                availableInterfaces = response.system_info.network_interfaces;
                populateNetworkInterfaces();
            }
        });
}

function populateConfigForm() {
    if (currentConfig.global_settings) {
        $('#checkInterval').val(currentConfig.global_settings.check_interval_seconds || 30);
    }
    
    if (currentConfig.logging) {
        $('#logLevel').val(currentConfig.logging.log_level || 'INFO');
        $('#logFile').val(currentConfig.logging.log_file || 'logs/redundancy_app.log');
        $('#logToConsole').prop('checked', currentConfig.logging.log_to_console !== false);
        $('#logToFile').prop('checked', currentConfig.logging.log_to_file !== false);
    }
}

function populateNetworkInterfaces() {
    let select = $('#networkInterface');
    select.empty().append('<option value="">Select Interface</option>');
    
    availableInterfaces.forEach(function(iface) {
        select.append(`<option value="${iface}">${iface}</option>`);
    });
}

function displayMonitors() {
    let container = $('#monitorsContainer');
    container.empty();
    
    if (!currentConfig.monitors || currentConfig.monitors.length === 0) {
        container.html('<div class="text-center py-4"><p class="text-muted">No monitors configured</p></div>');
        return;
    }
    
    currentConfig.monitors.forEach(function(monitor, index) {
        let monitorHtml = createMonitorCard(monitor, index);
        container.append(monitorHtml);
    });
}

function createMonitorCard(monitor, index) {
    let typeIcon = getMonitorTypeIcon(monitor.type);
    let typeBadge = getMonitorTypeBadge(monitor.type);
    
    let detailsHtml = '';
    if (monitor.type === 'disk') {
        detailsHtml = `<p><strong>Path:</strong> ${monitor.path}</p>
                       <p><strong>Threshold:</strong> ${monitor.threshold_percentage}%</p>`;
    } else if (monitor.type === 'network') {
        detailsHtml = `<p><strong>Interface:</strong> ${monitor.interface}</p>
                       <p><strong>Expected Status:</strong> ${monitor.expected_status || 'up'}</p>`;
    } else if (monitor.type === 'http') {
        detailsHtml = `<p><strong>URL:</strong> ${monitor.url}</p>
                       <p><strong>Expected Status:</strong> ${monitor.expected_status || 200}</p>
                       <p><strong>Timeout:</strong> ${monitor.timeout_seconds || 10}s</p>`;
    }
    
    return `
        <div class="card monitor-card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5 class="card-title">
                            <i class="${typeIcon} me-2"></i>
                            ${monitor.name}
                            ${typeBadge}
                        </h5>
                        <div class="monitor-details">
                            ${detailsHtml}
                        </div>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="editMonitor(${index})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteMonitor(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getMonitorTypeIcon(type) {
    switch(type) {
        case 'disk': return 'fas fa-hdd';
        case 'network': return 'fas fa-network-wired';
        case 'http': return 'fas fa-globe';
        default: return 'fas fa-question';
    }
}

function getMonitorTypeBadge(type) {
    let color = type === 'disk' ? 'primary' : type === 'network' ? 'success' : 'info';
    return `<span class="badge bg-${color} monitor-type-badge">${type.toUpperCase()}</span>`;
}

function showMonitorFields(type) {
    $('.monitor-fields').hide();
    if (type) {
        $(`#${type}Fields`).show();
    }
}

function addNewMonitor() {
    let type = $('#monitorType').val();
    let name = $('#monitorName').val();
    
    if (!type || !name) {
        showAlert('warning', 'Please fill in all required fields');
        return;
    }
    
    let monitor = {
        name: name,
        type: type
    };
    
    if (type === 'disk') {
        monitor.path = $('#diskPath').val();
        monitor.threshold_percentage = parseInt($('#diskThreshold').val());
    } else if (type === 'network') {
        monitor.interface = $('#networkInterface').val();
        monitor.expected_status = $('#networkStatus').val();
    } else if (type === 'http') {
        monitor.url = $('#httpUrl').val();
        monitor.expected_status = parseInt($('#httpStatus').val());
        monitor.timeout_seconds = parseInt($('#httpTimeout').val());
    }
    
    if (!currentConfig.monitors) {
        currentConfig.monitors = [];
    }
    
    currentConfig.monitors.push(monitor);
    displayMonitors();
    
    $('#addMonitorModal').modal('hide');
    $('#addMonitorForm')[0].reset();
    showAlert('success', 'Monitor added successfully');
}

function deleteMonitor(index) {
    if (confirm('Are you sure you want to delete this monitor?')) {
        currentConfig.monitors.splice(index, 1);
        displayMonitors();
        showAlert('success', 'Monitor deleted successfully');
    }
}

function saveConfiguration() {
    // Update config with form values
    currentConfig.global_settings = {
        check_interval_seconds: parseInt($('#checkInterval').val())
    };
    
    currentConfig.logging = {
        log_level: $('#logLevel').val(),
        log_file: $('#logFile').val(),
        log_to_console: $('#logToConsole').is(':checked'),
        log_to_file: $('#logToFile').is(':checked')
    };
    
    $.ajax({
        url: '/api/config',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(currentConfig),
        success: function(response) {
            if (response.success) {
                showAlert('success', 'Configuration saved successfully');
            } else {
                showAlert('danger', 'Failed to save configuration: ' + response.error);
            }
        },
        error: function() {
            showAlert('danger', 'Failed to save configuration');
        }
    });
}

function showAlert(type, message) {
    let alertHtml = `<div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                     </div>`;
    $('.container').prepend(alertHtml);
}
</script>
{% endblock %}
