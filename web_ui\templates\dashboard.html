{% extends "base.html" %}

{% block title %}Dashboard - Hardware Redundancy System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="fas fa-tachometer-alt me-2"></i>
                System Dashboard
            </h1>
            <div>
                <button id="startMonitoring" class="btn btn-success me-2">
                    <i class="fas fa-play me-1"></i>Start Monitoring
                </button>
                <button id="stopMonitoring" class="btn btn-danger me-2">
                    <i class="fas fa-stop me-1"></i>Stop Monitoring
                </button>
                <button id="refreshStatus" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- System Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-server fa-2x text-primary mb-2"></i>
                <h5 class="card-title">Total Monitors</h5>
                <h3 class="text-primary" id="totalMonitors">-</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h5 class="card-title">Healthy</h5>
                <h3 class="text-success" id="healthyMonitors">-</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                <h5 class="card-title">Unhealthy</h5>
                <h3 class="text-warning" id="unhealthyMonitors">-</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                <h5 class="card-title">Last Update</h5>
                <p class="text-info mb-0" id="lastUpdate">-</p>
            </div>
        </div>
    </div>
</div>

<!-- Monitor Status -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Monitor Status
                </h5>
            </div>
            <div class="card-body">
                <div id="monitorStatus">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading monitor status...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-network-wired me-2"></i>
                    Network Interfaces
                </h5>
            </div>
            <div class="card-body">
                <div id="networkInterfaces">
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                        <span class="ms-2">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-hdd me-2"></i>
                    Disk Partitions
                </h5>
            </div>
            <div class="card-body">
                <div id="diskPartitions">
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                        <span class="ms-2">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let monitoringActive = false;
let refreshInterval;

$(document).ready(function() {
    loadSystemInfo();
    loadMonitorStatus();
    
    // Auto-refresh every 30 seconds
    refreshInterval = setInterval(loadMonitorStatus, 30000);
    
    $('#startMonitoring').click(function() {
        startMonitoring();
    });
    
    $('#stopMonitoring').click(function() {
        stopMonitoring();
    });
    
    $('#refreshStatus').click(function() {
        loadMonitorStatus();
        loadSystemInfo();
    });
});

function loadSystemInfo() {
    $.get('/api/system/info')
        .done(function(response) {
            if (response.success) {
                displayNetworkInterfaces(response.system_info.network_interfaces);
                displayDiskPartitions(response.system_info.disk_partitions);
            }
        })
        .fail(function() {
            $('#networkInterfaces').html('<div class="text-danger">Failed to load network interfaces</div>');
            $('#diskPartitions').html('<div class="text-danger">Failed to load disk partitions</div>');
        });
}

function displayNetworkInterfaces(interfaces) {
    let html = '';
    if (interfaces.length === 0) {
        html = '<p class="text-muted">No network interfaces found</p>';
    } else {
        html = '<ul class="list-group list-group-flush">';
        interfaces.forEach(function(iface) {
            html += `<li class="list-group-item d-flex align-items-center">
                        <i class="fas fa-ethernet me-2 text-primary"></i>
                        ${iface}
                     </li>`;
        });
        html += '</ul>';
    }
    $('#networkInterfaces').html(html);
}

function displayDiskPartitions(partitions) {
    let html = '';
    if (partitions.length === 0) {
        html = '<p class="text-muted">No disk partitions found</p>';
    } else {
        partitions.forEach(function(partition) {
            let usageClass = partition.percent > 90 ? 'danger' : partition.percent > 75 ? 'warning' : 'success';
            html += `<div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span><i class="fas fa-hdd me-2"></i>${partition.mountpoint}</span>
                            <span class="text-${usageClass}">${partition.percent.toFixed(1)}%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-${usageClass}" style="width: ${partition.percent}%"></div>
                        </div>
                        <small class="text-muted">
                            ${formatBytes(partition.used)} / ${formatBytes(partition.total)} used
                        </small>
                     </div>`;
        });
    }
    $('#diskPartitions').html(html);
}

function loadMonitorStatus() {
    $.get('/api/monitors/status')
        .done(function(response) {
            if (response.success) {
                displayMonitorStatus(response.status);
                updateOverviewStats(response.status);
            }
        })
        .fail(function() {
            $('#monitorStatus').html('<div class="alert alert-danger">Failed to load monitor status</div>');
        });
}

function displayMonitorStatus(status) {
    let html = '';
    
    if (Object.keys(status).length === 0) {
        html = '<div class="text-center py-4"><p class="text-muted">No monitors configured</p></div>';
    } else {
        html = '<div class="table-responsive"><table class="table table-hover">';
        html += '<thead><tr><th>Monitor</th><th>Status</th><th>Last Check</th><th>Message</th></tr></thead><tbody>';
        
        for (let [name, info] of Object.entries(status)) {
            let statusClass = info.healthy ? 'success' : 'danger';
            let statusIcon = info.healthy ? 'check-circle' : 'exclamation-triangle';
            let lastCheck = info.last_check ? new Date(info.last_check).toLocaleString() : 'Never';
            
            html += `<tr>
                        <td><strong>${name}</strong></td>
                        <td>
                            <span class="status-indicator status-${info.healthy ? 'healthy' : 'unhealthy'}"></span>
                            <span class="text-${statusClass}">
                                <i class="fas fa-${statusIcon} me-1"></i>
                                ${info.healthy ? 'Healthy' : 'Unhealthy'}
                            </span>
                        </td>
                        <td>${lastCheck}</td>
                        <td>${info.message}</td>
                     </tr>`;
        }
        html += '</tbody></table></div>';
    }
    
    $('#monitorStatus').html(html);
}

function updateOverviewStats(status) {
    let total = Object.keys(status).length;
    let healthy = Object.values(status).filter(s => s.healthy).length;
    let unhealthy = total - healthy;
    
    $('#totalMonitors').text(total);
    $('#healthyMonitors').text(healthy);
    $('#unhealthyMonitors').text(unhealthy);
    $('#lastUpdate').text(new Date().toLocaleTimeString());
}

function startMonitoring() {
    $.post('/api/monitors/start')
        .done(function(response) {
            if (response.success) {
                showAlert('success', 'Monitoring started successfully');
                monitoringActive = true;
            } else {
                showAlert('danger', 'Failed to start monitoring: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to start monitoring');
        });
}

function stopMonitoring() {
    $.post('/api/monitors/stop')
        .done(function(response) {
            if (response.success) {
                showAlert('success', 'Monitoring stopped successfully');
                monitoringActive = false;
            } else {
                showAlert('danger', 'Failed to stop monitoring: ' + response.error);
            }
        })
        .fail(function() {
            showAlert('danger', 'Failed to stop monitoring');
        });
}

function showAlert(type, message) {
    let alertHtml = `<div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                     </div>`;
    $('.container').prepend(alertHtml);
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>
{% endblock %}
